
---

### **系统核心业务与财务集成全景图 (最终优化版)**

**图例说明:**
*   **类型/枚举 (`Type/Enum`)**: 定义了业务中固定的分类和选项。
*   **状态机 (`State Machine`)**: 使用Mermaid图展示了单个业务实体（单据）从创建到终结的完整生命周期和状态转换。
*   **集成流程图 (`Integrated Process Flow`)**: 使用Mermaid图展示了跨模块、跨单据的完整业务数据流和触发关系。

---
### **第一部分：采购与应付 (Purchase to Pay)**

本部分描绘了从下达采购订单，到货物入库，再到最终与供应商完成发票匹配和付款核销，并记录资金流出的全过程。

#### **1.1 涉及的核心实体**

*   `erp_purchase_order` (采购订单)
*   `erp_purchase_inbound` (采购入库单)
*   `wms_inbound` (WMS入库执行单)
*   `erp_fin_ap_invoice` (供应商发票)
*   `erp_fin_ap_payment_order` (付款单)
*   `erp_fin_account` (资金账户)
*   `erp_fin_account_ledger` (账户流水)

#### **1.2 核心实体状态机 (State Machines)**

##### **1.2.1 采购订单 (`erp_purchase_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建订单"
    DRAFT --> CONFIRMED: "审核确认"
    CONFIRMED --> PARTIALLY_RECEIVED: "部分到货"
    CONFIRMED --> FULLY_RECEIVED: "全部到货"
    PARTIALLY_RECEIVED --> FULLY_RECEIVED: "剩余到货"
    PARTIALLY_RECEIVED --> CLOSED: "强制关闭"
    FULLY_RECEIVED --> CLOSED: "财务关闭"
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    CONFIRMED --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    CLOSED --> [*]
    CANCELLED --> [*]
```

##### **1.2.2 采购入库单 (`erp_purchase_inbound`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建入库单"
    DRAFT --> PENDING_WAREHOUSE: "提交"
    PENDING_WAREHOUSE --> COMPLETED: "仓库执行完毕"
    COMPLETED --> [*]
```

##### **1.2.3 供应商发票 (`erp_fin_ap_invoice`)**
```mermaid
stateDiagram-v2
    [*] --> UNMATCHED: "发票录入"
    UNMATCHED --> PARTIALLY_MATCHED: "与入库单部分匹配"
    PARTIALLY_MATCHED --> FULLY_MATCHED: "与入库单完全匹配"
    UNMATCHED --> FULLY_MATCHED: "直接完全匹配"
    FULLY_MATCHED --> PAID: "付款完成"
    PAID --> [*]
```

##### **1.2.4 付款单 (`erp_fin_ap_payment_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建付款申请"
    DRAFT --> PENDING_APPROVAL: "提交审批"
    PENDING_APPROVAL --> DRAFT: "驳回"
    PENDING_APPROVAL --> APPROVED: "审批通过"
    APPROVED --> PARTIALLY_APPLIED: "部分核销"
    APPROVED --> FULLY_APPLIED: "全部核销"
    PARTIALLY_APPLIED --> FULLY_APPLIED: "剩余核销"

    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    PENDING_APPROVAL --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    FULLY_APPLIED --> [*]
    CANCELLED --> [*]
```

#### **1.3 采购到应付集成流程图 (Integrated Process Flow)**

```mermaid
graph TD
    subgraph "第一阶段: 采购与收货"
        A["<b>erp_purchase_order</b><br>DRAFT"] -- "审核" --> B["<b>erp_purchase_order</b><br>CONFIRMED"];
        B -- "供应商到货" --> C{"创建 <b>erp_purchase_inbound</b>"};
        C --> D["<b>erp_purchase_inbound</b><br>DRAFT"];
        D -- "提交" --> G["<b>erp_purchase_inbound</b><br>PENDING_WAREHOUSE"];
    end

    subgraph "第二阶段: 仓库执行与状态回传"
        G -- "推送入库指令" --> H["创建 <b>wms_inbound</b><br>PENDING_RECEIPT"];
        H -- "仓库收货上架" --> I["<b>wms_inbound</b><br>COMPLETED"];
        I -- "回传入库结果" --> J{"更新 <b>erp_purchase_inbound</b>"};
        J --> K["<b>erp_purchase_inbound</b><br>COMPLETED"];
        K -- "更新采购订单收货数量" --> L["<b>erp_purchase_order</b><br>PARTIALLY/FULLY_RECEIVED"];
    end

    subgraph "第三阶段: 财务发票处理与匹配"
        M["供应商提供纸质/电子发票"] --> N{"财务录入 <b>erp_fin_ap_invoice</b>"};
        N --> O["<b>erp_fin_ap_invoice</b><br>UNMATCHED"];
        O -- "财务在工作台进行匹配" --> P{"<b>三单匹配</b><br><i>(由 IThreeWayMatchService 实现)</i><br>匹配 <b>erp_fin_ap_invoice</b><br>与<br><b>erp_purchase_inbound</b>"};
        P -- "匹配成功" --> Q["<b>erp_fin_ap_invoice</b><br>FULLY_MATCHED"];
    end
    
    subgraph "第四阶段: 付款与核销"
        Q -- "纳入付款计划" --> R{"创建 <b>erp_fin_ap_payment_order</b>"};
        R --> S["<b>erp_fin_ap_payment_order</b><br>DRAFT"];
        S -- "审批流程" --> T["<b>erp_fin_ap_payment_order</b><br>APPROVED"];
        T -- "出纳付款并核销" --> U{"将付款单核销到发票"};
        U -- "更新发票状态" --> V["<b>erp_fin_ap_invoice</b><br>PAID"];
        U -- "更新付款单状态" --> W["<b>erp_fin_ap_payment_order</b><br>PARTIALLY/FULLY_APPLIED"];
    end
    
    subgraph "第五阶段: 资金账户变动 (最终闭环)"
        X["<b>erp_fin_account</b><br>(公司银行账户)"]
        Y["<b>erp_fin_account_ledger</b><br>(账户流水)"]
        W -- "触发" --> Z{"创建<b>账户流水</b>记录<br>direction: EXPENSE"};
        Z --> Y;
        Z -- "更新" --> X["<b>erp_fin_account</b><br>(余额减少)"];
    end

```

---
### **第二部分：销售与应收 (Order to Cash)**

本部分描绘了从接收客户订单，到发货出库，再到最终向客户开具应收单、接收回款并记录资金流入的全过程。

#### **2.1 涉及的核心实体**

*   `erp_sale_order` (销售订单)
*   `erp_sale_outbound` (销售出库单)
*   `wms_outbound` (WMS出库执行单)
*   `erp_fin_ar_receivable` (应收单据)
*   `erp_fin_ar_receipt_order` (客户收款单)
*   `erp_fin_account` (资金账户)
*   `erp_fin_account_ledger` (账户流水)

#### **2.2 核心实体状态机 (State Machines)**

##### **2.2.1 销售订单 (`erp_sale_order`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建订单"
    DRAFT --> CONFIRMED: "审核确认"
    
    state fork_status <<fork>>
    CONFIRMED --> fork_status
    fork_status --> ON_HOLD: "挂起"
    ON_HOLD --> CONFIRMED: "解除挂起"
    fork_status --> READY_TO_SHIP: "库存满足(待发货)"
    
    READY_TO_SHIP --> PARTIALLY_SHIPPED: "部分发货"
    PARTIALLY_SHIPPED --> FULLY_SHIPPED: "剩余发货"
    READY_TO_SHIP --> FULLY_SHIPPED: "全部发货"
    FULLY_SHIPPED --> CLOSED: "财务关闭"
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    CONFIRMED --> fork_cancel
    ON_HOLD --> fork_cancel
    fork_cancel --> CANCELLED: "取消"
    
    CLOSED --> [*]
    CANCELLED --> [*]
```

##### **2.2.2 应收单据 (`erp_fin_ar_receivable`)**
```mermaid
stateDiagram-v2
    [*] --> UNPAID: "开票/发货生成"
    UNPAID --> PARTIALLY_PAID: "收到部分付款"
    PARTIALLY_PAID --> FULLY_PAID: "收到全部付款"
    UNPAID --> FULLY_PAID: "一次性付清"
    
    state fork_overdue <<fork>>
    UNPAID --> fork_overdue
    PARTIALLY_PAID --> fork_overdue
    fork_overdue --> OVERDUE: "超出到期日"
    OVERDUE --> PARTIALLY_PAID: "逾期后部分付款"
    OVERDUE --> FULLY_PAID: "逾期后付清"
    
    FULLY_PAID --> [*]
```

##### **2.2.3 客户收款单 (`erp_fin_ar_receipt_order`)**
```mermaid
stateDiagram-v2
    [*] --> UNAPPLIED: "录入客户付款"
    UNAPPLIED --> PARTIALLY_APPLIED: "部分核销"
    PARTIALLY_APPLIED --> FULLY_APPLIED: "完全核销"
    UNAPPLIED --> FULLY_APPLIED: "直接完全核销"
    FULLY_APPLIED --> [*]
```

#### **2.3 销售到应收集成流程图 (Integrated Process Flow)**

```mermaid
graph TD
    subgraph "第一阶段: 销售与发货"
        A["<b>erp_sale_order</b><br>DRAFT"] -- "审核" --> B["<b>erp_sale_order</b><br>CONFIRMED"];
        B -- "备货完成/通知发货" --> C{"创建 <b>erp_sale_outbound</b>"};
        C --> D["<b>erp_sale_outbound</b><br>PENDING_WAREHOUSE"];
    end

    subgraph "第二阶段: 仓库执行与状态回传"
        D -- "推送出库指令" --> E["创建 <b>wms_outbound</b><br>PENDING_PICKING"];
        E -- "仓库拣货/复核/发运" --> F["<b>wms_outbound</b><br>SHIPPED"];
        F -- "回传出库结果" --> G{"更新 <b>erp_sale_outbound</b>"};
        G --> H["<b>erp_sale_outbound</b><br>COMPLETED"];
        H -- "更新销售订单发货数量" --> I["<b>erp_sale_order</b><br>PARTIALLY/FULLY_SHIPPED"];
    end

    subgraph "第三阶段: 财务开票与应收确认"
        H -- "发货完成，触发开票" --> J{"创建 <b>erp_fin_ar_receivable</b>"};
        J --> K["<b>erp_fin_ar_receivable</b><br>UNPAID"];
    end

    subgraph "第四阶段: 收款与核销"
        L["客户付款"] --> M{"财务录入 <b>erp_fin_ar_receipt_order</b>"};
        M --> N["<b>erp_fin_ar_receipt_order</b><br>UNAPPLIED"];
        N -- "财务在工作台进行核销" --> O{"将收款核销到应收单"};
        O -- "更新应收单状态" --> P["<b>erp_fin_ar_receivable</b><br>PARTIALLY_PAID / FULLY_PAID"];
        O -- "更新收款单状态" --> Q["<b>erp_fin_ar_receipt_order</b><br>PARTIALLY_APPLIED / FULLY_APPLIED"];
    end

    subgraph "第五阶段: 资金账户变动 (最终闭环)"
        R["<b>erp_fin_account</b><br>(公司银行账户)"]
        S["<b>erp_fin_account_ledger</b><br>(账户流水)"]
        Q -- "触发" --> T{"创建<b>账户流水</b>记录<br>direction: INCOME"};
        T --> S;
        T -- "更新" --> R["<b>erp_fin_account</b><br>(余额增加)"];
    end

```

---
### **第三部分：管理费用与应付 (Expense to Pay)**

本部分描绘了企业日常管理费用的发生、审批、付款与核销的全过程，例如员工报销、行政采购等。此流程与采购流程分离，但共享统一的付款平台。

#### **3.1 涉及的核心实体**

*   `erp_fin_expense_invoice` (管理费用发票)
*   `erp_fin_expense_invoice_item` (管理费用发票明细)
*   `erp_fin_ap_payment_order` (付款单)
*   `erp_fin_expense_payment_link` (费用与付款核销关系)
*   `erp_fin_account` (资金账户)
*   `erp_fin_account_ledger` (账户流水)

#### **3.2 核心实体状态机 (`erp_fin_expense_invoice`)**
```mermaid
stateDiagram-v2
    [*] --> DRAFT: "创建费用单"
    DRAFT --> SUBMITTED: "提交"
    SUBMITTED --> APPROVED: "审批通过"
    SUBMITTED --> REJECTED: "审批驳回"
    REJECTED --> DRAFT: "修改"
    
    state fork_cancel <<fork>>
    DRAFT --> fork_cancel
    SUBMITTED --> fork_cancel
    REJECTED --> fork_cancel
    fork_cancel --> CANCELLED: "作废"
    
    APPROVED --> PARTIALLY_PAID: "部分付款"
    APPROVED --> PAID: "全额付款"
    PARTIALLY_PAID --> PAID: "付清余款"
    PARTIALLY_PAID --> PARTIALLY_PAID: "继续部分付款"

    PAID --> [*]
    CANCELLED --> [*]
```

#### **3.3 管理费用到应付集成流程图 (Integrated Process Flow)**

```mermaid
graph TD
    subgraph "第一阶段: 费用录入与审批"
        A[/"<b>申请人/经办人</b><br>创建费用单"/] --> B["<b>erp_fin_expense_invoice</b><br>DRAFT"];
        B -- "提交" --> C["<b>erp_fin_expense_invoice</b><br>SUBMITTED"];
        C -- "<b>审批人</b> 审批" --> D{"审批决策"};
        D -- "通过" --> E["<b>erp_fin_expense_invoice</b><br>APPROVED"];
        D -- "驳回" --> F["<b>erp_fin_expense_invoice</b><br>REJECTED"];
        F --> B;
    end

    subgraph "第二阶段: 财务付款与核销"
        E -- "纳入付款计划" --> G{"创建 <b>erp_fin_ap_payment_order</b>"};
        G --> H["<b>erp_fin_ap_payment_order</b><br>DRAFT"];
        H -- "审批流程" --> I["<b>erp_fin_ap_payment_order</b><br>APPROVED"];
        I -- "<b>财务</b> 执行付款并核销" --> J{"关联 <b>erp_fin_expense_payment_link</b>"};
        J -- "更新费用单状态" --> K["<b>erp_fin_expense_invoice</b><br>PAID/PARTIALLY_PAID"];
        J -- "更新付款单状态" --> L["<b>erp_fin_ap_payment_order</b><br>PARTIALLY/FULLY_APPLIED"];
    end

    subgraph "第三阶段: 资金账户变动 (最终闭环)"
        M["<b>erp_fin_account</b><br>(公司银行账户)"]
        N["<b>erp_fin_account_ledger</b><br>(账户流水)"]
        L -- "触发" --> O{"创建<b>账户流水</b>记录<br>direction: EXPENSE"};
        O --> N;
        O -- "更新" --> M["<b>erp_fin_account</b><br>(余额减少)"];
    end
 
```
---
### **第四部分：账户收支与资金管理 (Cash Management)**

本部分是所有财务流程的终点，详细说明了所有业务付款和收款如何最终体现为公司资金账户的实际变动，并形成可追溯的会计流水。

#### **4.1 涉及的核心实体**

*   `erp_fin_account` (资金账户)
*   `erp_fin_account_ledger` (账户流水)

#### **4.2 核心类型与枚举 (Enums)**

*   **`erp_fin_account.account_type` (账户类型)**
    *   `BANK_CASH`: 银行存款
    *   `CASH`: 库存现金
    *   `THIRD_PARTY`: 第三方支付 (如支付宝)
    *   `VIRTUAL`: 虚拟账户
*   **`erp_fin_account_ledger.direction` (资金方向)**
    *   `INCOME`: 收入
    *   `EXPENSE`: 支出
*   **`erp_fin_account_ledger.transaction_type` (交易类型)**
    *   `SALES_RECEIPT`: 销售收款
    *   `PURCHASE_PAYMENT`: 采购付款
    *   `EXPENSE_REIMBURSEMENT`: 费用报销
    *   `OTHER`: 其他 (如银行手续费、利息等)

#### **4.3 核心实体状态机 (`erp_fin_account`)**

```mermaid
stateDiagram-v2
    [*] --> ACTIVE: "开户"
    ACTIVE --> FROZEN: "司法/风控冻结"
    FROZEN --> ACTIVE: "解冻"
    ACTIVE --> CLOSED: "销户"
    CLOSED --> [*]
```

#### **4.4 账户收支集成流程图 (Integrated Process Flow)**

```mermaid
graph TD
    subgraph "来源1: 销售收款 (Order to Cash)"
        A["<b>erp_fin_ar_receipt_order</b><br>UNAPPLIED"] -- "财务核销" --> B["<b>erp_fin_ar_receipt_order</b><br>FULLY_APPLIED"];
    end
    
    subgraph "来源2: 采购/费用付款 (Purchase/Expense to Pay)"
        C["<b>erp_fin_ap_payment_order</b><br>APPROVED"] -- "财务付款" --> D["<b>erp_fin_ap_payment_order</b><br>FULLY_APPLIED"];
    end
    
    subgraph "来源3: 其他收支"
        E[/"<b>财务人员</b><br>手工录入"/] --> F{"其他收支<br>(如: 银行手续费、利息)"};
    end
    
    subgraph "核心处理: 账户与流水"
        direction LR
        G{"<b>创建账户流水</b><br><i>(erp_fin_account_ledger)</i>"}
        H["<b>erp_fin_account</b><br>(更新余额)"]
        I["<b>erp_fin_account_ledger</b><br>(新增流水记录)"]
        
        G -- "1. 更新账户余额" --> H
        G -- "2. 记录详细流水" --> I
    end

    B -- "direction: INCOME" --> G
    D -- "direction: EXPENSE" --> G
    F -- "direction: INCOME/EXPENSE" --> G
    
 
```
