
主要调整如下：
1.  **移除模块**：完全删除了 “**五、计划与排程 (APS Module)**” 和 “**六、质量管理 (QMS Module)**” 两个章节。
2.  **简化流程**：
    *   **采购入库 (`erp_purchase_inbound`)**：移除了 `PENDING_INSPECTION` (待检验) 状态，流程简化为直接提交给仓库。
    *   **生产订单 (`mes_production_order`)**：移除了 `PENDING_FQC` (待完工检验) 状态，完工后直接进入关闭/核算阶段。
    *   **销售出库 (`wms_outbound`)**：移除了 `PENDING_OQC` (待出货检验) 状态，拣货完成后直接进入发运流程。
    *   **产品实例 (`pro_instance`)**：移除了 `PENDING_INSPECTION` (待检验) 状态，完工后直接变为在库状态。
3.  **统一命名**：将财务模块的单据名称与您的 DDL 保持一致（例如，`erp_ap_invoice` 调整为 `erp_fin_ap_invoice`）。

以下是为您整理好的最终版状态列表。

---

### **系统核心单据状态定义 (最终修订版)**

### **一、 基础数据 (Master Data)**

*   **pro_bom (BOM物料清单)**: `DRAFT` (草稿) ↔ `ACTIVE` (激活) → `ARCHIVED` (归档)
    *   **说明**: `ACTIVE` 与 `DRAFT` 状态之间可以相互转换，以支持BOM的修订和版本管理。

*   **pro_routing (工艺路线)**: `DRAFT` (草稿) ↔ `ACTIVE` (激活) → `ARCHIVED` (归档)
    *   **说明**: `ACTIVE` 与 `DRAFT` 状态之间可以相互转换，以支持工艺路线的修订。

### **二、 核心业务流程 (Core Business Flows)**

#### **采购流程 (Purchase Flow)**
*   **erp_purchase_order (采购订单)**: `DRAFT` → `CONFIRMED` → `PARTIALLY_RECEIVED` / `FULLY_RECEIVED` → `CLOSED`
    *   **可进入**: `CANCELLED` (已取消)

*   **erp_purchase_inbound (采购入库单)**: `DRAFT` → `PENDING_WAREHOUSE` (待入库) → `COMPLETED` (已入库)
    *   **说明**: 流程已简化，无需质检环节。

*   **erp_purchase_return (采购退货单)**: `DRAFT` → `PENDING_WAREHOUSE` (待出库) → `COMPLETED` (已退货)

#### **销售流程 (Sale Flow)**
*   **erp_sale_order (销售订单)**: `DRAFT` → `CONFIRMED` → (`PENDING_PRODUCTION` → `IN_PRODUCTION`) / `READY_TO_SHIP` → `PARTIALLY_SHIPPED` / `FULLY_SHIPPED` → `CLOSED`
    *   **可进入**: `ON_HOLD` (挂起), `CANCELLED` (已取消)

*   **erp_sale_outbound (销售出库单)**: `DRAFT` → `PENDING_WAREHOUSE` (待出库) → `COMPLETED` (已出库)

*   **erp_sale_return (销售退货单)**: `DRAFT` → `AWAITING_RETURN` (待客户退回) → `PENDING_WAREHOUSE` (待入库) → `COMPLETED` (已入库)

#### **生产流程 (MES Flow)**
*   **mes_instance_manager (工作总单/项目单)**: `PLANNING` (计划中) → `IN_PROGRESS` (执行中) → `COMPLETED` (已完成)
    *   **可进入**: `ON_HOLD` (挂起), `CANCELLED` (已取消)

*   **mes_production_order (生产订单)**: `DRAFT` → `RELEASED` (已下达) → `IN_PROGRESS` (生产中) → `PARTIALLY_COMPLETED` / `COMPLETED` → `CLOSED` (已关闭)
    *   **说明**: 流程已简化，生产完工后直接进入关闭状态。

*   **mes_production_issue (生产领料单)**: `DRAFT` → `PENDING_WAREHOUSE` (待出库) → `COMPLETED` (已领料)

*   **mes_production_inbound (生产入库单)**: `DRAFT` → `PENDING_WAREHOUSE` (待入库) → `COMPLETED` (已入库)

*   **mes_production_return (生产退料单)**: `DRAFT` → `PENDING_WAREHOUSE` (待入库) → `COMPLETED` (已入库)

### **三、 仓库与库存 (WMS & Inventory)**

#### **仓库执行 (WMS Execution)**
*   **wms_inbound (仓库入库执行单)**: `PENDING_RECEIPT` (待收货) → `PARTIALLY_RECEIVED` (部分收货) → `COMPLETED` (已完成)
    *   **可进入**: `CANCELLED` (已取消)

*   **wms_outbound (仓库出库执行单)**: `PENDING_PICKING` (待拣货) → `PICKING_IN_PROGRESS` (拣货中) → `PICKED` (已拣货) → `SHIPPED` (已发运)
    *   **说明**: 流程已简化，拣货完成后即可发运。
    *   **可进入**: `CANCELLED` (已取消)

*   **wms_transfer (仓库移库执行单)**: `PENDING` (待执行) → `IN_PROGRESS` (执行中) → `COMPLETED` (已完成)

#### **库存盘点 (Inventory Check)**
*   **wms_inventory_check (盘点任务)**: `DRAFT` → `CONFIRMED` (已确认/快照) → `IN_PROGRESS` (盘点中) → `PENDING_APPROVAL` (待审批) → `COMPLETED` (已完成)
    *   **可进入**: `CANCELLED` (已取消)

*   **wms_inventory_check_item (盘点明细)**: `PENDING_COUNT` (待盘点) → `COUNTED` (已盘点) → (`RECOUNTED` (已复盘)) → `APPROVED` (已审批) → `ADJUSTED` (已调整)

### **四、 追溯与库存实体 (Entities)**

*   **pro_instance (产品实例)**: `IN_PRODUCTION` (生产中) → `COMPLETED` (已完工) → `IN_STOCK` (在库) → `SHIPPED` (已发货)
    *   **可进入**: `SCRAPPED` (已报废)

*   **wms_inventory_batch (库存)**: `AVAILABLE` (可用), `ON_HOLD` (冻结), `IN_TRANSIT` (在途), `EXPIRED` (已过期)
    *   **说明**: 这是状态属性，而非线性流程。各状态间可相互转换，例如：`AVAILABLE` ↔ `ON_HOLD`。

### **五、 财务对账 (Finance Module)**

*   **erp_fin_ap_invoice (供应商发票)**: `UNMATCHED` (未匹配) → `PARTIALLY_MATCHED` (部分匹配) → `FULLY_MATCHED` (完全匹配) → `PAID` (已付款)

*   **erp_fin_ap_payment_order (付款单)**: `DRAFT` (草稿) → `PENDING_APPROVAL` (待审批) → `APPROVED` (已批准) → `PARTIALLY_APPLIED` / `FULLY_APPLIED` (已核销)
    *   **可进入**: `CANCELLED` (已取消)

*   **erp_fin_statement (对账单 - 通用)**: `DRAFT` (草稿) → `PENDING_CONFIRMATION` (待对方确认) → `CONFIRMED` (已确认) → `SETTLED` (已结算)
    *   **说明**: 此为通用对账单流程，适用于AP和AR。

*   **erp_fin_ar_receivable (应收单据)**: `UNPAID` (未付款) → `PARTIALLY_PAID` (部分付款) → `FULLY_PAID` (已付清)
    *   **可进入**: `OVERDUE` (逾期)

*   **erp_fin_ar_receipt_order (客户收款单)**: `UNAPPLIED` (未核销) → `PARTIALLY_APPLIED` (部分核销) → `FULLY_APPLIED` (完全核销)
