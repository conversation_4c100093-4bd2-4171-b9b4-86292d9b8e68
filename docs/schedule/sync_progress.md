# 文档与代码双向同步进度报告

## 状态标识符
- ✅ **已同步**: 文档与代码完全一致。
- ⚠️ **部分同步**: 存在差异，已在代码中添加 TODO 或更新文档，但需要进一步复核。
- ❌ **未同步**: 存在严重偏差，核心逻辑缺失，已在代码中添加 TODO。
- 🔄 **进行中**: 正在处理中。

---

## 第一阶段：采购到付款 (Purchase-to-Pay) 流程 🔄

| 文件路径 | 检查点 | 同步状态 | 备注 |
| :--- | :--- | :--- | :--- |
| `erp/service/impl/PurchaseOrderServiceImpl.java` | 确认订单后，触发入库流程 | ⚠️ | 已添加 `TODO` 注释，建议调用 `PurchaseInboundService` |
| `erp/service/impl/PurchaseInboundServiceImpl.java`| 提交入库单后，触发WMS任务 | ⚠️ | 已在 `confirmInbound` 方法中添加 `TODO` 注释，建议调用 `WmsInboundService` |
| `wms/service/impl/InboundServiceImpl.java` | WMS入库完成后，回调ERP | ⚠️ | 已在 `completeInbound` 方法中添加 `TODO` 注释，建议回调 `PurchaseInboundService` |
| `erp/service/impl/FinApInvoiceServiceImpl.java` | 三单匹配逻辑 | ⚠️ | 服务 (`IThreeWayMatchService`) 已定义但未实现。已在 `auditInvoice` 方法中添加 `TODO` 注释建议调用。 |
| `erp/service/impl/FinApPaymentOrderServiceImpl.java`| 付款核销逻辑 | ⚠️ | 已添加 `applyPaymentToInvoices` 方法框架及 `TODO` 注释。 |
| `erp/service/impl/ThreeWayMatchServiceImpl.java` | 三单匹配核心实现 | ⚠️ | 已增强方法内的 `TODO` 注释，提供更清晰的实现指引。 |
