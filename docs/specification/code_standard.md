# 代码开发与审核规范 🎯

## 分层架构与模块约束
```mermaid
graph LR
Client[客户端层] --> Gateway[网关层]
Gateway --> Application[应用层]
Application --> Framework[框架层]
Framework --> Data[数据层]
end
```

## Service层标准模板
```mermaid
sequenceDiagram
    participant Caller
    participant Service
    participant DB
    
    Caller->>Service: 调用业务方法
    Service->>Service: 开启事务
    Service->>DB: 执行数据库操作
    DB-->>Service: 返回结果
    Service-->>Caller: 返回Boolean
    Service->>Service: 提交事务
    Service-->>Service: 异常处理
end
```

## 枚举实现标准
```mermaid
classDiagram
    class IDictEnum {
        <<interface>>
        +int getValue()
        +String getDictName()
        +String getDictDesc()
    }
    
    class OrderStatus {
        -int value
        -String dictName
        -String dictDesc
        +getValue()
        +getDictName()
        +getDictDesc()
    }
    
    IDictEnum <|.. OrderStatus
    OrderStatus : 实现IDictEnum接口
end
```