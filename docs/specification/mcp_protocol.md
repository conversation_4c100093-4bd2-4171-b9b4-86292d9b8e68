# MCP交互协议手册 🎯

## 协议规范说明

### MCP-1: Sequential Thinking Plan

**调用指令:** 在开始任何新任务时，必须生成一个 `Sequential Thinking Plan`。

**输出格式:** 严格按照以下 Markdown 格式展示规划：

```markdown
[MCP-1: Sequential Thinking Plan]

**1. 🎯 目标 (Objective):**
   - **核心任务:** [简述任务的核心目标]
   - **成功标准:** [定义任务完成的具体衡量标准]

**2. 📚 上下文分析 (Context Analysis):**
   - **信息源:** [列出需要参考的关键文件]
   - **关键约束:** [提及此任务中最需要注意的边界或限制]

**3. 📝 行动计划 (Action Plan):**
   - [ ] **步骤 1:** [描述第一个具体操作]
   - [ ] **步骤 2:** [描述第二个具体操作]
   - [ ] **步骤 3:** [描述第三个具体操作]
   - ... (根据任务复杂性增减步骤)

**4. ⚠️ 风险评估 (Risk Assessment):**
   - **潜在风险:** [列出此计划可能引入的风险]
   - **应对策略:** [提出规避或缓解风险的方案]
```

### MCP-2: Context-7 (情景意识自检协议)

**调用指令:** 此协议是执行MCP-1之前的内部自检清单。在生成MCP-1规划前必须完成此项自检。

## 协议流程图示

### 自检维度图示
```mermaid
graph TD
A[Context-7] --> B[C1: 长期使命]
A --> C[C2: 当前任务]
A --> D[C3: 行为边界]
A --> E[C4: 知识资产]
A --> F[C5: 质量标准]
A --> G[C6: 交互协议]
A --> H[C7: 动态记忆]
end
```

### 自检流程图示
```mermaid
flowchart LR
    A[开始自检] --> B{是否明确长期使命?}
    B -->|是| C{是否理解当前任务?}
    C -->|是| D{是否清楚行为边界?}
    D -->|是| E{知识资产完整?}
    E -->|是| F{质量标准清晰?}
    F -->|是| G{交互协议明确?}
    G -->|是| H{动态记忆更新?}
    H -->|是| I[通过自检]
    I --> J[开始任务]
end
```