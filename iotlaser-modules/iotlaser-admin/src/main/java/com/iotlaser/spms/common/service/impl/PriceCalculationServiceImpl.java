package com.iotlaser.spms.common.service.impl;

import com.iotlaser.spms.common.domain.bo.PriceCalculationResult;
import com.iotlaser.spms.common.service.IPriceCalculationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 价格计算服务实现类
 * 提供统一的价格计算功能
 *
 * <AUTHOR> Kai
 * @date 2025/06/24
 */
@Slf4j
@Service
public class PriceCalculationServiceImpl implements IPriceCalculationService {

    /**
     * 默认精度：4位小数
     */
    private static final int DEFAULT_SCALE = 4;

    /**
     * 默认舍入模式：四舍五入
     */
    private static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 计算
     *
     * @param price    单价(含税)
     * @param taxRate  税率
     * @param quantity 数量
     * @return 集合
     */
    @Override
    public PriceCalculationResult calculateFromInclusivePrice(BigDecimal price, BigDecimal taxRate, BigDecimal quantity) {
        BigDecimal priceExclusiveTax = calculatePriceExclusiveTax(price, taxRate);
        BigDecimal amount = calculateAmount(quantity, price);
        BigDecimal amountExclusiveTax = calculateAmountExclusiveTax(quantity, priceExclusiveTax);
        BigDecimal taxAmount = calculateTaxAmount(amountExclusiveTax, taxRate);

        return PriceCalculationResult.builder()
            .price(price)
            .taxRate(taxRate)
            .quantity(quantity)
            .amount(amount)
            .amountExclusiveTax(amountExclusiveTax)
            .taxAmount(taxAmount)
            .priceExclusiveTax(priceExclusiveTax)
            .build();
    }

    /**
     * 计算含税金额
     *
     * @param quantity 数量
     * @param price    单价(含税)
     * @return 含税金额
     */
    @Override
    public BigDecimal calculateAmount(BigDecimal quantity, BigDecimal price) {
        if (quantity == null || price == null) {
            return BigDecimal.ZERO;
        }
        return quantity.multiply(price).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 计算不含税金额
     *
     * @param quantity          数量
     * @param priceExclusiveTax 单价(不含税)
     * @return 不含税金额
     */
    @Override
    public BigDecimal calculateAmountExclusiveTax(BigDecimal quantity, BigDecimal priceExclusiveTax) {
        if (quantity == null || priceExclusiveTax == null) {
            return BigDecimal.ZERO;
        }
        return quantity.multiply(priceExclusiveTax).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 计算税额
     *
     * @param amountExclusiveTax 不含税金额
     * @param taxRate            税率
     * @return 税额
     */
    @Override
    public BigDecimal calculateTaxAmount(BigDecimal amountExclusiveTax, BigDecimal taxRate) {
        if (amountExclusiveTax == null || taxRate == null) {
            return BigDecimal.ZERO;
        }
        return amountExclusiveTax.multiply(taxRate).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 根据含税单价和税率计算不含税单价
     *
     * @param price   含税单价
     * @param taxRate 税率
     * @return 不含税单价
     */
    @Override
    public BigDecimal calculatePriceExclusiveTax(BigDecimal price, BigDecimal taxRate) {
        if (price == null || taxRate == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal divisor = BigDecimal.ONE.add(taxRate);
        return price.divide(divisor, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 根据不含税单价和税率计算含税单价
     *
     * @param priceExclusiveTax 不含税单价
     * @param taxRate           税率
     * @return 含税单价
     */
    @Override
    public BigDecimal calculatePrice(BigDecimal priceExclusiveTax, BigDecimal taxRate) {
        if (priceExclusiveTax == null || taxRate == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal multiplier = BigDecimal.ONE.add(taxRate);
        return priceExclusiveTax.multiply(multiplier).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 校验价格计算的一致性
     *
     * @param quantity           数量
     * @param price              含税单价
     * @param priceExclusiveTax  不含税单价
     * @param amount             含税金额
     * @param amountExclusiveTax 不含税金额
     * @param taxRate            税率
     * @param taxAmount          税额
     * @return 是否一致
     */
    @Override
    public boolean validatePriceConsistency(BigDecimal quantity, BigDecimal price, BigDecimal priceExclusiveTax,
                                            BigDecimal amount, BigDecimal amountExclusiveTax,
                                            BigDecimal taxRate, BigDecimal taxAmount) {
        try {
            // 计算期望值
            BigDecimal expectedAmount = calculateAmount(quantity, price);
            BigDecimal expectedAmountExclusiveTax = calculateAmountExclusiveTax(quantity, priceExclusiveTax);
            BigDecimal expectedTaxAmount = calculateTaxAmount(amountExclusiveTax, taxRate);
            BigDecimal expectedPrice = calculatePrice(priceExclusiveTax, taxRate);

            // 允许的误差范围（0.01）
            BigDecimal tolerance = new BigDecimal("0.01");

            // 检查各项计算是否一致
            boolean amountConsistent = isWithinTolerance(amount, expectedAmount, tolerance);
            boolean amountExclusiveTaxConsistent = isWithinTolerance(amountExclusiveTax, expectedAmountExclusiveTax, tolerance);
            boolean taxAmountConsistent = isWithinTolerance(taxAmount, expectedTaxAmount, tolerance);
            boolean priceConsistent = isWithinTolerance(price, expectedPrice, tolerance);

            if (!amountConsistent || !amountExclusiveTaxConsistent || !taxAmountConsistent || !priceConsistent) {
                log.warn("价格计算不一致 - 含税金额: {} vs {}, 不含税金额: {} vs {}, 税额: {} vs {}, 含税单价: {} vs {}",
                    amount, expectedAmount, amountExclusiveTax, expectedAmountExclusiveTax,
                    taxAmount, expectedTaxAmount, price, expectedPrice);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("价格一致性校验失败：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查两个数值是否在容差范围内
     */
    private boolean isWithinTolerance(BigDecimal actual, BigDecimal expected, BigDecimal tolerance) {
        if (actual == null && expected == null) {
            return true;
        }
        if (actual == null || expected == null) {
            return false;
        }
        BigDecimal difference = actual.subtract(expected).abs();
        return difference.compareTo(tolerance) <= 0;
    }
}
