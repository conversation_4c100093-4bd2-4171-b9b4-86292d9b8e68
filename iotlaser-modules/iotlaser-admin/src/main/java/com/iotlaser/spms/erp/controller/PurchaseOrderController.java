package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.PurchaseOrderBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import com.iotlaser.spms.erp.service.IPurchaseOrderService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购订单控制器
 * 提供采购订单的完整生命周期管理，包括订单创建、审批流程、入库单生成、应付单生成等核心业务功能
 *
 * <AUTHOR> Kai
 * @date 2025-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/purchaseOrder")
public class PurchaseOrderController extends BaseController {

    private final IPurchaseOrderService purchaseOrderService;

    @SaCheckPermission("erp:purchaseOrder:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseOrderVo> list(PurchaseOrderBo bo, PageQuery pageQuery) {
        return purchaseOrderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出采购订单列表
     */
    @SaCheckPermission("erp:purchaseOrder:export")
    @Log(title = "采购订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseOrderBo bo, HttpServletResponse response) {
        List<PurchaseOrderVo> list = purchaseOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "采购订单", PurchaseOrderVo.class, response);
    }

    /**
     * 获取采购订单详细信息
     *
     * @param orderId 主键
     */
    @SaCheckPermission("erp:purchaseOrder:query")
    @GetMapping("/{orderId}")
    public R<PurchaseOrderVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long orderId) {
        return R.ok(purchaseOrderService.queryById(orderId));
    }

    /**
     * 新增采购订单
     */
    @SaCheckPermission("erp:purchaseOrder:add")
    @Log(title = "采购订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<PurchaseOrderVo> add(@Validated(AddGroup.class) @RequestBody PurchaseOrderBo bo) {
        return R.ok(purchaseOrderService.insertByBo(bo));
    }

    /**
     * 修改采购订单
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<PurchaseOrderVo> edit(@Validated(EditGroup.class) @RequestBody PurchaseOrderBo bo) {
        return R.ok(purchaseOrderService.updateByBo(bo));
    }

    /**
     * 删除采购订单
     *
     * @param orderIds 主键串
     */
    @SaCheckPermission("erp:purchaseOrder:remove")
    @Log(title = "采购订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] orderIds) {
        return toAjax(purchaseOrderService.deleteWithValidByIds(List.of(orderIds), true));
    }

    /**
     * 确认采购订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单确认", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{orderId}")
    public R<Void> confirm(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(purchaseOrderService.confirmOrder(orderId));
    }

    /**
     * 批量确认采购订单
     *
     * @param orderIds 订单ID数组
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单批量确认", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "订单ID不能为空") @RequestBody Long[] orderIds) {
        return toAjax(purchaseOrderService.batchConfirmOrders(List.of(orderIds)));
    }

    /**
     * 创建采购入库单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "创建采购入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/createPurchaseInbound/{orderId}")
    public R<Void> createPurchaseInbound(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(purchaseOrderService.createPurchaseInbound(orderId));
    }

    /**
     * 批量创建采购入库单
     *
     * @param orderIds 订单ID数组
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "批量创建采购入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchCreatePurchaseInbound")
    public R<Void> batchCreatePurchaseInbound(@NotEmpty(message = "订单ID不能为空") @RequestBody Long[] orderIds) {
        return toAjax(purchaseOrderService.batchCreatePurchaseInbound(List.of(orderIds)));
    }

    /**
     * 创建仓库入库单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "创建仓库入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/createInbound/{orderId}")
    public R<Void> createInbound(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(purchaseOrderService.createInbound(orderId));
    }

    /**
     * 批量创建仓库入库单
     *
     * @param orderIds 订单ID数组
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "批量创建仓库入库单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchCreateInbound")
    public R<Void> batchCreateInbound(@NotEmpty(message = "订单ID不能为空") @RequestBody Long[] orderIds) {
        return toAjax(purchaseOrderService.batchCreateInbound(List.of(orderIds)));
    }

    /**
     * 取消采购订单
     *
     * @param orderId 订单ID
     * @param reason  取消原因
     */
    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单取消", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{orderId}")
    public R<Void> cancel(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId, @RequestParam(required = false) String reason) {
        return toAjax(purchaseOrderService.cancelOrder(orderId, reason));
    }

    @SaCheckPermission("erp:purchaseOrder:edit")
    @Log(title = "采购订单关闭", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{orderId}")
    public R<Void> close(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(purchaseOrderService.closeOrder(orderId));
    }

    // TODO: [采购订单审批流程接口] - 优先级: HIGH - 参考文档: docs/design/README_STATE.md
    // 需要添加以下审批相关接口，与 warm-flow 工作流引擎集成：

    /**
     * 提交采购订单审批
     * @param orderId 订单ID
     * @return 提交结果
     */
    // @SaCheckPermission("erp:purchaseOrder:submit")
    // @Log(title = "提交采购订单审批", businessType = BusinessType.UPDATE)
    // @PostMapping("/submit/{orderId}")
    // public R<Void> submitForApproval(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
    //     return toAjax(purchaseOrderService.submitForApproval(orderId));
    // }

    /**
     * 审批通过采购订单
     * @param orderId 订单ID
     * @param approvalComment 审批意见
     * @return 审批结果
     */
    // @SaCheckPermission("erp:purchaseOrder:approve")
    // @Log(title = "审批通过采购订单", businessType = BusinessType.UPDATE)
    // @PostMapping("/approve/{orderId}")
    // public R<Void> approveOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
    //                            @RequestParam(required = false) String approvalComment) {
    //     return toAjax(purchaseOrderService.approveOrder(orderId, approvalComment));
    // }

    /**
     * 审批驳回采购订单
     * @param orderId 订单ID
     * @param rejectReason 驳回原因
     * @return 驳回结果
     */
    // @SaCheckPermission("erp:purchaseOrder:reject")
    // @Log(title = "审批驳回采购订单", businessType = BusinessType.UPDATE)
    // @PostMapping("/reject/{orderId}")
    // public R<Void> rejectOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
    //                           @RequestParam(required = true) String rejectReason) {
    //     return toAjax(purchaseOrderService.rejectOrder(orderId, rejectReason));
    // }

    // TODO: [采购订单统计分析接口] - 优先级: MEDIUM - 参考文档: docs/design/README_OVERVIEW.md
    // 需要添加统计分析接口：
    // 1. 采购统计: GET /statistics - 按供应商、时间、状态等维度统计
    // 2. 采购趋势: GET /trends - 采购趋势分析和预测
    // 3. 供应商分析: GET /supplierAnalysis - 供应商绩效分析
    // 实现思路：使用聚合查询，返回图表数据和业务指标
}
