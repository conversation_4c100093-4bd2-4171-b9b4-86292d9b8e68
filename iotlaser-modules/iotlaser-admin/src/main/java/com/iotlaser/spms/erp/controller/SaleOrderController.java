package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleOrderBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import com.iotlaser.spms.erp.service.ISaleOrderService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售订单控制器
 * 提供销售订单的完整生命周期管理，包括订单创建、状态流转、出库单生成、应收单生成等核心业务功能
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleOrder")
public class SaleOrderController extends BaseController {

    private final ISaleOrderService saleOrderService;

    @SaCheckPermission("erp:saleOrder:list")
    @GetMapping("/list")
    public TableDataInfo<SaleOrderVo> list(SaleOrderBo bo, PageQuery pageQuery) {
        return saleOrderService.queryPageList(bo, pageQuery);
    }

    @SaCheckPermission("erp:saleOrder:export")
    @Log(title = "销售订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleOrderBo bo, HttpServletResponse response) {
        List<SaleOrderVo> list = saleOrderService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售订单", SaleOrderVo.class, response);
    }

    @SaCheckPermission("erp:saleOrder:query")
    @GetMapping("/{orderId}")
    public R<SaleOrderVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long orderId) {
        return R.ok(saleOrderService.queryById(orderId));
    }

    @SaCheckPermission("erp:saleOrder:add")
    @Log(title = "销售订单", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<SaleOrderVo> add(@Validated(AddGroup.class) @RequestBody SaleOrderBo bo) {
        return R.ok(saleOrderService.insertByBo(bo));
    }

    @SaCheckPermission("erp:saleOrder:edit")
    @Log(title = "销售订单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<SaleOrderVo> edit(@Validated(EditGroup.class) @RequestBody SaleOrderBo bo) {
        return R.ok(saleOrderService.updateByBo(bo));
    }

    /**
     * 删除销售订单
     *
     * @param orderIds 主键串
     */
    @SaCheckPermission("erp:saleOrder:remove")
    @Log(title = "销售订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orderIds) {
        return toAjax(saleOrderService.deleteWithValidByIds(List.of(orderIds), true));
    }

    /**
     * 确认销售订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:saleOrder:confirm")
    @Log(title = "确认销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{orderId}")
    public R<Void> confirmOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.confirmOrder(orderId));
    }

    /**
     * 挂起销售订单
     *
     * @param orderId    订单ID
     * @param holdReason 挂起原因
     */
    @SaCheckPermission("erp:saleOrder:hold")
    @Log(title = "挂起销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/hold/{orderId}")
    public R<Void> holdOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
                             @RequestParam(required = false) String holdReason) {
        return toAjax(saleOrderService.holdOrder(orderId, holdReason));
    }

    /**
     * 恢复挂起的销售订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:saleOrder:resume")
    @Log(title = "恢复销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/resume/{orderId}")
    public R<Void> resumeOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.resumeOrder(orderId));
    }

    /**
     * 取消销售订单
     *
     * @param orderId      订单ID
     * @param cancelReason 取消原因
     */
    @SaCheckPermission("erp:saleOrder:cancel")
    @Log(title = "取消销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{orderId}")
    public R<Void> cancelOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId,
                               @RequestParam(required = false) String cancelReason) {
        return toAjax(saleOrderService.cancelOrder(orderId, cancelReason));
    }

    /**
     * 关闭销售订单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:saleOrder:close")
    @Log(title = "关闭销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/close/{orderId}")
    public R<Void> closeOrder(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.closeOrder(orderId));
    }

    /**
     * 从销售订单生成应收单
     *
     * @param orderId 订单ID
     */
    @SaCheckPermission("erp:saleOrder:generateReceivable")
    @Log(title = "生成应收单", businessType = BusinessType.INSERT)
    @PostMapping("/generateReceivable/{orderId}")
    public R<Void> generateReceivable(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.generateReceivableFromOrder(orderId));
    }

    @SaCheckPermission("erp:saleOrder:createOutbound")
    @Log(title = "创建出库单", businessType = BusinessType.INSERT)
    @PostMapping("/createOutbound/{orderId}")
    public R<Void> createOutbound(@NotNull(message = "订单ID不能为空") @PathVariable Long orderId) {
        return toAjax(saleOrderService.createOutbound(orderId));
    }

    // TODO: [销售订单状态流转接口] - 优先级: HIGH - 参考文档: docs/design/README_STATE.md
    // 需要添加以下状态流转接口：
    // 1. 提交审核: POST /submit/{orderId} - 将订单从 DRAFT 转为 PENDING_APPROVAL
    // 2. 审核通过: POST /approve/{orderId} - 将订单从 PENDING_APPROVAL 转为 CONFIRMED
    // 3. 审核驳回: POST /reject/{orderId} - 将订单从 PENDING_APPROVAL 转为 DRAFT
    // 4. 取消订单: POST /cancel/{orderId} - 将订单转为 CANCELLED 状态
    // 5. 完成订单: POST /complete/{orderId} - 将订单转为 COMPLETED 状态
    // 实现思路：每个接口调用对应的 Service 方法，包含状态校验和业务逻辑

    // TODO: [销售订单批量操作接口] - 优先级: MEDIUM - 参考文档: docs/design/README_FLOW.md
    // 需要添加批量操作接口：
    // 1. 批量审核: POST /batchApprove - 批量审核多个订单
    // 2. 批量取消: POST /batchCancel - 批量取消多个订单
    // 3. 批量创建出库单: POST /batchCreateOutbound - 为多个订单批量创建出库单
    // 实现思路：接收订单ID列表，循环调用单个操作方法，支持部分成功的结果返回

    // TODO: [销售订单统计分析接口] - 优先级: LOW - 参考文档: docs/design/README_OVERVIEW.md
    // 需要添加统计分析接口：
    // 1. 订单统计: GET /statistics - 按状态、时间、客户等维度统计订单数量和金额
    // 2. 销售趋势: GET /trends - 销售趋势分析，支持按月、季度、年度统计
    // 3. 客户分析: GET /customerAnalysis - 客户销售排行和分析
    // 实现思路：使用聚合查询，返回统计数据和图表所需的数据结构
}
