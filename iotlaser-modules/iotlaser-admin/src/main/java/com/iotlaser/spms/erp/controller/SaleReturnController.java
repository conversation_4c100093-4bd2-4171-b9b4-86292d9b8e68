package com.iotlaser.spms.erp.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.erp.domain.bo.SaleReturnBo;
import com.iotlaser.spms.erp.domain.vo.SaleReturnVo;
import com.iotlaser.spms.erp.service.ISaleReturnService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 销售退货
 *
 * <AUTHOR> Kai
 * @date 2025/05/08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/erp/saleReturn")
public class SaleReturnController extends BaseController {

    private final ISaleReturnService saleReturnService;

    /**
     * 查询销售退货列表
     */
    @SaCheckPermission("erp:saleReturn:list")
    @GetMapping("/list")
    public TableDataInfo<SaleReturnVo> list(SaleReturnBo bo, PageQuery pageQuery) {
        return saleReturnService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出销售退货列表
     */
    @SaCheckPermission("erp:saleReturn:export")
    @Log(title = "销售退货", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SaleReturnBo bo, HttpServletResponse response) {
        List<SaleReturnVo> list = saleReturnService.queryList(bo);
        ExcelUtil.exportExcel(list, "销售退货", SaleReturnVo.class, response);
    }

    /**
     * 获取销售退货详细信息
     *
     * @param returnId 主键
     */
    @SaCheckPermission("erp:saleReturn:query")
    @GetMapping("/{returnId}")
    public R<SaleReturnVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long returnId) {
        return R.ok(saleReturnService.queryById(returnId));
    }

    /**
     * 新增销售退货
     */
    @SaCheckPermission("erp:saleReturn:add")
    @Log(title = "销售退货", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<SaleReturnVo> add(@Validated(AddGroup.class) @RequestBody SaleReturnBo bo) {
        return R.ok(saleReturnService.insertByBo(bo));
    }

    /**
     * 修改销售退货
     */
    @SaCheckPermission("erp:saleReturn:edit")
    @Log(title = "销售退货", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<SaleReturnVo> edit(@Validated(EditGroup.class) @RequestBody SaleReturnBo bo) {
        return R.ok(saleReturnService.updateByBo(bo));
    }

    /**
     * 删除销售退货
     *
     * @param returnIds 主键串
     */
    @SaCheckPermission("erp:saleReturn:remove")
    @Log(title = "销售退货", businessType = BusinessType.DELETE)
    @DeleteMapping("/{returnIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] returnIds) {
        return toAjax(saleReturnService.deleteWithValidByIds(List.of(returnIds), true));
    }

    /**
     * 确认销售退货单
     *
     * @param returnId 退货单ID
     */
    @SaCheckPermission("erp:saleReturn:edit")
    @Log(title = "确认销售退货单", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm/{returnId}")
    public R<Void> confirm(@NotNull(message = "退货单ID不能为空") @PathVariable Long returnId) {
        return toAjax(saleReturnService.confirmReturn(returnId));
    }

    /**
     * 批量确认销售退货单
     *
     * @param returnIds 退货单ID集合
     */
    @SaCheckPermission("erp:saleReturn:edit")
    @Log(title = "批量确认销售退货单", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "退货单ID不能为空") @RequestBody Long[] returnIds) {
        return toAjax(saleReturnService.batchConfirmReturns(List.of(returnIds)));
    }

    /**
     * 收货确认（客户已退回货物）
     *
     * @param returnId 退货单ID
     */
    @SaCheckPermission("erp:saleReturn:edit")
    @Log(title = "销售退货收货确认", businessType = BusinessType.UPDATE)
    @PostMapping("/receive/{returnId}")
    public R<Void> receive(@NotNull(message = "退货单ID不能为空") @PathVariable Long returnId) {
        return toAjax(saleReturnService.receiveReturn(returnId));
    }

    /**
     * 完成销售退货入库
     *
     * @param returnId 退货单ID
     */
    @SaCheckPermission("erp:saleReturn:edit")
    @Log(title = "完成销售退货入库", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{returnId}")
    public R<Void> complete(@NotNull(message = "退货单ID不能为空") @PathVariable Long returnId) {
        return toAjax(saleReturnService.completeReturn(returnId));
    }

    /**
     * 取消销售退货单
     *
     * @param returnId 退货单ID
     * @param reason   取消原因
     */
    @SaCheckPermission("erp:saleReturn:edit")
    @Log(title = "取消销售退货单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{returnId}")
    public R<Void> cancel(@NotNull(message = "退货单ID不能为空") @PathVariable Long returnId,
                          @RequestParam(required = false) String reason) {
        return toAjax(saleReturnService.cancelReturn(returnId, reason));
    }

}
