package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账户收支流水对象 erp_fin_account_ledger
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_account_ledger")
public class FinAccountLedger extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收支ID
     */
    @TableId(value = "ledger_id")
    private Long ledgerId;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编号
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编号
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 上游明细ID
     */
    private Long directSourceItemId;

    /**
     * 银行交易流水号
     */
    private String blankSerialNumber;

    /**
     * 交易发生时间
     */
    private LocalDateTime transactionTime;

    /**
     * 方向
     */
    private String direction;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易前账户余额
     */
    private BigDecimal balanceBefore;

    /**
     * 交易后账户余额
     */
    private BigDecimal balanceAfter;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 往来单位ID
     */
    private Long partnerId;

    /**
     * 往来单位编码
     */
    private String partnerCode;

    /**
     * 往来单位名称
     */
    private String partnerName;

    /**
     * 往来单位账号
     */
    private String partnerAccount;

    /**
     * 流水状态
     */
    private String ledgerStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
