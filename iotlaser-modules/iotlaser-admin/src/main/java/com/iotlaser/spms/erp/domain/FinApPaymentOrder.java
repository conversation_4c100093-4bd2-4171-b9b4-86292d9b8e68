package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 付款单对象 erp_fin_ap_payment_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ap_payment_order")
public class FinApPaymentOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 付款ID
     */
    @TableId(value = "payment_id")
    private Long paymentId;

    /**
     * 付款编码
     */
    private String paymentCode;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;

    /**
     * 付款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 付款方式
     */
    private String paymentMethod;

    /**
     * 付款时间
     */
    private LocalDate paymentDate;

    /**
     * 银行交易流水号
     */
    private String bankSerialNumber;

    /**
     * 已核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 未核销金额
     */
    private BigDecimal unappliedAmount;

    /**
     * 付款状态
     */
    private String paymentStatus;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
