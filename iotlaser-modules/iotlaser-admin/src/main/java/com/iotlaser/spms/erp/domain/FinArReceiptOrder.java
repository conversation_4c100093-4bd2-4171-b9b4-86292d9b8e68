package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收款单对象 erp_fin_ar_receipt_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_ar_receipt_order")
public class FinArReceiptOrder extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收款ID
     */
    @TableId(value = "receipt_id")
    private Long receiptId;

    /**
     * 收款编号
     */
    private String receiptCode;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;

    /**
     * 收款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 收款方式
     */
    private String paymentMethod;

    /**
     * 收款日期
     */
    private LocalDate paymentDate;

    /**
     * 银行交易流水
     */
    private String bankSerialNumber;

    /**
     * 已核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 未核销金额
     */
    private BigDecimal unappliedAmount;

    /**
     * 收款状态
     */
    private String receiptStatus;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
