package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 对账单对象 erp_fin_statement
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_fin_statement")
public class FinStatement extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 对账ID
     */
    @TableId(value = "statement_id")
    private Long statementId;

    /**
     * 对账编码
     */
    private String statementCode;

    /**
     * 对账名称
     */
    private String statementName;

    /**
     * 往来单位ID
     */
    private Long partnerId;

    /**
     * 往来单位名称
     */
    private String partnerName;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 此前余额
     */
    private BigDecimal openingBalance;

    /**
     * 期末余额 (应收余额)
     */
    private BigDecimal closingBalance;

    /**
     * 对账状态
     */
    private String statementStatus;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
