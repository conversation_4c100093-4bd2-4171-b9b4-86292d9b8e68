package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.PurchaseReturnStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.time.LocalDate;

/**
 * 采购退货对象 erp_purchase_return
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_purchase_return")
public class PurchaseReturn extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退货单ID
     */
    @TableId(value = "return_id")
    private Long returnId;

    /**
     * 退货单编号
     */
    private String returnCode;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编码
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 退货日期
     */
    private LocalDate returnDate;

    /**
     * 退货状态
     */
    private PurchaseReturnStatus returnStatus;

    /**
     * 退货申请人ID
     */
    private Long applicantId;

    /**
     * 退货申请人
     */
    private String applicantName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
