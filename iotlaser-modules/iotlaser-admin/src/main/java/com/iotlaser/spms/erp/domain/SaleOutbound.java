package com.iotlaser.spms.erp.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.erp.enums.SaleOutboundStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 销售出库对象 erp_sale_outbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("erp_sale_outbound")
public class SaleOutbound extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 出库单ID
     */
    @TableId(value = "outbound_id")
    private Long outboundId;

    /**
     * 出库单编号
     */
    private String outboundCode;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编码
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 出库时间
     */
    private LocalDateTime outboundTime;

    /**
     * 出库状态
     */
    private SaleOutboundStatus outboundStatus;

    /**
     * 发货负责人ID
     */
    private Long handlerId;

    /**
     * 发货负责人
     */
    private String handlerName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
