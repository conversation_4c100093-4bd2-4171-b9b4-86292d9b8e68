package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinAccountLedger;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 账户收支流水业务对象 erp_fin_account_ledger
 *
 * <AUTHOR> Kai
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinAccountLedger.class, reverseConvertGenerate = false)
public class FinAccountLedgerBo extends BaseEntity {

    /**
     * 收支ID
     */
    private Long ledgerId;

    /**
     * 账号ID
     */
    @NotNull(message = "账号ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long accountId;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 账户类型
     */
    private String accountType;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编号
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @NotNull(message = "上游ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceId;

    /**
     * 上游编号
     */
    @NotBlank(message = "上游编号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotBlank(message = "上游类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 上游明细ID
     */
    @NotNull(message = "上游明细ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceItemId;

    /**
     * 银行交易流水号
     */
    private String blankSerialNumber;

    /**
     * 交易发生时间
     */
    private LocalDateTime transactionTime;

    /**
     * 方向
     */
    private String direction;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 交易前账户余额
     */
    private BigDecimal balanceBefore;

    /**
     * 交易后账户余额
     */
    private BigDecimal balanceAfter;

    /**
     * 交易类型
     */
    private String transactionType;

    /**
     * 往来单位ID
     */
    private Long partnerId;

    /**
     * 往来单位编码
     */
    private String partnerCode;

    /**
     * 往来单位名称
     */
    private String partnerName;

    /**
     * 往来单位账号
     */
    private String partnerAccount;

    /**
     * 流水状态
     */
    private String ledgerStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
