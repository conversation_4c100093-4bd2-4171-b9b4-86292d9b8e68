package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.FinExpensePaymentLink;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 管理费用与付款单核销关系业务对象 erp_fin_expense_payment_link
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FinExpensePaymentLink.class, reverseConvertGenerate = false)
public class FinExpensePaymentLinkBo extends BaseEntity {

    /**
     * 关系ID
     */
    private Long linkId;

    /**
     * 付款ID
     */
    @NotNull(message = "付款ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long paymentId;

    /**
     * 应付ID
     */
    @NotNull(message = "应付ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long invoiceId;

    /**
     * 核销金额
     */
    private BigDecimal appliedAmount;

    /**
     * 核销日期
     */
    private LocalDate cancellationDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
