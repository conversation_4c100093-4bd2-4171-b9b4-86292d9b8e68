package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.SaleOrder;
import com.iotlaser.spms.erp.enums.SaleOrderStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售订单业务对象 erp_sale_order
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SaleOrder.class, reverseConvertGenerate = false)
public class SaleOrderBo extends BaseEntity {

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空", groups = {EditGroup.class})
    private String orderCode;

    /**
     * 源头ID
     */
    @NotNull(message = "源头ID不能为空", groups = {EditGroup.class})
    private Long sourceId;

    /**
     * 源头编码
     */
    @NotBlank(message = "源头编码不能为空", groups = {EditGroup.class})
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotNull(message = "源头类型不能为空", groups = {EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @NotNull(message = "上游ID不能为空", groups = {EditGroup.class})
    private Long directSourceId;

    /**
     * 上游编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {EditGroup.class})
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotNull(message = "上游类型不能为空", groups = {EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 下单日期
     */
    private LocalDate orderDate;

    /**
     * 订单状态
     */
    private SaleOrderStatus orderStatus;

    /**
     * 销售员ID
     */
    private Long handlerId;

    /**
     * 销售员
     */
    private String handlerName;

    /**
     * 审批人ID
     */
    private Long approverId;

    /**
     * 审批人
     */
    private String approverName;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
