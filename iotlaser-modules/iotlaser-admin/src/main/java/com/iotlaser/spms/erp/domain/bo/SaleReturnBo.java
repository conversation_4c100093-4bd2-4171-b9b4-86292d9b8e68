package com.iotlaser.spms.erp.domain.bo;

import com.iotlaser.spms.erp.domain.SaleReturn;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 销售退货业务对象 erp_sale_return
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SaleReturn.class, reverseConvertGenerate = false)
public class SaleReturnBo extends BaseEntity {

    /**
     * 退货单ID
     */
    private Long returnId;

    /**
     * 退货单编号
     */
    @NotBlank(message = "退货单编号不能为空", groups = {EditGroup.class})
    private String returnCode;

    /**
     * 源头ID
     */
    @NotNull(message = "源头ID不能为空", groups = {EditGroup.class})
    private Long sourceId;

    /**
     * 源头编码
     */
    @NotBlank(message = "源头编码不能为空", groups = {EditGroup.class})
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotNull(message = "源头类型不能为空", groups = {EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @NotNull(message = "上游ID不能为空", groups = {EditGroup.class})
    private Long directSourceId;

    /**
     * 上游编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {EditGroup.class})
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotNull(message = "上游类型不能为空", groups = {EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 退货时间
     */
    @NotNull(message = "退货时间不能为空", groups = {EditGroup.class})
    private LocalDateTime returnTime;

    /**
     * 退货状态
     */
    private SaleReturnStatus returnStatus;

    /**
     * 退货处理人ID
     */
    private Long handlerId;

    /**
     * 退货处理人
     */
    private String handlerName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
