package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinAccountLedger;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 账户收支流水视图对象 erp_fin_account_ledger
 *
 * <AUTHOR> Kai
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinAccountLedger.class)
public class FinAccountLedgerVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收支ID
     */
    @ExcelProperty(value = "收支ID")
    private Long ledgerId;

    /**
     * 账号ID
     */
    @ExcelProperty(value = "账号ID")
    private Long accountId;

    /**
     * 账户编码
     */
    @ExcelProperty(value = "账户编码")
    private String accountCode;

    /**
     * 账户名称
     */
    @ExcelProperty(value = "账户名称")
    private String accountName;

    /**
     * 账户类型
     */
    @ExcelProperty(value = "账户类型")
    private String accountType;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编号
     */
    @ExcelProperty(value = "源头编号")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编号
     */
    @ExcelProperty(value = "上游编号")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;

    /**
     * 上游明细ID
     */
    @ExcelProperty(value = "上游明细ID")
    private Long directSourceItemId;

    /**
     * 银行交易流水号
     */
    @ExcelProperty(value = "银行交易流水号")
    private String blankSerialNumber;

    /**
     * 交易发生时间
     */
    @ExcelProperty(value = "交易发生时间")
    private LocalDateTime transactionTime;

    /**
     * 方向
     */
    @ExcelProperty(value = "方向")
    private String direction;

    /**
     * 交易金额
     */
    @ExcelProperty(value = "交易金额")
    private BigDecimal amount;

    /**
     * 交易前账户余额
     */
    @ExcelProperty(value = "交易前账户余额")
    private BigDecimal balanceBefore;

    /**
     * 交易后账户余额
     */
    @ExcelProperty(value = "交易后账户余额")
    private BigDecimal balanceAfter;

    /**
     * 交易类型
     */
    @ExcelProperty(value = "交易类型")
    private String transactionType;

    /**
     * 往来单位ID
     */
    @ExcelProperty(value = "往来单位ID")
    private Long partnerId;

    /**
     * 往来单位编码
     */
    @ExcelProperty(value = "往来单位编码")
    private String partnerCode;

    /**
     * 往来单位名称
     */
    @ExcelProperty(value = "往来单位名称")
    private String partnerName;

    /**
     * 往来单位账号
     */
    @ExcelProperty(value = "往来单位账号")
    private String partnerAccount;

    /**
     * 流水状态
     */
    @ExcelProperty(value = "流水状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_fin_account_ledger_status")
    private String ledgerStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
