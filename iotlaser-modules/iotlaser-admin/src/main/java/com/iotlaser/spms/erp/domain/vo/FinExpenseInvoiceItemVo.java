package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinExpenseInvoiceItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 管理费用明细视图对象 erp_fin_expense_invoice_item
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinExpenseInvoiceItem.class)
public class FinExpenseInvoiceItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @ExcelProperty(value = "明细ID")
    private Long itemId;

    /**
     * 应付ID
     */
    @ExcelProperty(value = "应付ID")
    private Long invoiceId;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 单价(含税)
     */
    @ExcelProperty(value = "单价(含税)")
    private BigDecimal price;

    /**
     * 单价(不含税)
     */
    @ExcelProperty(value = "单价(不含税)")
    private BigDecimal priceExclusiveTax;

    /**
     * 金额(含税)
     */
    @ExcelProperty(value = "金额(含税)")
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    @ExcelProperty(value = "金额(不含税)")
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    @ExcelProperty(value = "税率")
    private BigDecimal taxRate;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
