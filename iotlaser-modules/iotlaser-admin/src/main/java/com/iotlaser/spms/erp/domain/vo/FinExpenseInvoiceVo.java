package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.FinExpenseInvoice;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;


/**
 * 管理费用视图对象 erp_fin_expense_invoice
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = FinExpenseInvoice.class)
public class FinExpenseInvoiceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 应付ID
     */
    @ExcelProperty(value = "应付ID")
    private Long invoiceId;

    /**
     * 应付编号
     */
    @ExcelProperty(value = "应付编号")
    private String invoiceCode;

    /**
     * 应付名称
     */
    @ExcelProperty(value = "应付名称")
    private String invoiceName;

    /**
     * 收款方类型
     */
    @ExcelProperty(value = "收款方类型")
    private String payeeType;

    /**
     * 收款方ID
     */
    @ExcelProperty(value = "收款方ID")
    private Long payeeId;

    /**
     * 收款方编码
     */
    @ExcelProperty(value = "收款方编码")
    private String payeeCode;

    /**
     * 收款方名称
     */
    @ExcelProperty(value = "收款方名称")
    private String payeeName;

    /**
     * 发票号码
     */
    @ExcelProperty(value = "发票号码")
    private String invoiceNumber;

    /**
     * 开票日期
     */
    @ExcelProperty(value = "开票日期")
    private LocalDate invoiceDate;

    /**
     * 金额(不含税)
     */
    @ExcelProperty(value = "金额(不含税)")
    private BigDecimal amountExclusiveTax;

    /**
     * 金额(含税)
     */
    @ExcelProperty(value = "金额(含税)")
    private BigDecimal amount;

    /**
     * 税额
     */
    @ExcelProperty(value = "税额")
    private BigDecimal taxAmount;

    /**
     * 应付状态
     */
    @ExcelProperty(value = "应付状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_fin_expense_invoice_status")
    private String invoiceStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
