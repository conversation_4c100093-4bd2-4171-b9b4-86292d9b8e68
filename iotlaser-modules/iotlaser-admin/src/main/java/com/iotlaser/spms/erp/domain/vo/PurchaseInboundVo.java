package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 采购入库视图对象 erp_purchase_inbound
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PurchaseInbound.class)
public class PurchaseInboundVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入库单ID
     */
    @ExcelProperty(value = "入库单ID")
    private Long inboundId;

    /**
     * 入库单编号
     */
    @ExcelProperty(value = "入库单编号")
    private String inboundCode;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编码
     */
    @ExcelProperty(value = "源头编码")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;

    /**
     * 供应商ID
     */
    @ExcelProperty(value = "供应商ID")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 入库时间
     */
    @ExcelProperty(value = "入库时间")
    private LocalDateTime inboundTime;

    /**
     * 入库状态
     */
    @ExcelProperty(value = "入库状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_purchase_inbound_status")
    private PurchaseInboundStatus inboundStatus;

    /**
     * 收货负责人ID
     */
    @ExcelProperty(value = "收货负责人ID")
    private Long handlerId;

    /**
     * 收货负责人
     */
    @ExcelProperty(value = "收货负责人")
    private String handlerName;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 明细
     */
    private List<PurchaseInboundItemVo> items;

}
