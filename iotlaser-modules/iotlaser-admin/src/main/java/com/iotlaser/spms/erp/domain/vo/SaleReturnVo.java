package com.iotlaser.spms.erp.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.erp.domain.SaleReturn;
import com.iotlaser.spms.erp.enums.SaleReturnStatus;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 销售退货视图对象 erp_sale_return
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SaleReturn.class)
public class SaleReturnVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退货单ID
     */
    @ExcelProperty(value = "退货单ID")
    private Long returnId;

    /**
     * 退货单编号
     */
    @ExcelProperty(value = "退货单编号")
    private String returnCode;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编码
     */
    @ExcelProperty(value = "源头编码")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long customerId;

    /**
     * 客户名称
     */
    @ExcelProperty(value = "客户名称")
    private String customerName;

    /**
     * 退货时间
     */
    @ExcelProperty(value = "退货时间")
    private LocalDateTime returnTime;

    /**
     * 退货状态
     */
    @ExcelProperty(value = "退货状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "erp_sale_return_status")
    private SaleReturnStatus returnStatus;

    /**
     * 退货处理人ID
     */
    @ExcelProperty(value = "退货处理人ID")
    private Long handlerId;

    /**
     * 退货处理人
     */
    @ExcelProperty(value = "退货处理人")
    private String handlerName;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
