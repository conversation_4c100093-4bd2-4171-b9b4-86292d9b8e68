package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应付发票状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinApInvoiceStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "发票已录入，但未提交"),
    PENDING("pending", "待处理", "发票已提交，等待处理"),
    PENDING_APPROVAL("pending_approval", "待审批", "发票已提交，等待上级批准"),
    APPROVED("approved", "已审批", "发票已审核无误，成为正式的应付账款"),
    PARTIALLY_PAID("partially_paid", "部分付款", "已支付并核销了部分发票金额"),
    FULLY_PAID("fully_paid", "全部付清", "发票金额已全部核销完毕"),
    OVERDUE("overdue", "已逾期", "发票已超过付款期限"),
    REJECTED("rejected", "已拒绝", "发票审批被拒绝"),
    CANCELLED("cancelled", "已取消", "发票在付款前被取消");

    public final static String DICT_CODE = "erp_fin_ap_invoice_status";
    public final static String DICT_NAME = "应付发票状态";
    public final static String DICT_DESC = "管理应付发票的处理流程状态，从录入、审批到付款完成的完整业务流程";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 应付发票状态枚举
     */
    public static FinApInvoiceStatus getByValue(String value) {
        for (FinApInvoiceStatus invoiceStatus : values()) {
            if (invoiceStatus.getValue().equals(value)) {
                return invoiceStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否为可编辑状态
     *
     * @return 是否可编辑
     */
    public boolean isEditable() {
        return this == DRAFT || this == REJECTED;
    }

    /**
     * 判断是否为可删除状态
     *
     * @return 是否可删除
     */
    public boolean isDeletable() {
        return this == DRAFT;
    }

    /**
     * 判断是否为已完成状态
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return this == FULLY_PAID || this == CANCELLED;
    }

    /**
     * 判断是否可以付款
     *
     * @return 是否可以付款
     */
    public boolean isPayable() {
        return this == APPROVED || this == PARTIALLY_PAID || this == OVERDUE;
    }

    /**
     * 判断是否为逾期状态
     *
     * @return 是否逾期
     */
    public boolean isOverdue() {
        return this == OVERDUE;
    }

    /**
     * 获取下一个可能的状态
     *
     * @return 下一个可能的状态列表
     */
    public FinApInvoiceStatus[] getNextPossibleStates() {
        switch (this) {
            case DRAFT:
                return new FinApInvoiceStatus[]{PENDING_APPROVAL, CANCELLED};
            case PENDING_APPROVAL:
                return new FinApInvoiceStatus[]{APPROVED, REJECTED};
            case APPROVED:
                return new FinApInvoiceStatus[]{PARTIALLY_PAID, OVERDUE, CANCELLED};
            case PARTIALLY_PAID:
                return new FinApInvoiceStatus[]{FULLY_PAID, OVERDUE};
            case OVERDUE:
                return new FinApInvoiceStatus[]{PARTIALLY_PAID, FULLY_PAID};
            case REJECTED:
                return new FinApInvoiceStatus[]{CANCELLED};
            case FULLY_PAID:
            case CANCELLED:
            default:
                return new FinApInvoiceStatus[]{};
        }
    }
}
