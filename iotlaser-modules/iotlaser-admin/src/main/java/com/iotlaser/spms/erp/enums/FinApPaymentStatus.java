package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 应付付款单状态枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinApPaymentStatus implements IDictEnum<String> {

    DRAFT("draft", "草稿", "付款申请已创建，但未提交"),
    PENDING_APPROVAL("pending_approval", "待审批", "等待上级批准付款"),
    APPROVED("approved", "已审批", "付款已获批，资金已付出，但尚未核销"),
    CONFIRMED("confirmed", "已确认", "付款已确认，可以进行核销"),
    PARTIALLY_APPLIED("partially_applied", "部分核销", "付款金额的一部分已用于核销发票"),
    FULLY_APPLIED("fully_applied", "全部核销", "付款金额已全部分配给一张或多张发票"),
    REJECTED("rejected", "已拒绝", "付款申请被拒绝"),
    CANCELLED("cancelled", "已取消", "付款单在执行前被取消");

    public final static String DICT_CODE = "erp_fin_ap_payment_status";
    public final static String DICT_NAME = "应付付款状态";
    public final static String DICT_DESC = "管理应付付款单的处理流程状态，从申请、审批到付款执行的完整业务流程";
    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 状态值
     * @return 付款单状态枚举
     */
    public static FinApPaymentStatus getByValue(String value) {
        for (FinApPaymentStatus paymentStatus : values()) {
            if (paymentStatus.getValue().equals(value)) {
                return paymentStatus;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否为可编辑状态
     *
     * @return 是否可编辑
     */
    public boolean isEditable() {
        return this == DRAFT || this == REJECTED;
    }

    /**
     * 判断是否为可删除状态
     *
     * @return 是否可删除
     */
    public boolean isDeletable() {
        return this == DRAFT;
    }

    /**
     * 判断是否为已完成状态
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return this == FULLY_APPLIED || this == CANCELLED;
    }

    /**
     * 判断是否可以核销
     *
     * @return 是否可以核销
     */
    public boolean isApplicable() {
        return this == CONFIRMED || this == PARTIALLY_APPLIED;
    }

    /**
     * 获取下一个可能的状态
     *
     * @return 下一个可能的状态列表
     */
    public FinApPaymentStatus[] getNextPossibleStates() {
        switch (this) {
            case DRAFT:
                return new FinApPaymentStatus[]{PENDING_APPROVAL, CANCELLED};
            case PENDING_APPROVAL:
                return new FinApPaymentStatus[]{APPROVED, REJECTED};
            case APPROVED:
                return new FinApPaymentStatus[]{CONFIRMED, CANCELLED};
            case CONFIRMED:
                return new FinApPaymentStatus[]{PARTIALLY_APPLIED, CANCELLED};
            case PARTIALLY_APPLIED:
                return new FinApPaymentStatus[]{FULLY_APPLIED};
            case REJECTED:
                return new FinApPaymentStatus[]{CANCELLED};
            case FULLY_APPLIED:
            case CANCELLED:
            default:
                return new FinApPaymentStatus[]{};
        }
    }
}
