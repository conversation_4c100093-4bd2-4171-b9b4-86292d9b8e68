package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 付款类型枚举
 *
 * <AUTHOR>
 * @date 2025007-10
 */
@Getter
@AllArgsConstructor
public enum FinPayeeType implements IDictEnum<String> {

    SUPPLIER("supplier", "供应商费用", "供应商费用"),
    EMPLOYEE("employee", "供应商费用", "供应商费用");

    public final static String DICT_CODE = "erp_fin_ap_invoice_payee_type";
    public final static String DICT_NAME = "付款类型";
    public final static String DICT_DESC = "付款方式";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 付款类型枚举
     */
    public static FinPayeeType getByValue(String value) {
        for (FinPayeeType paymentType : values()) {
            if (paymentType.getValue().equals(value)) {
                return paymentType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

}
