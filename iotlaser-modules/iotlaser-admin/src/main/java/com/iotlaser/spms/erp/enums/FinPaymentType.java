package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 付款类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinPaymentType implements IDictEnum<String> {

    BANK_TRANSFER("bank_transfer", "银行转账", "通过银行转账方式付款"),
    CASH("cash", "现金支付", "现金方式付款"),
    CHECK("check", "支票支付", "开具支票付款"),
    CREDIT_CARD("credit_card", "信用卡支付", "信用卡方式付款"),
    ONLINE_PAYMENT("online_payment", "在线支付", "通过在线支付平台付款"),
    LETTER_OF_CREDIT("letter_of_credit", "信用证", "信用证方式付款"),
    BILL_OF_EXCHANGE("bill_of_exchange", "汇票", "汇票方式付款"),
    OFFSET("offset", "抵扣", "通过债权债务抵扣"),
    OTHER("other", "其他方式", "其他付款方式");

    public final static String DICT_CODE = "erp_fin_payment_type";
    public final static String DICT_NAME = "付款类型";
    public final static String DICT_DESC = "定义供应商付款的方式类型，包括银行转账、现金、支票等不同付款渠道";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 付款类型枚举
     */
    public static FinPaymentType getByValue(String value) {
        for (FinPaymentType paymentType : values()) {
            if (paymentType.getValue().equals(value)) {
                return paymentType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否需要银行账户
     *
     * @return 是否需要银行账户
     */
    public boolean requiresBankAccount() {
        return this == BANK_TRANSFER || this == CHECK || this == LETTER_OF_CREDIT;
    }

    /**
     * 判断是否为即时到账
     *
     * @return 是否即时到账
     */
    public boolean isInstant() {
        return this == CASH || this == CREDIT_CARD || this == ONLINE_PAYMENT;
    }

    /**
     * 获取预计到账时间（小时）
     *
     * @return 预计到账时间
     */
    public int getExpectedArrivalHours() {
        switch (this) {
            case CASH:
            case CREDIT_CARD:
            case ONLINE_PAYMENT:
                return 0; // 即时到账
            case BANK_TRANSFER:
                return 24; // 1天
            case CHECK:
                return 72; // 3天
            case LETTER_OF_CREDIT:
            case BILL_OF_EXCHANGE:
                return 168; // 7天
            case OFFSET:
                return 0; // 即时生效
            case OTHER:
            default:
                return 24; // 默认1天
        }
    }

    /**
     * 判断是否需要审批
     *
     * @return 是否需要审批
     */
    public boolean requiresApproval() {
        return this == LETTER_OF_CREDIT || this == BILL_OF_EXCHANGE || this == OFFSET;
    }
}
