package com.iotlaser.spms.erp.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 收款类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Getter
@AllArgsConstructor
public enum FinReceiptType implements IDictEnum<String> {

    BANK_TRANSFER("bank_transfer", "银行转账", "客户通过银行转账收款"),
    CASH("cash", "现金收款", "现金方式收款"),
    CHECK("check", "支票收款", "收取客户支票"),
    CREDIT_CARD("credit_card", "信用卡收款", "信用卡方式收款"),
    ONLINE_PAYMENT("online_payment", "在线收款", "通过在线支付平台收款"),
    LETTER_OF_CREDIT("letter_of_credit", "信用证", "信用证方式收款"),
    BILL_OF_EXCHANGE("bill_of_exchange", "汇票", "汇票方式收款"),
    ADVANCE_PAYMENT("advance_payment", "预收款", "客户预付款项"),
    OFFSET("offset", "抵扣", "通过债权债务抵扣"),
    OTHER("other", "其他方式", "其他收款方式");

    public final static String DICT_CODE = "erp_fin_receipt_type";
    public final static String DICT_NAME = "收款类型";
    public final static String DICT_DESC = "定义客户收款的方式类型，包括银行转账、现金、支票等不同收款渠道";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 收款类型枚举
     */
    public static FinReceiptType getByValue(String value) {
        for (FinReceiptType receiptType : values()) {
            if (receiptType.getValue().equals(value)) {
                return receiptType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }

    /**
     * 判断是否需要银行账户
     *
     * @return 是否需要银行账户
     */
    public boolean requiresBankAccount() {
        return this == BANK_TRANSFER || this == CHECK || this == LETTER_OF_CREDIT;
    }

    /**
     * 判断是否为即时到账
     *
     * @return 是否即时到账
     */
    public boolean isInstant() {
        return this == CASH || this == CREDIT_CARD || this == ONLINE_PAYMENT;
    }

    /**
     * 判断是否为预收款
     *
     * @return 是否为预收款
     */
    public boolean isAdvancePayment() {
        return this == ADVANCE_PAYMENT;
    }

    /**
     * 获取预计到账时间（小时）
     *
     * @return 预计到账时间
     */
    public int getExpectedArrivalHours() {
        switch (this) {
            case CASH:
            case CREDIT_CARD:
            case ONLINE_PAYMENT:
                return 0; // 即时到账
            case BANK_TRANSFER:
                return 24; // 1天
            case CHECK:
                return 72; // 3天
            case LETTER_OF_CREDIT:
            case BILL_OF_EXCHANGE:
                return 168; // 7天
            case ADVANCE_PAYMENT:
            case OFFSET:
                return 0; // 即时生效
            case OTHER:
            default:
                return 24; // 默认1天
        }
    }

    /**
     * 判断是否需要特殊处理
     *
     * @return 是否需要特殊处理
     */
    public boolean requiresSpecialHandling() {
        return this == LETTER_OF_CREDIT || this == BILL_OF_EXCHANGE || this == ADVANCE_PAYMENT || this == OFFSET;
    }
}
