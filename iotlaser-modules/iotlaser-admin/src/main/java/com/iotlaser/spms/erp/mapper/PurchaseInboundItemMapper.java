package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.PurchaseInboundItem;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购入库明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
public interface PurchaseInboundItemMapper extends BaseMapperPlus<PurchaseInboundItem, PurchaseInboundItemVo> {

    default List<PurchaseInboundItem> queryByInboundId(Long inboundId) {
        return selectList(new LambdaQueryWrapper<PurchaseInboundItem>().eq(PurchaseInboundItem::getInboundId, inboundId));
    }

    /**
     * 删除采购入库明细表
     */
    default int deleteByInboundId(Long inboundId) {
        return delete(new LambdaQueryWrapper<PurchaseInboundItem>().eq(PurchaseInboundItem::getInboundId, inboundId));
    }

}
