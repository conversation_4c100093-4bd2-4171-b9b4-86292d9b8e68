package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.PurchaseInbound;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购入库Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface PurchaseInboundMapper extends BaseMapperPlus<PurchaseInbound, PurchaseInboundVo> {

    default Boolean existsByDirectSourceId(Long directSourceId) {
        return exists(new LambdaQueryWrapper<PurchaseInbound>().eq(PurchaseInbound::getDirectSourceId, directSourceId));
    }

    default List<PurchaseInboundVo> selectListByDirectSourceId(Long directSourceId) {
        return selectVoList(new LambdaQueryWrapper<PurchaseInbound>().eq(PurchaseInbound::getDirectSourceId, directSourceId));
    }
}
