package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.PurchaseOrderItem;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购订单明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
public interface PurchaseOrderItemMapper extends BaseMapperPlus<PurchaseOrderItem, PurchaseOrderItemVo> {

    default List<PurchaseOrderItemVo> selectListByOrderId(Long orderId) {
        return selectVoList(new LambdaQueryWrapper<PurchaseOrderItem>().eq(PurchaseOrderItem::getOrderId, orderId));
    }

    default int deleteByOrderId(Long orderId) {
        return delete(new LambdaQueryWrapper<PurchaseOrderItem>().eq(PurchaseOrderItem::getOrderId, orderId));
    }

}
