package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.PurchaseReturnItem;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 采购退货明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
public interface PurchaseReturnItemMapper extends BaseMapperPlus<PurchaseReturnItem, PurchaseReturnItemVo> {

    default List<PurchaseReturnItemVo> selectListByReturnId(Long returnId) {
        return selectVoList(new LambdaQueryWrapper<PurchaseReturnItem>().eq(PurchaseReturnItem::getReturnId, returnId));
    }

    default int deleteByOrderId(Long returnId) {
        return delete(new LambdaQueryWrapper<PurchaseReturnItem>().eq(PurchaseReturnItem::getReturnId, returnId));
    }
}
