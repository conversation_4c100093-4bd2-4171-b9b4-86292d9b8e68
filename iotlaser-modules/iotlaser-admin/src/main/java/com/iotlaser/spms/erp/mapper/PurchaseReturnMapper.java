package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 采购退货Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
public interface PurchaseReturnMapper extends BaseMapperPlus<PurchaseReturn, PurchaseReturnVo> {

    default Boolean existsByDirectSourceId(Long directSourceId) {
        return exists(new LambdaQueryWrapper<PurchaseReturn>().eq(PurchaseReturn::getDirectSourceId, directSourceId));
    }

}
