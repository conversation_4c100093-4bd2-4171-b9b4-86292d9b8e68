package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.SaleOutboundItem;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 销售出库明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
public interface SaleOutboundItemMapper extends BaseMapperPlus<SaleOutboundItem, SaleOutboundItemVo> {

    default List<SaleOutboundItemVo> queryByOutboundId(Long outboundId) {
        return selectVoList(new LambdaQueryWrapper<SaleOutboundItem>().eq(SaleOutboundItem::getOutboundId, outboundId));
    }

    default int deleteByOutboundId(Long outboundId) {
        return delete(new LambdaQueryWrapper<SaleOutboundItem>().eq(SaleOutboundItem::getOutboundId, outboundId));
    }
}
