package com.iotlaser.spms.erp.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.erp.domain.SaleReturnItem;
import com.iotlaser.spms.erp.domain.vo.SaleReturnItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 销售退货明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/08
 */
public interface SaleReturnItemMapper extends BaseMapperPlus<SaleReturnItem, SaleReturnItemVo> {

    default List<SaleReturnItemVo> queryByReturnId(Long returnId) {
        return selectVoList(new LambdaQueryWrapper<SaleReturnItem>().eq(SaleReturnItem::getReturnId, returnId));
    }

    default int deleteByReturnId(Long returnId) {
        return delete(new LambdaQueryWrapper<SaleReturnItem>().eq(SaleReturnItem::getReturnId, returnId));
    }
}
