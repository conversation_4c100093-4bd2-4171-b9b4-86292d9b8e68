package com.iotlaser.spms.erp.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据链路验证服务接口
 * 用于验证销售订单到财务对账完整数据链路的数据传递完整性和一致性
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
public interface IDataChainValidationService {

    /**
     * 验证销售订单主表与明细表的金额汇总一致性
     *
     * @param orderId 订单ID
     * @return 验证结果
     */
    DataChainValidationResult validateOrderAmountConsistency(Long orderId);

    /**
     * 验证订单明细与出库单的数量、金额对应关系
     *
     * @param orderId 订单ID
     * @return 验证结果
     */
    DataChainValidationResult validateOrderOutboundConsistency(Long orderId);

    /**
     * 验证出库单与应收发票的开票依据和金额传递
     *
     * @param orderId 订单ID
     * @return 验证结果
     */
    DataChainValidationResult validateOutboundInvoiceConsistency(Long orderId);

    /**
     * 验证应收发票与对账结果的金额匹配性
     *
     * @param receivableId 应收发票ID
     * @return 验证结果
     */
    DataChainValidationResult validateInvoiceReconciliationConsistency(Long receivableId);

    /**
     * 验证完整数据链路
     *
     * @param orderId 订单ID
     * @return 完整验证结果
     */
    default CompleteDataChainValidationResult validateCompleteDataChain(Long orderId) {
        CompleteDataChainValidationResult completeResult = new CompleteDataChainValidationResult();
        completeResult.setOrderId(orderId);
        completeResult.setValidationTime(LocalDate.now());

        try {
            // 验证订单金额一致性
            DataChainValidationResult orderResult = validateOrderAmountConsistency(orderId);
            completeResult.setOrderAmountValidation(orderResult);

            // 验证订单出库一致性
            DataChainValidationResult outboundResult = validateOrderOutboundConsistency(orderId);
            completeResult.setOrderOutboundValidation(outboundResult);

            // 验证出库开票一致性
            DataChainValidationResult invoiceResult = validateOutboundInvoiceConsistency(orderId);
            completeResult.setOutboundInvoiceValidation(invoiceResult);

            // 验证发票对账一致性（需要先获取应收发票ID）
            // TODO: 实现应收发票查询并验证对账一致性

            // 计算总体验证结果
            boolean overallValid = orderResult.isValid() && outboundResult.isValid() && invoiceResult.isValid();
            completeResult.setOverallValid(overallValid);

            // 汇总错误和警告
            List<String> allErrors = new ArrayList<>();
            List<String> allWarnings = new ArrayList<>();

            allErrors.addAll(orderResult.getErrors());
            allErrors.addAll(outboundResult.getErrors());
            allErrors.addAll(invoiceResult.getErrors());

            allWarnings.addAll(orderResult.getWarnings());
            allWarnings.addAll(outboundResult.getWarnings());
            allWarnings.addAll(invoiceResult.getWarnings());

            completeResult.setAllErrors(allErrors);
            completeResult.setAllWarnings(allWarnings);

        } catch (Exception e) {
            completeResult.setOverallValid(false);
            completeResult.addError("完整验证过程异常: " + e.getMessage());
        }

        return completeResult;
    }

    /**
     * 数据链路验证结果
     */
    class DataChainValidationResult {
        private String validationType;
        private Long targetId;
        private LocalDate validationTime;
        private boolean valid = true;
        private List<String> errors = new ArrayList<>();
        private List<String> warnings = new ArrayList<>();
        private Map<String, String> details = new HashMap<>();

        // Getters and Setters
        public String getValidationType() {
            return validationType;
        }

        public void setValidationType(String validationType) {
            this.validationType = validationType;
        }

        public Long getTargetId() {
            return targetId;
        }

        public void setTargetId(Long targetId) {
            this.targetId = targetId;
        }

        public LocalDate getValidationTime() {
            return validationTime;
        }

        public void setValidationTime(LocalDate validationTime) {
            this.validationTime = validationTime;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public List<String> getErrors() {
            return errors;
        }

        public void setErrors(List<String> errors) {
            this.errors = errors;
        }

        public List<String> getWarnings() {
            return warnings;
        }

        public void setWarnings(List<String> warnings) {
            this.warnings = warnings;
        }

        public Map<String, String> getDetails() {
            return details;
        }

        public void setDetails(Map<String, String> details) {
            this.details = details;
        }

        // 便捷方法
        public void addError(String error) {
            this.errors.add(error);
            this.valid = false;
        }

        public void addWarning(String warning) {
            this.warnings.add(warning);
        }

        public void addDetail(String key, String value) {
            this.details.put(key, value);
        }
    }

    /**
     * 完整数据链路验证结果
     */
    class CompleteDataChainValidationResult {
        private Long orderId;
        private LocalDate validationTime;
        private boolean overallValid;
        private DataChainValidationResult orderAmountValidation;
        private DataChainValidationResult orderOutboundValidation;
        private DataChainValidationResult outboundInvoiceValidation;
        private DataChainValidationResult invoiceReconciliationValidation;
        private List<String> allErrors = new ArrayList<>();
        private List<String> allWarnings = new ArrayList<>();

        // Getters and Setters
        public Long getOrderId() {
            return orderId;
        }

        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }

        public LocalDate getValidationTime() {
            return validationTime;
        }

        public void setValidationTime(LocalDate validationTime) {
            this.validationTime = validationTime;
        }

        public boolean isOverallValid() {
            return overallValid;
        }

        public void setOverallValid(boolean overallValid) {
            this.overallValid = overallValid;
        }

        public DataChainValidationResult getOrderAmountValidation() {
            return orderAmountValidation;
        }

        public void setOrderAmountValidation(DataChainValidationResult orderAmountValidation) {
            this.orderAmountValidation = orderAmountValidation;
        }

        public DataChainValidationResult getOrderOutboundValidation() {
            return orderOutboundValidation;
        }

        public void setOrderOutboundValidation(DataChainValidationResult orderOutboundValidation) {
            this.orderOutboundValidation = orderOutboundValidation;
        }

        public DataChainValidationResult getOutboundInvoiceValidation() {
            return outboundInvoiceValidation;
        }

        public void setOutboundInvoiceValidation(DataChainValidationResult outboundInvoiceValidation) {
            this.outboundInvoiceValidation = outboundInvoiceValidation;
        }

        public DataChainValidationResult getInvoiceReconciliationValidation() {
            return invoiceReconciliationValidation;
        }

        public void setInvoiceReconciliationValidation(DataChainValidationResult invoiceReconciliationValidation) {
            this.invoiceReconciliationValidation = invoiceReconciliationValidation;
        }

        public List<String> getAllErrors() {
            return allErrors;
        }

        public void setAllErrors(List<String> allErrors) {
            this.allErrors = allErrors;
        }

        public List<String> getAllWarnings() {
            return allWarnings;
        }

        public void setAllWarnings(List<String> allWarnings) {
            this.allWarnings = allWarnings;
        }

        // 便捷方法
        public void addError(String error) {
            this.allErrors.add(error);
            this.overallValid = false;
        }

        public void addWarning(String warning) {
            this.allWarnings.add(warning);
        }

        /**
         * 获取验证摘要
         */
        public String getValidationSummary() {
            StringBuilder summary = new StringBuilder();
            summary.append("数据链路验证摘要:\n");
            summary.append("- 总体结果: ").append(overallValid ? "通过" : "失败").append("\n");
            summary.append("- 错误数量: ").append(allErrors.size()).append("\n");
            summary.append("- 警告数量: ").append(allWarnings.size()).append("\n");

            if (orderAmountValidation != null) {
                summary.append("- 订单金额一致性: ").append(orderAmountValidation.isValid() ? "通过" : "失败").append("\n");
            }
            if (orderOutboundValidation != null) {
                summary.append("- 订单出库一致性: ").append(orderOutboundValidation.isValid() ? "通过" : "失败").append("\n");
            }
            if (outboundInvoiceValidation != null) {
                summary.append("- 出库开票一致性: ").append(outboundInvoiceValidation.isValid() ? "通过" : "失败").append("\n");
            }
            if (invoiceReconciliationValidation != null) {
                summary.append("- 发票对账一致性: ").append(invoiceReconciliationValidation.isValid() ? "通过" : "失败").append("\n");
            }

            return summary.toString();
        }
    }

    /**
     * 数据链路验证统计信息
     */
    class DataChainValidationStatistics {
        private int totalValidations;
        private int passedValidations;
        private int failedValidations;
        private int totalErrors;
        private int totalWarnings;
        private LocalDate statisticsDate;

        // Getters and Setters
        public int getTotalValidations() {
            return totalValidations;
        }

        public void setTotalValidations(int totalValidations) {
            this.totalValidations = totalValidations;
        }

        public int getPassedValidations() {
            return passedValidations;
        }

        public void setPassedValidations(int passedValidations) {
            this.passedValidations = passedValidations;
        }

        public int getFailedValidations() {
            return failedValidations;
        }

        public void setFailedValidations(int failedValidations) {
            this.failedValidations = failedValidations;
        }

        public int getTotalErrors() {
            return totalErrors;
        }

        public void setTotalErrors(int totalErrors) {
            this.totalErrors = totalErrors;
        }

        public int getTotalWarnings() {
            return totalWarnings;
        }

        public void setTotalWarnings(int totalWarnings) {
            this.totalWarnings = totalWarnings;
        }

        public LocalDate getStatisticsDate() {
            return statisticsDate;
        }

        public void setStatisticsDate(LocalDate statisticsDate) {
            this.statisticsDate = statisticsDate;
        }

        /**
         * 计算通过率
         */
        public BigDecimal getPassRate() {
            if (totalValidations == 0) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(passedValidations)
                .divide(new BigDecimal(totalValidations), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        }

    }
}
