package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.FinArReceivableBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.wms.enums.SourceType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 应收单Service接口
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface IFinArReceivableService {

    /**
     * 查询应收单
     *
     * @param receivableId 主键
     * @return 应收单
     */
    FinArReceivableVo queryById(Long receivableId);

    /**
     * 分页查询应收单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应收单分页列表
     */
    TableDataInfo<FinArReceivableVo> queryPageList(FinArReceivableBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应收单列表
     *
     * @param bo 查询条件
     * @return 应收单列表
     */
    List<FinArReceivableVo> queryList(FinArReceivableBo bo);

    /**
     * 新增应收单
     *
     * @param bo 应收单
     * @return 是否新增成功
     */
    Boolean insertByBo(FinArReceivableBo bo);

    /**
     * 修改应收单
     *
     * @param bo 应收单
     * @return 是否修改成功
     */
    Boolean updateByBo(FinArReceivableBo bo);

    /**
     * 校验并批量删除应收单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 从销售出库单生成应收账款
     *
     * @param outboundId     出库单ID
     * @param receivableType 应收类型
     * @param dueDate        到期日期
     * @param paymentTerms   付款条件
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 应收账款ID
     */
    Long generateFromSaleOutbound(Long outboundId, String receivableType, LocalDate dueDate,
                                  String paymentTerms, Long operatorId, String operatorName);

    /**
     * 批量从销售出库单生成应收账款
     *
     * @param outboundIds    出库单ID列表
     * @param receivableType 应收类型
     * @param dueDate        到期日期
     * @param paymentTerms   付款条件
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 批量生成结果
     */
    Map<String, Object> batchGenerateFromSaleOutbounds(List<Long> outboundIds, String receivableType,
                                                       LocalDate dueDate, String paymentTerms,
                                                       Long operatorId, String operatorName);

    /**
     * 检查出库单是否已生成应收账款
     *
     * @param outboundId 出库单ID
     * @return 是否已生成应收账款
     */
    Boolean existsByOutboundId(Long outboundId);

    /**
     * 检查销售订单是否已生成应收账款
     *
     * @param orderId 销售订单ID
     * @return 是否已生成应收账款
     */
    Boolean existsByOrderId(Long orderId);

    /**
     * 根据出库单ID查询应收账款
     *
     * @param outboundId 出库单ID
     * @return 应收账款信息
     */
    FinArReceivableVo queryByOutboundId(Long outboundId);

    /**
     * 更新应收账款状态
     *
     * @param receivableId 应收账款ID
     * @param newStatus    新状态
     * @return 是否更新成功
     */
    Boolean updateReceivableStatus(Long receivableId, String newStatus);

    /**
     * 获取客户应收账款汇总
     *
     * @param customerId 客户ID
     * @return 应收账款汇总信息
     */
    Map<String, Object> getCustomerReceivableSummary(Long customerId);

    /**
     * 从销售订单明细生成应收单明细
     *
     * @param receivableId     应收单ID
     * @param saleOrderItemIds 销售订单明细ID列表
     * @param operatorId       操作人ID
     * @param operatorName     操作人姓名
     * @return 是否生成成功
     */
    Boolean generateReceivableItemsFromSaleOrderItems(Long receivableId, List<Long> saleOrderItemIds,
                                                      Long operatorId, String operatorName);

    /**
     * 从销售出库单生成应收单（重载方法）
     *
     * @param outboundId   出库单ID
     * @param orderId      销售订单ID
     * @param customerId   客户ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 应收单ID
     */
    Long generateFromSaleOutbound(Long outboundId, Long orderId, Long customerId,
                                  Long operatorId, String operatorName);

    /**
     * 从出库单明细生成应收单明细
     *
     * @param receivableId    应收单ID
     * @param outboundItemIds 出库单明细ID列表
     * @param operatorId      操作人ID
     * @param operatorName    操作人姓名
     * @return 是否生成成功
     */
    Boolean generateReceivableItemsFromOutboundItems(Long receivableId, List<Long> outboundItemIds,
                                                     Long operatorId, String operatorName);

    /**
     * 从应收单生成收款单
     *
     * @param receivableId  应收单ID
     * @param receiptAmount 收款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 收款单ID
     */
    Long generateReceiptOrderFromReceivable(Long receivableId, BigDecimal receiptAmount,
                                            Long accountId, Long operatorId, String operatorName);

    /**
     * 从销售订单生成应收单
     *
     * @param orderId      销售订单ID
     * @param orderCode    销售订单编号
     * @param customerId   客户ID
     * @param customerName 客户名称
     * @param amount       应收金额
     * @param dueDate      到期日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 应收单ID
     */
    Long generateFromSaleOrder(Long orderId, String orderCode, Long customerId,
                               String customerName, BigDecimal amount, LocalDate dueDate,
                               Long operatorId, String operatorName);

    /**
     * 根据来源ID和来源类型查询应收单列表
     *
     * @param sourceId   来源ID
     * @param sourceType 来源类型
     * @return 应收单列表
     */
    List<FinArReceivableVo> queryBySourceId(Long sourceId, SourceType sourceType);

    /**
     * 更新应收单收款后的状态
     *
     * @param receivableId  应收单ID
     * @param paymentAmount 收款金额
     * @return 是否更新成功
     */
    Boolean updateStatusAfterPayment(Long receivableId, BigDecimal paymentAmount);
}
