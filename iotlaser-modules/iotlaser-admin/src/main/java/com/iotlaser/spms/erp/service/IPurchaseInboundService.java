package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.PurchaseInboundBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 采购入库Service接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IPurchaseInboundService {

    /**
     * 查询采购入库
     *
     * @param inboundId 主键
     * @return 采购入库
     */
    PurchaseInboundVo queryById(Long inboundId);

    /**
     * 分页查询采购入库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购入库分页列表
     */
    TableDataInfo<PurchaseInboundVo> queryPageList(PurchaseInboundBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的采购入库列表
     *
     * @param bo 查询条件
     * @return 采购入库列表
     */
    List<PurchaseInboundVo> queryList(PurchaseInboundBo bo);

    /**
     * 新增采购入库
     *
     * @param bo 采购入库
     * @return 是否新增成功
     */
    PurchaseInboundVo insertByBo(PurchaseInboundBo bo);

    /**
     * 修改采购入库
     *
     * @param bo 采购入库
     * @return 是否修改成功
     */
    PurchaseInboundVo updateByBo(PurchaseInboundBo bo);

    /**
     * 校验并批量删除采购入库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认采购入库单
     *
     * @param inboundId 入库单ID
     * @return 是否确认成功
     */
    Boolean confirmInbound(Long inboundId);

    /**
     * 创建仓库入库单
     *
     * @param inboundId 采购入库ID
     * @return 是否创建成功
     */
    Boolean createInbound(Long inboundId);

    /**
     * 创建采购退货单
     *
     * @param inboundId 采购入库ID
     * @return 是否创建成功
     */
    Boolean createPurchaseReturn(Long inboundId);

    /**
     * 批量确认采购入库单
     *
     * @param inboundIds 入库单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmInbounds(Collection<Long> inboundIds);

    /**
     * 完成采购入库
     *
     * @param inboundId 入库单ID
     * @return 是否完成成功
     */
    Boolean completeInbound(Long inboundId);

    /**
     * 根据采购订单创建采购入库单
     *
     * @param orderVo 采购订单
     * @return 创建的入库单
     */
    Boolean createFromPurchaseOrder(PurchaseOrderVo orderVo);

    /**
     * 检查是否存在指定订单ID的入库单
     *
     * @param orderId 订单ID
     * @return 是否存在
     */
    Boolean existsByOrderId(Long orderId);

    /**
     * 入库完成后自动生成应付单
     *
     * @param inboundId    入库单ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否生成成功
     */
    Boolean generateInvoiceAfterInboundComplete(Long inboundId, Long operatorId, String operatorName);

    /**
     * 根据采购订单ID查询采购入库单列表
     *
     * @param orderId 采购订单ID
     * @return 采购入库单列表
     */
    List<PurchaseInboundVo> selectListByPurchaseOrderId(Long orderId);

    /**
     * 采购业务完整流程：从入库完成到付款出账
     *
     * @param inboundId     入库单ID
     * @param paymentAmount 付款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 完整的业务结果
     */
    Map<String, Object> completePurchaseBusinessFlow(Long inboundId, BigDecimal paymentAmount,
                                                     Long accountId, Long operatorId, String operatorName);

}
