package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.SaleOrderItemBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 销售订单明细Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface ISaleOrderItemService {

    /**
     * 查询销售订单明细
     *
     * @param itemId 主键
     * @return 销售订单明细
     */
    SaleOrderItemVo queryById(Long itemId);

    /**
     * 分页查询销售订单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售订单明细分页列表
     */
    TableDataInfo<SaleOrderItemVo> queryPageList(SaleOrderItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的销售订单明细列表
     *
     * @param bo 查询条件
     * @return 销售订单明细列表
     */
    List<SaleOrderItemVo> queryList(SaleOrderItemBo bo);

    /**
     * 新增销售订单明细
     *
     * @param bo 销售订单明细
     * @return 是否新增成功
     */
    Boolean insertByBo(SaleOrderItemBo bo);

    /**
     * 修改销售订单明细
     *
     * @param bo 销售订单明细
     * @return 是否修改成功
     */
    Boolean updateByBo(SaleOrderItemBo bo);

    /**
     * 校验并批量删除销售订单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量新增或更新销售订单明细
     *
     * @param items 销售订单明细列表
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<SaleOrderItemBo> items);

    /**
     * 根据订单ID查询明细列表
     *
     * @param orderId 订单ID
     * @return 明细列表
     */
    List<SaleOrderItemVo> queryByOrderId(Long orderId);


    /**
     * 查询销售订单明细表及其关联信息
     *
     * @param itemId 主键
     * @return 销售订单明细表
     */
    SaleOrderItemVo queryByIdWith(Long itemId);

    /**
     * 分页查询销售订单明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售订单明细表分页列表
     */
    TableDataInfo<SaleOrderItemVo> queryPageListWith(SaleOrderItemBo bo, PageQuery pageQuery);


}
