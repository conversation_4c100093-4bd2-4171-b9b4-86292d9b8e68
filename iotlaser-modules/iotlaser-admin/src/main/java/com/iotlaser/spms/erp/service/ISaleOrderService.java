package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.SaleOrderBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 销售订单Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface ISaleOrderService {

    /**
     * 查询销售订单
     *
     * @param orderId 主键
     * @return 销售订单
     */
    SaleOrderVo queryById(Long orderId);

    /**
     * 分页查询销售订单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售订单分页列表
     */
    TableDataInfo<SaleOrderVo> queryPageList(SaleOrderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的销售订单列表
     *
     * @param bo 查询条件
     * @return 销售订单列表
     */
    List<SaleOrderVo> queryList(SaleOrderBo bo);

    /**
     * 新增销售订单
     *
     * @param bo 销售订单
     * @return 是否新增成功
     */
    SaleOrderVo insertByBo(SaleOrderBo bo);

    /**
     * 修改销售订单
     *
     * @param bo 销售订单
     * @return 是否修改成功
     */
    SaleOrderVo updateByBo(SaleOrderBo bo);

    /**
     * 校验并批量删除销售订单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认销售订单
     *
     * @param orderId 订单ID
     * @return 是否确认成功
     */
    Boolean confirmOrder(Long orderId);

    /**
     * 批量确认销售订单
     *
     * @param orderIds 订单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmOrders(Collection<Long> orderIds);

    /**
     * 挂起销售订单
     *
     * @param orderId    订单ID
     * @param holdReason 挂起原因
     * @return 是否挂起成功
     */
    Boolean holdOrder(Long orderId, String holdReason);

    /**
     * 恢复挂起的销售订单
     *
     * @param orderId 订单ID
     * @return 是否恢复成功
     */
    Boolean resumeOrder(Long orderId);

    /**
     * 取消销售订单
     *
     * @param orderId      订单ID
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    Boolean cancelOrder(Long orderId, String cancelReason);

    /**
     * 关闭销售订单
     *
     * @param orderId 订单ID
     * @return 是否关闭成功
     */
    Boolean closeOrder(Long orderId);

    /**
     * 销售订单财务对账
     *
     * @param saleOrderId 销售订单ID
     * @return 对账结果
     */
    Map<String, Object> reconcileSaleOrder(Long saleOrderId);

    /**
     * 创建出库单
     *
     * @param orderId 销售订单ID
     * @return 是否创建成功
     */
    Boolean createOutbound(Long orderId);

    /**
     * 批量创建出库单
     *
     * @param orderIds 销售订单ID列表
     * @return 创建成功的数量
     */
    Integer batchCreateOutbound(List<Long> orderIds);

    /**
     * 从销售订单生成应收单
     *
     * @param orderId 销售订单ID
     * @return 是否生成成功
     */
    Boolean generateReceivableFromOrder(Long orderId);
}
