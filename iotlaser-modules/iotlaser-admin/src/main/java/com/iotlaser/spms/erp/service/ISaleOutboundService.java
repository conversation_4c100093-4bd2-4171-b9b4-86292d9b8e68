package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.domain.bo.SaleOutboundBo;
import com.iotlaser.spms.erp.domain.vo.SaleOrderVo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 销售出库Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025-05-10
 */
public interface ISaleOutboundService {

    /**
     * 查询销售出库
     *
     * @param outboundId 主键
     * @return 销售出库
     */
    SaleOutboundVo queryById(Long outboundId);

    /**
     * 分页查询销售出库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 销售出库分页列表
     */
    TableDataInfo<SaleOutboundVo> queryPageList(SaleOutboundBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的销售出库列表
     *
     * @param bo 查询条件
     * @return 销售出库列表
     */
    List<SaleOutboundVo> queryList(SaleOutboundBo bo);

    /**
     * 新增销售出库
     *
     * @param bo 销售出库
     * @return 是否新增成功
     */
    SaleOutboundVo insertByBo(SaleOutboundBo bo);

    /**
     * 修改销售出库
     *
     * @param bo 销售出库
     * @return 是否修改成功
     */
    SaleOutboundVo updateByBo(SaleOutboundBo bo);

    /**
     * 校验并批量删除销售出库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认销售出库单
     *
     * @param outboundId 出库单ID
     * @return 是否确认成功
     */
    Boolean confirmOutbound(Long outboundId);

    /**
     * 批量确认销售出库单
     *
     * @param outboundIds 出库单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmOutbounds(Collection<Long> outboundIds);

    /**
     * 取消销售出库单
     *
     * @param outboundId 出库单ID
     * @param reason     取消原因
     * @return 是否取消成功
     */
    Boolean cancelOutbound(Long outboundId, String reason);

    /**
     * 完成销售出库
     *
     * @param outboundId 出库单ID
     * @return 是否完成成功
     */
    Boolean completeOutbound(Long outboundId);

    /**
     * 创建仓库出库单
     *
     * @param outboundId 出库单ID
     * @return 是否创建成功
     */
    Boolean createOutbound(Long outboundId);

    /**
     * 创建销售退货
     *
     * @param outboundId 出库单ID
     * @return 是否创建成功
     */
    Boolean createReturn(Long outboundId);

    /**
     * 根据销售订单创建出库单
     *
     * @param orderVo 销售订单
     * @return 创建的出库单
     */
    Boolean createFromSaleOrder(SaleOrderVo orderVo);

    /**
     * 检查是否有关联的出库单
     *
     * @param orderId 销售订单ID
     * @return 是否存在
     */
    Boolean existsByOrderId(Long orderId);

    /**
     * 出库完成后自动生成应收单
     *
     * @param outboundId   出库单ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否生成成功
     */
    Boolean generateReceivableAfterOutboundComplete(Long outboundId, Long operatorId, String operatorName);

    /**
     * 根据销售订单ID查询销售出库单列表
     *
     * @param orderId 销售订单ID
     * @return 销售出库单列表
     */
    List<SaleOutboundVo> queryByOrderId(Long orderId);

    /**
     * 销售业务完整流程：从出库完成到收款入账
     *
     * @param outboundId    出库单ID
     * @param receiptAmount 收款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 完整的业务结果
     */
    Map<String, Object> completeSaleBusinessFlow(Long outboundId, BigDecimal receiptAmount,
                                                 Long accountId, Long operatorId, String operatorName);
}
