package com.iotlaser.spms.erp.service;

import com.iotlaser.spms.erp.service.IDataChainValidationService.DataChainValidationResult;

import java.math.BigDecimal;

/**
 * 仓储管理数据链路验证服务接口
 * 专门用于验证仓储管理模块与财务系统的数据传递完整性和一致性
 *
 * <AUTHOR> Agent
 * @date 2025-06-24
 */
public interface IWarehouseDataChainValidationService {

    /**
     * 验证采购订单→采购入库→仓库入库的数据链路
     *
     * @param purchaseOrderId 采购订单ID
     * @return 验证结果
     */
    DataChainValidationResult validatePurchaseInboundChain(Long purchaseOrderId);

    /**
     * 验证销售订单→销售出库→仓库出库的数据链路
     *
     * @param saleOrderId 销售订单ID
     * @return 验证结果
     */
    DataChainValidationResult validateSaleOutboundChain(Long saleOrderId);

    /**
     * 验证库存移库操作的数据一致性
     *
     * @param transferId 移库单ID
     * @return 验证结果
     */
    DataChainValidationResult validateTransferConsistency(Long transferId);

    /**
     * 验证库存管理的完整性
     *
     * @param productId  产品ID
     * @param locationId 库位ID (可为null，表示验证该产品在所有库位的批次)
     * @return 验证结果
     */
    DataChainValidationResult validateInventoryIntegrity(Long productId, Long locationId);

    /**
     * 验证仓储与财务系统的数据传递
     *
     * @param warehouseOperationId 仓储操作ID
     * @param operationType        操作类型 (INBOUND/OUTBOUND/TRANSFER)
     * @return 验证结果
     */
    default DataChainValidationResult validateWarehouseFinanceIntegration(Long warehouseOperationId, String operationType) {
        DataChainValidationResult result = new DataChainValidationResult();
        result.setValidationType("WAREHOUSE_FINANCE_INTEGRATION");
        result.setTargetId(warehouseOperationId);
        result.addWarning("仓储与财务系统集成验证功能待实现");
        return result;
    }

    /**
     * 验证完整的仓储数据链路
     *
     * @param sourceOrderId   源订单ID
     * @param sourceOrderType 源订单类型 (PURCHASE_ORDER/SALE_ORDER)
     * @return 完整验证结果
     */
    default CompleteWarehouseDataChainValidationResult validateCompleteWarehouseDataChain(Long sourceOrderId, String sourceOrderType) {
        CompleteWarehouseDataChainValidationResult completeResult = new CompleteWarehouseDataChainValidationResult();
        completeResult.setSourceOrderId(sourceOrderId);
        completeResult.setSourceOrderType(sourceOrderType);
        completeResult.setValidationTime(java.time.LocalDate.now());

        try {
            if ("PURCHASE_ORDER".equals(sourceOrderType)) {
                // 验证采购链路
                DataChainValidationResult purchaseInboundResult = validatePurchaseInboundChain(sourceOrderId);
                completeResult.setPurchaseInboundValidation(purchaseInboundResult);

                // TODO: 验证采购入库与应付发票的关联
                DataChainValidationResult financeIntegrationResult = validateWarehouseFinanceIntegration(sourceOrderId, "INBOUND");
                completeResult.setFinanceIntegrationValidation(financeIntegrationResult);

            } else if ("SALE_ORDER".equals(sourceOrderType)) {
                // 验证销售链路
                DataChainValidationResult saleOutboundResult = validateSaleOutboundChain(sourceOrderId);
                completeResult.setSaleOutboundValidation(saleOutboundResult);

                // TODO: 验证销售出库与应收发票的关联
                DataChainValidationResult financeIntegrationResult = validateWarehouseFinanceIntegration(sourceOrderId, "OUTBOUND");
                completeResult.setFinanceIntegrationValidation(financeIntegrationResult);
            }

            // 计算总体验证结果
            boolean overallValid = true;
            if (completeResult.getPurchaseInboundValidation() != null) {
                overallValid &= completeResult.getPurchaseInboundValidation().isValid();
            }
            if (completeResult.getSaleOutboundValidation() != null) {
                overallValid &= completeResult.getSaleOutboundValidation().isValid();
            }
            if (completeResult.getFinanceIntegrationValidation() != null) {
                overallValid &= completeResult.getFinanceIntegrationValidation().isValid();
            }

            completeResult.setOverallValid(overallValid);

        } catch (Exception e) {
            completeResult.setOverallValid(false);
            completeResult.addError("完整仓储验证过程异常: " + e.getMessage());
        }

        return completeResult;
    }

    /**
     * 完整仓储数据链路验证结果
     */
    class CompleteWarehouseDataChainValidationResult {
        private Long sourceOrderId;
        private String sourceOrderType;
        private java.time.LocalDate validationTime;
        private boolean overallValid;
        private DataChainValidationResult purchaseInboundValidation;
        private DataChainValidationResult saleOutboundValidation;
        private DataChainValidationResult transferValidation;
        private DataChainValidationResult inventoryValidation;
        private DataChainValidationResult financeIntegrationValidation;
        private java.util.List<String> allErrors = new java.util.ArrayList<>();
        private java.util.List<String> allWarnings = new java.util.ArrayList<>();

        // Getters and Setters
        public Long getSourceOrderId() {
            return sourceOrderId;
        }

        public void setSourceOrderId(Long sourceOrderId) {
            this.sourceOrderId = sourceOrderId;
        }

        public String getSourceOrderType() {
            return sourceOrderType;
        }

        public void setSourceOrderType(String sourceOrderType) {
            this.sourceOrderType = sourceOrderType;
        }

        public java.time.LocalDate getValidationTime() {
            return validationTime;
        }

        public void setValidationTime(java.time.LocalDate validationTime) {
            this.validationTime = validationTime;
        }

        public boolean isOverallValid() {
            return overallValid;
        }

        public void setOverallValid(boolean overallValid) {
            this.overallValid = overallValid;
        }

        public DataChainValidationResult getPurchaseInboundValidation() {
            return purchaseInboundValidation;
        }

        public void setPurchaseInboundValidation(DataChainValidationResult purchaseInboundValidation) {
            this.purchaseInboundValidation = purchaseInboundValidation;
        }

        public DataChainValidationResult getSaleOutboundValidation() {
            return saleOutboundValidation;
        }

        public void setSaleOutboundValidation(DataChainValidationResult saleOutboundValidation) {
            this.saleOutboundValidation = saleOutboundValidation;
        }

        public DataChainValidationResult getTransferValidation() {
            return transferValidation;
        }

        public void setTransferValidation(DataChainValidationResult transferValidation) {
            this.transferValidation = transferValidation;
        }

        public DataChainValidationResult getInventoryValidation() {
            return inventoryValidation;
        }

        public void setInventoryValidation(DataChainValidationResult inventoryValidation) {
            this.inventoryValidation = inventoryValidation;
        }

        public DataChainValidationResult getFinanceIntegrationValidation() {
            return financeIntegrationValidation;
        }

        public void setFinanceIntegrationValidation(DataChainValidationResult financeIntegrationValidation) {
            this.financeIntegrationValidation = financeIntegrationValidation;
        }

        public java.util.List<String> getAllErrors() {
            return allErrors;
        }

        public void setAllErrors(java.util.List<String> allErrors) {
            this.allErrors = allErrors;
        }

        public java.util.List<String> getAllWarnings() {
            return allWarnings;
        }

        public void setAllWarnings(java.util.List<String> allWarnings) {
            this.allWarnings = allWarnings;
        }

        // 便捷方法
        public void addError(String error) {
            this.allErrors.add(error);
            this.overallValid = false;
        }

        public void addWarning(String warning) {
            this.allWarnings.add(warning);
        }

        /**
         * 获取仓储验证摘要
         */
        public String getWarehouseValidationSummary() {
            StringBuilder summary = new StringBuilder();
            summary.append("仓储数据链路验证摘要:\n");
            summary.append("- 源订单类型: ").append(sourceOrderType).append("\n");
            summary.append("- 总体结果: ").append(overallValid ? "通过" : "失败").append("\n");
            summary.append("- 错误数量: ").append(allErrors.size()).append("\n");
            summary.append("- 警告数量: ").append(allWarnings.size()).append("\n");

            if (purchaseInboundValidation != null) {
                summary.append("- 采购入库链路: ").append(purchaseInboundValidation.isValid() ? "通过" : "失败").append("\n");
            }
            if (saleOutboundValidation != null) {
                summary.append("- 销售出库链路: ").append(saleOutboundValidation.isValid() ? "通过" : "失败").append("\n");
            }
            if (transferValidation != null) {
                summary.append("- 库存移库: ").append(transferValidation.isValid() ? "通过" : "失败").append("\n");
            }
            if (inventoryValidation != null) {
                summary.append("- 库存管理: ").append(inventoryValidation.isValid() ? "通过" : "失败").append("\n");
            }
            if (financeIntegrationValidation != null) {
                summary.append("- 财务系统集成: ").append(financeIntegrationValidation.isValid() ? "通过" : "失败").append("\n");
            }

            return summary.toString();
        }
    }

    /**
     * 仓储数据链路验证统计信息
     */
    class WarehouseDataChainValidationStatistics {
        private int totalWarehouseValidations;
        private int passedWarehouseValidations;
        private int failedWarehouseValidations;
        private int inboundValidations;
        private int outboundValidations;
        private int transferValidations;
        private int batchValidations;
        private java.time.LocalDate statisticsDate;

        // Getters and Setters
        public int getTotalWarehouseValidations() {
            return totalWarehouseValidations;
        }

        public void setTotalWarehouseValidations(int totalWarehouseValidations) {
            this.totalWarehouseValidations = totalWarehouseValidations;
        }

        public int getPassedWarehouseValidations() {
            return passedWarehouseValidations;
        }

        public void setPassedWarehouseValidations(int passedWarehouseValidations) {
            this.passedWarehouseValidations = passedWarehouseValidations;
        }

        public int getFailedWarehouseValidations() {
            return failedWarehouseValidations;
        }

        public void setFailedWarehouseValidations(int failedWarehouseValidations) {
            this.failedWarehouseValidations = failedWarehouseValidations;
        }

        public int getInboundValidations() {
            return inboundValidations;
        }

        public void setInboundValidations(int inboundValidations) {
            this.inboundValidations = inboundValidations;
        }

        public int getOutboundValidations() {
            return outboundValidations;
        }

        public void setOutboundValidations(int outboundValidations) {
            this.outboundValidations = outboundValidations;
        }

        public int getTransferValidations() {
            return transferValidations;
        }

        public void setTransferValidations(int transferValidations) {
            this.transferValidations = transferValidations;
        }

        public int getBatchValidations() {
            return batchValidations;
        }

        public void setBatchValidations(int batchValidations) {
            this.batchValidations = batchValidations;
        }

        public java.time.LocalDate getStatisticsDate() {
            return statisticsDate;
        }

        public void setStatisticsDate(java.time.LocalDate statisticsDate) {
            this.statisticsDate = statisticsDate;
        }

        /**
         * 计算仓储验证通过率
         */
        public BigDecimal getWarehousePassRate() {
            if (totalWarehouseValidations == 0) {
                return java.math.BigDecimal.ZERO;
            }
            return java.math.BigDecimal.valueOf(passedWarehouseValidations)
                .divide(java.math.BigDecimal.valueOf(totalWarehouseValidations), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(java.math.BigDecimal.valueOf(100));
        }
    }
}
