package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinAccountLedger;
import com.iotlaser.spms.erp.domain.bo.FinAccountLedgerBo;
import com.iotlaser.spms.erp.domain.vo.FinAccountLedgerVo;
import com.iotlaser.spms.erp.domain.vo.FinAccountVo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentOrderVo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptOrderVo;
import com.iotlaser.spms.erp.mapper.FinAccountLedgerMapper;
import com.iotlaser.spms.erp.service.IFinAccountLedgerService;
import com.iotlaser.spms.erp.service.IFinAccountService;
import com.iotlaser.spms.erp.service.IFinApPaymentOrderService;
import com.iotlaser.spms.erp.service.IFinArReceiptOrderService;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 账户收支流水Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinAccountLedgerServiceImpl implements IFinAccountLedgerService {

    private final FinAccountLedgerMapper baseMapper;
    private final IFinAccountService finAccountService;
    private final IFinArReceiptOrderService finArReceiptOrderService;
    private final IFinApPaymentOrderService finApPaymentOrderService;

    /**
     * 查询账户收支流水
     *
     * @param ledgerId 主键
     * @return 账户收支流水
     */
    @Override
    public FinAccountLedgerVo queryById(Long ledgerId) {
        return baseMapper.selectVoById(ledgerId);
    }

    /**
     * 分页查询账户收支流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 账户收支流水分页列表
     */
    @Override
    public TableDataInfo<FinAccountLedgerVo> queryPageList(FinAccountLedgerBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinAccountLedger> lqw = buildQueryWrapper(bo);
        Page<FinAccountLedgerVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的账户收支流水列表
     *
     * @param bo 查询条件
     * @return 账户收支流水列表
     */
    @Override
    public List<FinAccountLedgerVo> queryList(FinAccountLedgerBo bo) {
        LambdaQueryWrapper<FinAccountLedger> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinAccountLedger> buildQueryWrapper(FinAccountLedgerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinAccountLedger> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinAccountLedger::getLedgerId);
        lqw.eq(bo.getAccountId() != null, FinAccountLedger::getAccountId, bo.getAccountId());
        lqw.eq(StringUtils.isNotBlank(bo.getBlankSerialNumber()), FinAccountLedger::getBlankSerialNumber, bo.getBlankSerialNumber());
        lqw.eq(bo.getTransactionTime() != null, FinAccountLedger::getTransactionTime, bo.getTransactionTime());
        lqw.eq(StringUtils.isNotBlank(bo.getDirection()), FinAccountLedger::getDirection, bo.getDirection());
        lqw.eq(bo.getAmount() != null, FinAccountLedger::getAmount, bo.getAmount());
        lqw.eq(bo.getBalanceBefore() != null, FinAccountLedger::getBalanceBefore, bo.getBalanceBefore());
        lqw.eq(bo.getBalanceAfter() != null, FinAccountLedger::getBalanceAfter, bo.getBalanceAfter());
        lqw.eq(StringUtils.isNotBlank(bo.getTransactionType()), FinAccountLedger::getTransactionType, bo.getTransactionType());
        lqw.eq(bo.getSourceId() != null, FinAccountLedger::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), FinAccountLedger::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(FinAccountLedger::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getPartnerId() != null, FinAccountLedger::getPartnerId, bo.getPartnerId());
        lqw.eq(StringUtils.isNotBlank(bo.getPartnerCode()), FinAccountLedger::getPartnerCode, bo.getPartnerCode());
        lqw.like(StringUtils.isNotBlank(bo.getPartnerName()), FinAccountLedger::getPartnerName, bo.getPartnerName());
        lqw.eq(StringUtils.isNotBlank(bo.getPartnerAccount()), FinAccountLedger::getPartnerAccount, bo.getPartnerAccount());
        // TODO: 需要在实体中新增handlerId和handlerName字段
        // lqw.eq(bo.getHandlerId() != null, FinAccountLedger::getHandlerId, bo.getHandlerId());
        // lqw.like(StringUtils.isNotBlank(bo.getHandlerName()), FinAccountLedger::getHandlerName, bo.getHandlerName());
        lqw.eq(StringUtils.isNotBlank(bo.getLedgerStatus()), FinAccountLedger::getLedgerStatus, bo.getLedgerStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinAccountLedger::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增账户收支流水
     *
     * @param bo 账户收支流水
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinAccountLedgerBo bo) {
        FinAccountLedger add = MapstructUtils.convert(bo, FinAccountLedger.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLedgerId(add.getLedgerId());
        }
        return flag;
    }

    /**
     * 修改账户收支流水
     *
     * @param bo 账户收支流水
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinAccountLedgerBo bo) {
        FinAccountLedger update = MapstructUtils.convert(bo, FinAccountLedger.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinAccountLedger entity) {
    }

    /**
     * 校验并批量删除账户收支流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验账户流水是否可以删除
            List<FinAccountLedger> ledgers = baseMapper.selectByIds(ids);
            for (FinAccountLedger ledger : ledgers) {
                // 检查流水状态，只有草稿状态的流水才能删除
                if ("CONFIRMED".equals(ledger.getLedgerStatus())) {
                    throw new ServiceException("账户流水【" + ledger.getSourceCode() + "】已确认，不允许删除");
                }

                // 检查是否有关联的业务单据
                if (ledger.getSourceId() != null && ledger.getSourceType() != null) {
                    throw new ServiceException("账户流水【" + ledger.getSourceCode() + "】存在关联业务单据，不允许删除");
                }

                log.info("删除账户流水校验通过：{}", ledger.getSourceCode());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除账户流水成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除账户流水失败：{}", e.getMessage(), e);
            throw new ServiceException("删除账户流水失败：" + e.getMessage());
        }
    }

    /**
     * 创建账户流水记录
     *
     * @param accountId       账户ID
     * @param amount          交易金额
     * @param direction       交易方向 (IN-收入, OUT-支出)
     * @param transactionType 交易类型
     * @param sourceId        来源ID
     * @param sourceCode      来源编号
     * @param sourceType      来源类型
     * @param remark          备注
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createLedgerRecord(Long accountId, BigDecimal amount, String direction,
                                      String transactionType, Long sourceId, String sourceCode,
                                      SourceType sourceType, String remark) {
        try {
            // 获取账户信息
            // TODO: finAccountService.selectById方法可能不存在，使用queryById方法替代
            FinAccountVo accountVo = finAccountService.queryById(accountId);
            if (accountVo == null) {
                throw new ServiceException("账户不存在");
            }
            // 从VO对象获取当前余额
            BigDecimal balanceBefore = accountVo.getCurrentBalance();

            // balanceBefore已在上面获取
            BigDecimal balanceAfter;

            // 计算交易后余额
            if ("IN".equals(direction)) {
                balanceAfter = balanceBefore.add(amount);
            } else if ("OUT".equals(direction)) {
                balanceAfter = balanceBefore.subtract(amount);
                if (balanceAfter.compareTo(BigDecimal.ZERO) < 0) {
                    throw new ServiceException("账户余额不足");
                }
            } else {
                throw new ServiceException("无效的交易方向");
            }

            // 创建流水记录
            FinAccountLedger ledger = new FinAccountLedger();
            ledger.setAccountId(accountId);
            ledger.setDirection(direction);
            ledger.setAmount(amount);
            ledger.setBalanceBefore(balanceBefore);
            ledger.setBalanceAfter(balanceAfter);
            ledger.setTransactionType(transactionType);
            ledger.setSourceId(sourceId);
            ledger.setSourceCode(sourceCode);
            ledger.setSourceType(sourceType);
            ledger.setLedgerStatus("CONFIRMED");
            ledger.setRemark(remark);

            boolean ledgerResult = baseMapper.insert(ledger) > 0;

            // 更新账户余额
            // ✅ 修复：finAccountService.updateAccountBalance方法确实存在，恢复调用
            BigDecimal balanceChangeAmount = "IN".equals(direction) ? amount : amount.negate();
            boolean balanceResult = finAccountService.updateAccountBalance(accountId, balanceChangeAmount);

            if (ledgerResult && balanceResult) {
                log.info("账户流水记录创建成功 - 账户ID: {}, 金额: {}, 方向: {}, 余额: {} -> {}",
                    accountId, amount, direction, balanceBefore, balanceAfter);
                return true;
            } else {
                throw new ServiceException("流水记录创建失败");
            }
        } catch (Exception e) {
            log.error("账户流水记录创建失败 - 账户ID: {}, 金额: {}, 错误: {}",
                accountId, amount, e.getMessage(), e);
            throw new ServiceException("流水记录创建失败：" + e.getMessage());
        }
    }

    /**
     * 从收款单生成收入流水
     *
     * @param receiptId 收款单ID
     * @param accountId 账户ID
     * @param remark    备注
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateIncomeFromReceiptOrder(Long receiptId, Long accountId, String remark) {
        try {
            if (receiptId == null) {
                throw new ServiceException("收款单ID不能为空");
            }
            if (accountId == null) {
                throw new ServiceException("账户ID不能为空");
            }

            // ✅ 修复：获取收款单信息
            FinArReceiptOrderVo receipt = finArReceiptOrderService.queryById(receiptId);
            if (receipt == null) {
                throw new ServiceException("收款单不存在");
            }

            // 检查是否已生成流水
            LambdaQueryWrapper<FinAccountLedger> checkWrapper = Wrappers.lambdaQuery();
            checkWrapper.eq(FinAccountLedger::getSourceId, receiptId);
            checkWrapper.eq(FinAccountLedger::getSourceType, "RECEIPT_ORDER");
            checkWrapper.eq(FinAccountLedger::getAccountId, accountId);
            if (baseMapper.exists(checkWrapper)) {
                throw new ServiceException("该收款单已生成账户流水");
            }

            // ✅ 修复：获取收款单信息并创建流水
            BigDecimal amount = receipt.getPaymentAmount();
            String receiptCode = receipt.getReceiptCode();

            // 创建收入流水记录
            boolean result = createLedgerRecord(
                accountId,
                amount,
                "IN", // 收入方向
                "RECEIPT", // 交易类型：收款
                receiptId,
                receiptCode,
                SourceType.RECEIPT_ORDER, // 来源类型：收款单
                StringUtils.isNotBlank(remark) ? remark : "收款单生成收入流水"
            );

            if (result) {
                log.info("从收款单生成收入流水成功 - 收款单ID: {}, 账户ID: {}, 金额: {}",
                    receiptId, accountId, amount);
            }

            return result;
        } catch (Exception e) {
            log.error("从收款单生成收入流水失败 - 收款单ID: {}, 账户ID: {}, 错误: {}",
                receiptId, accountId, e.getMessage(), e);
            throw new ServiceException("生成收入流水失败：" + e.getMessage());
        }
    }

    /**
     * 从付款单生成支出流水
     *
     * @param paymentId 付款单ID
     * @param accountId 账户ID
     * @param remark    备注
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateExpenseFromPaymentOrder(Long paymentId, Long accountId, String remark) {
        try {
            if (paymentId == null) {
                throw new ServiceException("付款单ID不能为空");
            }
            if (accountId == null) {
                throw new ServiceException("账户ID不能为空");
            }

            // ✅ 修复：获取付款单信息
            FinApPaymentOrderVo payment = finApPaymentOrderService.queryById(paymentId);
            if (payment == null) {
                throw new ServiceException("付款单不存在");
            }

            // 检查是否已生成流水
            LambdaQueryWrapper<FinAccountLedger> checkWrapper = Wrappers.lambdaQuery();
            checkWrapper.eq(FinAccountLedger::getSourceId, paymentId);
            checkWrapper.eq(FinAccountLedger::getSourceType, "PAYMENT_ORDER");
            checkWrapper.eq(FinAccountLedger::getAccountId, accountId);
            if (baseMapper.exists(checkWrapper)) {
                throw new ServiceException("该付款单已生成账户流水");
            }

            // ✅ 修复：获取付款单信息并创建流水
            BigDecimal amount = payment.getPaymentAmount();
            String paymentCode = payment.getPaymentCode();

            // 创建支出流水记录
            boolean result = createLedgerRecord(
                accountId,
                amount,
                "OUT", // 支出方向
                "PAYMENT", // 交易类型：付款
                paymentId,
                paymentCode,
                SourceType.PAYMENT_ORDER, // 来源类型：付款单
                StringUtils.isNotBlank(remark) ? remark : "付款单生成支出流水"
            );

            if (result) {
                log.info("从付款单生成支出流水成功 - 付款单ID: {}, 账户ID: {}, 金额: {}",
                    paymentId, accountId, amount);
            }

            return result;
        } catch (Exception e) {
            log.error("从付款单生成支出流水失败 - 付款单ID: {}, 账户ID: {}, 错误: {}",
                paymentId, accountId, e.getMessage(), e);
            throw new ServiceException("生成支出流水失败：" + e.getMessage());
        }
    }

    /**
     * 查询来源单据的流水记录
     *
     * @param sourceId   来源单据ID
     * @param sourceType 来源单据类型
     * @return 流水记录列表
     */
    @Override
    public List<FinAccountLedgerVo> queryBySource(Long sourceId, SourceType sourceType) {
        LambdaQueryWrapper<FinAccountLedger> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinAccountLedger::getSourceId, sourceId);
        if (sourceType != null) {
            wrapper.eq(FinAccountLedger::getSourceType, sourceType);
        }
        wrapper.eq(FinAccountLedger::getStatus, "1"); // 有效状态
        wrapper.orderByDesc(FinAccountLedger::getTransactionTime);

        List<FinAccountLedger> ledgers = baseMapper.selectList(wrapper);
        return MapstructUtils.convert(ledgers, FinAccountLedgerVo.class);
    }

    /**
     * 查询账户的流水记录
     *
     * @param accountId 账户ID
     * @return 流水记录列表
     */
    @Override
    public List<FinAccountLedgerVo> queryByAccountId(Long accountId) {
        LambdaQueryWrapper<FinAccountLedger> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinAccountLedger::getAccountId, accountId);
        wrapper.eq(FinAccountLedger::getStatus, "1"); // 有效状态
        wrapper.orderByDesc(FinAccountLedger::getTransactionTime);

        List<FinAccountLedger> ledgers = baseMapper.selectList(wrapper);
        return MapstructUtils.convert(ledgers, FinAccountLedgerVo.class);
    }

    /**
     * 撤销流水记录
     *
     * @param ledgerId 流水ID
     * @param reason   撤销原因
     * @return 是否撤销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelLedger(Long ledgerId, String reason) {
        try {
            if (ledgerId == null) {
                throw new ServiceException("流水ID不能为空");
            }

            // 获取流水记录
            FinAccountLedger ledger = baseMapper.selectById(ledgerId);
            if (ledger == null) {
                throw new ServiceException("流水记录不存在");
            }

            // 校验是否可以撤销
            if (!"CONFIRMED".equals(ledger.getLedgerStatus())) {
                throw new ServiceException("只有已确认的流水记录才能撤销");
            }

            // 撤销账户余额变动
            BigDecimal reverseAmount = "IN".equals(ledger.getDirection()) ?
                ledger.getAmount().negate() : ledger.getAmount();

            // ✅ 修复：finAccountService.updateAccountBalance方法确实存在，恢复调用
            boolean balanceResult = finAccountService.updateAccountBalance(ledger.getAccountId(), reverseAmount);

            // 删除流水记录（逻辑删除）
            boolean ledgerResult = baseMapper.deleteById(ledgerId) > 0;

            if (balanceResult && ledgerResult) {
                log.info("流水记录撤销成功 - 流水ID: {}, 账户ID: {}, 金额: {}, 撤销原因: {}",
                    ledgerId, ledger.getAccountId(), ledger.getAmount(), reason);
                return true;
            } else {
                throw new ServiceException("流水记录撤销失败");
            }
        } catch (Exception e) {
            log.error("流水记录撤销失败 - 流水ID: {}, 错误: {}", ledgerId, e.getMessage(), e);
            throw new ServiceException("流水记录撤销失败：" + e.getMessage());
        }
    }
}
