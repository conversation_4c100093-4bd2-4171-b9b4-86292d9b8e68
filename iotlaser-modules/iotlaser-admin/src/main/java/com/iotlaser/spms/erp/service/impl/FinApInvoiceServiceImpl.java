package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.CompanyVo;
import com.iotlaser.spms.base.service.ICompanyService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.FinApInvoice;
import com.iotlaser.spms.erp.domain.FinApInvoiceItem;
import com.iotlaser.spms.erp.domain.bo.FinApInvoiceBo;
import com.iotlaser.spms.erp.domain.bo.FinApInvoiceItemBo;
import com.iotlaser.spms.erp.domain.bo.PurchaseInboundItemBo;
import com.iotlaser.spms.erp.domain.dto.FinApInvoiceMatchResultDto;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceItemVo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.enums.FinApInvoiceStatus;
import com.iotlaser.spms.erp.enums.FinPayeeType;
import com.iotlaser.spms.erp.enums.PurchaseInboundStatus;
import com.iotlaser.spms.erp.mapper.FinApInvoiceMapper;
import com.iotlaser.spms.erp.service.*;
import com.iotlaser.spms.erp.utils.AmountCalculationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_AP_INVOICE_CODE;

/**
 * 供应商发票Service业务层处理
 * 管理应付发票的完整生命周期，包括发票创建、审核、匹配、核销等核心财务功能
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinApInvoiceServiceImpl implements IFinApInvoiceService {

    private final FinApInvoiceMapper baseMapper;
    private final IFinApInvoiceItemService finApInvoiceItemService;
    private final IPurchaseOrderItemService purchaseOrderItemService;
    private final IPurchaseInboundItemService purchaseInboundItemService;
    private final ICompanyService companyService;
    private final Gen gen;
    private final IThreeWayMatchService threeWayMatchService;
    @Lazy
    @Autowired
    private IPurchaseInboundService purchaseInboundService;
    @Lazy
    @Autowired
    private IFinApPaymentInvoiceLinkService finApPaymentInvoiceLinkService;

    /**
     * 查询供应商发票
     *
     * @param invoiceId 主键
     * @return 供应商发票
     */
    @Override
    public FinApInvoiceVo queryById(Long invoiceId) {
        return baseMapper.selectVoById(invoiceId);
    }

    /**
     * 分页查询供应商发票列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 供应商发票分页列表
     */
    @Override
    public TableDataInfo<FinApInvoiceVo> queryPageList(FinApInvoiceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinApInvoice> lqw = buildQueryWrapper(bo);
        Page<FinApInvoiceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的供应商发票列表
     *
     * @param bo 查询条件
     * @return 供应商发票列表
     */
    @Override
    public List<FinApInvoiceVo> queryList(FinApInvoiceBo bo) {
        LambdaQueryWrapper<FinApInvoice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinApInvoice> buildQueryWrapper(FinApInvoiceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinApInvoice> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinApInvoice::getInvoiceId);
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceCode()), FinApInvoice::getInvoiceCode, bo.getInvoiceCode());
        if (bo.getPayeeType() != null) {
            lqw.eq(FinApInvoice::getPayeeType, bo.getPayeeType());
        }
        lqw.eq(bo.getPayeeId() != null, FinApInvoice::getPayeeId, bo.getPayeeId());
        lqw.like(StringUtils.isNotBlank(bo.getPayeeName()), FinApInvoice::getPayeeName, bo.getPayeeName());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNumber()), FinApInvoice::getInvoiceNumber, bo.getInvoiceNumber());
        lqw.eq(bo.getInvoiceDate() != null, FinApInvoice::getInvoiceDate, bo.getInvoiceDate());
        lqw.eq(bo.getAmountExclusiveTax() != null, FinApInvoice::getAmountExclusiveTax, bo.getAmountExclusiveTax());
        lqw.eq(bo.getTaxAmount() != null, FinApInvoice::getTaxAmount, bo.getTaxAmount());
        lqw.eq(bo.getAmount() != null, FinApInvoice::getAmount, bo.getAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceStatus()), FinApInvoice::getInvoiceStatus, bo.getInvoiceStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinApInvoice::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增供应商发票
     *
     * @param bo 供应商发票
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(FinApInvoiceBo bo) {
        try {
            // 生成发票编码
            if (StringUtils.isEmpty(bo.getInvoiceCode())) {
                bo.setInvoiceCode(gen.code(ERP_AP_INVOICE_CODE));
            }

            // 设置初始状态
            if (bo.getInvoiceStatus() == null) {
                bo.setInvoiceStatus(FinApInvoiceStatus.PENDING.getValue());
            }

            // 填充冗余字段
            fillRedundantFields(bo);

            // 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 计算金额（价税分离）
            calculateAmounts(bo);

            // 转换为实体并校验
            FinApInvoice add = MapstructUtils.convert(bo, FinApInvoice.class);
            validEntityBeforeSave(add);

            // 插入数据库
            boolean flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setInvoiceId(add.getInvoiceId());
                log.info("新增供应商发票成功：{}", add.getInvoiceCode());
            }
            return flag;
        } catch (Exception e) {
            log.error("新增供应商发票失败：{}", e.getMessage(), e);
            throw new ServiceException("新增供应商发票失败：" + e.getMessage());
        }
    }

    /**
     * 修改供应商发票
     *
     * @param bo 供应商发票
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(FinApInvoiceBo bo) {
        try {
            // 填充冗余字段
            fillRedundantFields(bo);

            // 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 计算金额（价税分离）
            calculateAmounts(bo);

            // 转换为实体并校验
            FinApInvoice update = MapstructUtils.convert(bo, FinApInvoice.class);
            validEntityBeforeSave(update);

            // 更新数据库
            boolean result = baseMapper.updateById(update) > 0;
            if (result) {
                log.info("修改供应商发票成功：{}", update.getInvoiceCode());
            }
            return result;
        } catch (Exception e) {
            log.error("修改供应商发票失败：{}", e.getMessage(), e);
            throw new ServiceException("修改供应商发票失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinApInvoice entity) {
        // 校验发票编号唯一性
        if (StringUtils.isNotBlank(entity.getInvoiceCode())) {
            LambdaQueryWrapper<FinApInvoice> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinApInvoice::getInvoiceCode, entity.getInvoiceCode());
            if (entity.getInvoiceId() != null) {
                wrapper.ne(FinApInvoice::getInvoiceId, entity.getInvoiceId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("发票编号已存在：" + entity.getInvoiceCode());
            }
        }

        // 校验状态流转合法性
        if (entity.getInvoiceId() != null) {
            FinApInvoice existing = baseMapper.selectById(entity.getInvoiceId());
            if (existing != null && existing.getInvoiceStatus() != null && entity.getInvoiceStatus() != null) {
                FinApInvoiceStatus fromStatus = FinApInvoiceStatus.valueOf(existing.getInvoiceStatus());
                FinApInvoiceStatus toStatus = FinApInvoiceStatus.valueOf(entity.getInvoiceStatus());
                if (!isValidStatusTransition(fromStatus, toStatus)) {
                    throw new ServiceException("状态流转不合法");
                }
            }
        }

        // 校验金额合理性
        if (entity.getAmount() != null && entity.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("发票总金额必须大于0");
        }

        // 校验不含税金额合理性
        if (entity.getAmountExclusiveTax() != null && entity.getAmountExclusiveTax().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("发票不含税金额必须大于0");
        }

        // 校验税额合理性
        if (entity.getTaxAmount() != null && entity.getTaxAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("发票税额不能为负数");
        }

        // 校验税额计算（保留核心业务逻辑校验）
        if (entity.getAmountExclusiveTax() != null && entity.getTaxAmount() != null && entity.getAmount() != null) {
            BigDecimal calculatedTotal = entity.getAmountExclusiveTax().add(entity.getTaxAmount());
            if (calculatedTotal.compareTo(entity.getAmount()) != 0) {
                throw new ServiceException("金额(不含税) + 税额 ≠ 总金额，请检查计算");
            }
        }

        // 校验供应商状态
        if (entity.getPayeeId() != null) {
            CompanyVo supplier = companyService.queryById(entity.getPayeeId());
            if (supplier == null) {
                throw new ServiceException("供应商不存在");
            }
        }
    }

    /**
     * 校验并批量删除供应商发票信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验供应商发票是否可以删除
            List<FinApInvoice> invoices = baseMapper.selectByIds(ids);
            for (FinApInvoice invoice : invoices) {
                // 检查发票状态，只有草稿状态的发票才能删除
                if (!"DRAFT".equals(invoice.getInvoiceStatus()) && !"PENDING".equals(invoice.getInvoiceStatus())) {
                    throw new ServiceException("供应商发票【" + invoice.getInvoiceCode() + "】状态为【" +
                        invoice.getInvoiceStatus() + "】，不允许删除");
                }

                // 检查是否有关联的付款核销记录
                if (finApPaymentInvoiceLinkService.existsByInvoiceId(invoice.getInvoiceId())) {
                    throw new ServiceException("供应商发票【" + invoice.getInvoiceCode() + "】存在付款核销记录，不允许删除");
                }

                // 检查是否有关联的发票明细
                if (finApInvoiceItemService.existsByInvoiceId(invoice.getInvoiceId())) {
                    // 级联删除发票明细
                    List<Long> itemIds = finApInvoiceItemService.getItemIdsByInvoiceId(invoice.getInvoiceId());
                    if (!itemIds.isEmpty()) {
                        finApInvoiceItemService.deleteWithValidByIds(itemIds, false);
                        log.info("级联删除供应商发票明细，发票：{}，明细数量：{}", invoice.getInvoiceCode(), itemIds.size());
                    }
                }

                log.info("删除供应商发票校验通过：{}", invoice.getInvoiceCode());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除供应商发票成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除供应商发票失败：{}", e.getMessage(), e);
            throw new ServiceException("删除供应商发票失败：" + e.getMessage());
        }
    }

    /**
     * 从采购订单生成应付发票
     *
     * @param purchaseOrderId    采购订单ID
     * @param purchaseOrderCode  采购订单编号
     * @param supplierId         供应商ID
     * @param supplierCode       供应商编码
     * @param supplierName       供应商名称
     * @param amount             发票总金额
     * @param amountExclusiveTax 金额(不含税)
     * @param taxAmount          税额
     * @param invoiceCode        发票编号
     * @return 是否生成成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateFromPurchaseOrder(Long purchaseOrderId, String purchaseOrderCode,
                                             Long supplierId, String supplierCode, String supplierName,
                                             BigDecimal amount, BigDecimal amountExclusiveTax,
                                             BigDecimal taxAmount, String invoiceCode) {
        try {
            FinApInvoice invoice = new FinApInvoice();

            // 发票基本信息
            invoice.setInvoiceCode(invoiceCode);
            invoice.setSummary("采购发票-" + purchaseOrderCode);

            // 供应商信息
            invoice.setPayeeType(FinPayeeType.SUPPLIER);
            invoice.setPayeeId(supplierId);
            invoice.setPayeeName(supplierName);

            // 注意：FinApInvoice主表不直接关联采购订单
            // 应付单应该从入库单生成，而不是直接从采购订单生成
            // TODO: 需要重新设计从入库单生成应付单的流程

            // 金额信息
            invoice.setAmount(amount);
            invoice.setAmountExclusiveTax(amountExclusiveTax);
            invoice.setTaxAmount(taxAmount);

            // 日期信息
            invoice.setInvoiceDate(LocalDate.now());

            // 状态信息
            invoice.setInvoiceStatus(FinApInvoiceStatus.PENDING.getValue());

            boolean result = baseMapper.insert(invoice) > 0;

            if (result) {
                log.info("应付发票生成成功 - 来源: {}, 供应商: {}, 金额: {}",
                    purchaseOrderCode, supplierName, amount);
            }

            return result;
        } catch (Exception e) {
            log.error("应付发票生成失败 - 来源: {}, 错误: {}", purchaseOrderCode, e.getMessage(), e);
            throw new ServiceException("应付发票生成失败：" + e.getMessage());
        }
    }

    /**
     * 审核发票
     *
     * @param invoiceId   发票ID
     * @param auditById   审核人ID
     * @param auditByName 审核人姓名
     * @param auditResult 审核结果 (APPROVED/REJECTED)
     * @param auditRemark 审核备注
     * @return 是否审核成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditInvoice(Long invoiceId, Long auditById, String auditByName,
                                String auditResult, String auditRemark) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            if (!"PENDING".equals(invoice.getInvoiceStatus())) {
                throw new ServiceException("发票状态不允许审核");
            }

            // 更新发票状态
            if ("APPROVED".equals(auditResult)) {
                invoice.setInvoiceStatus("APPROVED");
            } else if ("REJECTED".equals(auditResult)) {
                invoice.setInvoiceStatus("REJECTED");
            } else {
                throw new ServiceException("无效的审核结果");
            }

            invoice.setRemark(auditRemark);

            boolean result = baseMapper.updateById(invoice) > 0;

            if (result) {
                log.info("发票审核完成 - 发票: {}, 结果: {}, 审核人: {}",
                    invoice.getInvoiceCode(), auditResult, auditByName);

                // TODO: [调用三单匹配服务] - 参考文档 docs/design/README_FINANCE.md
                // 发票审核通过后，应自动触发三单匹配流程。
                // if ("APPROVED".equals(auditResult)) {
                //     threeWayMatchService.matchInvoice(invoiceId);
                // }
            }

            return result;
        } catch (Exception e) {
            log.error("发票审核失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票审核失败：" + e.getMessage());
        }
    }

    /**
     * 更新发票状态
     *
     * @param invoiceId 发票ID
     * @param newStatus 新状态
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateInvoiceStatus(Long invoiceId, String newStatus) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            invoice.setInvoiceStatus(newStatus);
            boolean result = baseMapper.updateById(invoice) > 0;

            if (result) {
                log.info("发票状态更新成功 - 发票: {}, 新状态: {}",
                    invoice.getInvoiceCode(), newStatus);
            }

            return result;
        } catch (Exception e) {
            log.error("发票状态更新失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 三单匹配 - 自动匹配
     *
     * @param invoiceId 发票ID
     * @return 匹配结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean autoMatchThreeWay(Long invoiceId) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            if (!"UNMATCHED".equals(invoice.getInvoiceStatus())) {
                throw new ServiceException("发票状态不允许匹配");
            }

            // 查找匹配的采购订单和入库单
            List<FinApInvoiceMatchResultDto> matchResults = findMatchingDocuments(invoice);

            if (matchResults.isEmpty()) {
                log.warn("发票自动匹配失败 - 未找到匹配的采购订单或入库单: {}", invoice.getInvoiceCode());
                return false;
            }

            // 执行匹配逻辑
            boolean matchResult = executeMatching(invoice, matchResults);

            if (matchResult) {
                // 更新发票匹配状态
                String newStatus = calculateMatchStatus(invoice, matchResults);
                updateInvoiceStatus(invoiceId, newStatus);

                log.info("发票自动匹配成功 - 发票: {}, 状态: {}", invoice.getInvoiceCode(), newStatus);
            }

            return matchResult;
        } catch (Exception e) {
            log.error("发票自动匹配失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票自动匹配失败：" + e.getMessage());
        }
    }

    /**
     * 三单匹配 - 手工匹配
     *
     * @param invoiceId       发票ID
     * @param purchaseOrderId 采购订单ID
     * @param inboundId       入库单ID
     * @param matchedAmount   匹配金额
     * @param operatorId      操作人ID
     * @param operatorName    操作人姓名
     * @return 匹配结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean manualMatchThreeWay(Long invoiceId, Long purchaseOrderId, Long inboundId,
                                       BigDecimal matchedAmount, Long operatorId, String operatorName) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            // 校验匹配金额
            if (matchedAmount.compareTo(invoice.getAmount()) > 0) {
                throw new ServiceException("匹配金额不能超过发票总金额");
            }

            // 创建匹配记录
            boolean matchResult = createMatchRecord(invoice, purchaseOrderId, inboundId,
                matchedAmount, operatorId, operatorName);

            if (matchResult) {
                // 更新发票匹配状态
                String newStatus = calculateMatchStatusByAmount(invoice, matchedAmount);
                updateInvoiceStatus(invoiceId, newStatus);

                log.info("发票手工匹配成功 - 发票: {}, 匹配金额: {}, 操作人: {}",
                    invoice.getInvoiceCode(), matchedAmount, operatorName);
            }

            return matchResult;
        } catch (Exception e) {
            log.error("发票手工匹配失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票手工匹配失败：" + e.getMessage());
        }
    }

    /**
     * 查找匹配的采购订单和入库单
     */
    private List<FinApInvoiceMatchResultDto> findMatchingDocuments(FinApInvoice invoice) {
        List<FinApInvoiceMatchResultDto> results = new ArrayList<>();

        // 基于供应商和时间范围查找采购订单
        // 这里需要调用采购订单服务查找匹配的订单
        // 实际实现中需要根据供应商ID、物料、数量等条件进行匹配

        // 基于采购订单查找对应的入库单
        // 这里需要调用入库单服务查找匹配的入库单

        // 计算匹配度并排序
        // 匹配度计算基于：供应商匹配、物料匹配、数量匹配、金额匹配、时间匹配等

        return results;
    }

    /**
     * 执行匹配逻辑
     */
    private boolean executeMatching(FinApInvoice invoice, List<FinApInvoiceMatchResultDto> matchResults) {
        // 执行具体的匹配逻辑
        // 创建匹配记录
        // 更新相关单据状态
        return true;
    }

    /**
     * 计算匹配状态
     */
    private String calculateMatchStatus(FinApInvoice invoice, List<FinApInvoiceMatchResultDto> matchResults) {
        // 根据匹配结果计算发票状态
        BigDecimal totalMatchedAmount = matchResults.stream()
            .map(FinApInvoiceMatchResultDto::getMatchedAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        if (totalMatchedAmount.compareTo(invoice.getAmount()) == 0) {
            return "FULLY_MATCHED";
        } else if (totalMatchedAmount.compareTo(BigDecimal.ZERO) > 0) {
            return "PARTIALLY_MATCHED";
        } else {
            return "UNMATCHED";
        }
    }

    /**
     * 根据金额计算匹配状态
     */
    private String calculateMatchStatusByAmount(FinApInvoice invoice, BigDecimal matchedAmount) {
        // 获取已匹配总金额
        BigDecimal totalMatchedAmount = getInvoiceMatchedAmount(invoice.getInvoiceId()).add(matchedAmount);

        if (totalMatchedAmount.compareTo(invoice.getAmount()) == 0) {
            return "FULLY_MATCHED";
        } else if (totalMatchedAmount.compareTo(BigDecimal.ZERO) > 0) {
            return "PARTIALLY_MATCHED";
        } else {
            return "UNMATCHED";
        }
    }

    /**
     * 创建匹配记录
     */
    private boolean createMatchRecord(FinApInvoice invoice, Long purchaseOrderId, Long inboundId,
                                      BigDecimal matchedAmount, Long operatorId, String operatorName) {
        // 创建三单匹配记录
        // 这里需要创建一个匹配记录表来记录匹配关系
        // 包含：发票ID、采购订单ID、入库单ID、匹配金额、匹配时间、操作人等
        return true;
    }

    /**
     * 获取发票已匹配金额
     */
    private BigDecimal getInvoiceMatchedAmount(Long invoiceId) {
        // 查询发票的已匹配金额
        // 从匹配记录表中汇总计算
        return BigDecimal.ZERO;
    }

    /**
     * 获取待匹配发票列表
     *
     * @param supplierId 供应商ID (可选)
     * @param startDate  开始日期 (可选)
     * @param endDate    结束日期 (可选)
     * @return 待匹配发票列表
     */
    public List<FinApInvoiceVo> getUnmatchedInvoices(Long supplierId, LocalDate startDate, LocalDate endDate) {
        LambdaQueryWrapper<FinApInvoice> wrapper = Wrappers.lambdaQuery();
        wrapper.in(FinApInvoice::getInvoiceStatus, "UNMATCHED", "PARTIALLY_MATCHED");

        if (supplierId != null) {
            wrapper.eq(FinApInvoice::getPayeeId, supplierId);
        }

        if (startDate != null) {
            wrapper.ge(FinApInvoice::getInvoiceDate, startDate);
        }

        if (endDate != null) {
            wrapper.le(FinApInvoice::getInvoiceDate, endDate);
        }

        wrapper.orderByDesc(FinApInvoice::getInvoiceDate);

        List<FinApInvoice> invoices = baseMapper.selectList(wrapper);
        return MapstructUtils.convert(invoices, FinApInvoiceVo.class);
    }

    /**
     * 获取发票匹配详情
     *
     * @param invoiceId 发票ID
     * @return 匹配详情
     */
    public FinApInvoiceVo getInvoiceMatchDetail(Long invoiceId) {
        FinApInvoice invoice = baseMapper.selectById(invoiceId);
        if (invoice == null) {
            throw new ServiceException("发票不存在");
        }

        FinApInvoiceVo invoiceVo = MapstructUtils.convert(invoice, FinApInvoiceVo.class);

        // 获取匹配记录
        // 这里需要查询匹配记录表，获取该发票的所有匹配记录
        // 包括匹配的采购订单、入库单、匹配金额等信息

        return invoiceVo;
    }

    /**
     * 撤销发票匹配
     *
     * @param invoiceId     发票ID
     * @param matchRecordId 匹配记录ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 是否撤销成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelInvoiceMatch(Long invoiceId, Long matchRecordId, Long operatorId, String operatorName) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            // 删除匹配记录
            boolean deleteResult = deleteMatchRecord(matchRecordId, operatorId, operatorName);

            if (deleteResult) {
                // 重新计算发票匹配状态
                String newStatus = recalculateMatchStatus(invoiceId);
                updateInvoiceStatus(invoiceId, newStatus);

                log.info("发票匹配撤销成功 - 发票: {}, 操作人: {}", invoice.getInvoiceCode(), operatorName);
            }

            return deleteResult;
        } catch (Exception e) {
            log.error("发票匹配撤销失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票匹配撤销失败：" + e.getMessage());
        }
    }

    /**
     * 删除匹配记录
     */
    private boolean deleteMatchRecord(Long matchRecordId, Long operatorId, String operatorName) {
        // 删除匹配记录的具体实现
        // 这里需要操作匹配记录表
        return true;
    }

    /**
     * 重新计算匹配状态
     */
    private String recalculateMatchStatus(Long invoiceId) {
        // 重新计算发票的匹配状态
        BigDecimal totalMatchedAmount = getInvoiceMatchedAmount(invoiceId);
        FinApInvoice invoice = baseMapper.selectById(invoiceId);

        if (totalMatchedAmount.compareTo(BigDecimal.ZERO) == 0) {
            return "UNMATCHED";
        } else if (totalMatchedAmount.compareTo(invoice.getAmount()) == 0) {
            return "FULLY_MATCHED";
        } else {
            return "PARTIALLY_MATCHED";
        }
    }

    /**
     * 从采购订单自动生成应付发票
     *
     * @param purchaseOrderId   采购订单ID
     * @param purchaseOrderCode 采购订单编号
     * @param supplierId        供应商ID
     * @param supplierCode      供应商编码
     * @param supplierName      供应商名称
     * @param amount            发票总金额
     * @param dueDate           到期日期
     * @param operatorId        操作人ID
     * @param operatorName      操作人姓名
     * @return 发票ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long generateFromPurchaseOrder(Long purchaseOrderId, String purchaseOrderCode,
                                          Long supplierId, String supplierCode, String supplierName,
                                          BigDecimal amount, LocalDate dueDate,
                                          Long operatorId, String operatorName) {
        try {
            // TODO: FinApInvoice实体中没有sourceId/sourceType字段，需要重新设计重复检查逻辑
            // 暂时跳过重复检查，待重新设计基于入库单的关联查询
            // LambdaQueryWrapper<FinApInvoice> checkWrapper = Wrappers.lambdaQuery();
            // checkWrapper.eq(FinApInvoice::getSourceId, purchaseOrderId);
            // checkWrapper.eq(FinApInvoice::getSourceType, "PURCHASE_ORDER");
            // if (baseMapper.exists(checkWrapper)) {
            //     throw new ServiceException("采购订单已生成发票，不能重复生成");
            // }

            FinApInvoice invoice = new FinApInvoice();

            // 生成发票编号和名称
            invoice.setInvoiceCode(generateInvoiceCode());
            invoice.setSummary("采购发票-" + supplierName + "-" + purchaseOrderCode);

            // 供应商信息
            invoice.setPayeeType(FinPayeeType.SUPPLIER);
            invoice.setPayeeId(supplierId);
            invoice.setPayeeName(supplierName);

            // 注意：FinApInvoice主表不直接关联采购订单
            // 应付单应该从入库单生成，而不是直接从采购订单生成
            // TODO: 需要重新设计从入库单生成应付单的流程

            // 金额信息
            invoice.setAmountExclusiveTax(amount);
            invoice.setTaxAmount(BigDecimal.ZERO);
            invoice.setAmount(amount);

            // 日期信息
            invoice.setInvoiceDate(LocalDate.now());
            // TODO: FinApInvoice实体中没有dueDate字段
            // invoice.setDueDate(dueDate);

            // 状态信息
            invoice.setInvoiceStatus("UNMATCHED");

            // TODO: FinApInvoice实体中没有applicantId/applicantName字段
            // invoice.setApplicantId(operatorId);
            // invoice.setApplicantName(operatorName);

            boolean result = baseMapper.insert(invoice) > 0;

            if (result) {
                log.info("从采购订单自动生成应付发票成功 - 采购订单: {}, 发票: {}, 金额: {}",
                    purchaseOrderCode, invoice.getInvoiceCode(), amount);
                return invoice.getInvoiceId();
            } else {
                throw new ServiceException("应付发票生成失败");
            }

        } catch (Exception e) {
            log.error("从采购订单生成应付发票失败 - 采购订单: {}, 错误: {}", purchaseOrderCode, e.getMessage(), e);
            throw new ServiceException("从采购订单生成应付发票失败：" + e.getMessage());
        }
    }

    /**
     * 生成发票编号
     */
    private String generateInvoiceCode() {
        return "INV" + System.currentTimeMillis();
    }

    /**
     * 审批应付发票
     *
     * @param invoiceId      发票ID
     * @param approvalAction 审批动作 (APPROVE/REJECT)
     * @param approvalRemark 审批备注
     * @param approverId     审批人ID
     * @param approverName   审批人姓名
     * @return 是否审批成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean approveInvoice(Long invoiceId, String approvalAction, String approvalRemark,
                                  Long approverId, String approverName) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            if (!"PENDING".equals(invoice.getInvoiceStatus()) && !"UNMATCHED".equals(invoice.getInvoiceStatus())) {
                throw new ServiceException("发票状态不允许审批");
            }

            if ("APPROVE".equals(approvalAction)) {
                invoice.setInvoiceStatus("APPROVED");
            } else if ("REJECT".equals(approvalAction)) {
                invoice.setInvoiceStatus("REJECTED");
            } else {
                throw new ServiceException("无效的审批动作");
            }

            invoice.setRemark(approvalRemark);
            boolean result = baseMapper.updateById(invoice) > 0;

            if (result) {
                log.info("发票审批成功 - 发票: {}, 动作: {}, 审批人: {}",
                    invoice.getInvoiceCode(), approvalAction, approverName);
            }

            return result;
        } catch (Exception e) {
            log.error("发票审批失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票审批失败：" + e.getMessage());
        }
    }

    /**
     * 取消应付发票
     *
     * @param invoiceId    发票ID
     * @param cancelById   取消人ID
     * @param cancelByName 取消人姓名
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelInvoice(Long invoiceId, Long cancelById, String cancelByName, String cancelReason) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            if ("PAID".equals(invoice.getInvoiceStatus()) || "CANCELLED".equals(invoice.getInvoiceStatus())) {
                throw new ServiceException("发票状态不允许取消");
            }

            invoice.setInvoiceStatus("CANCELLED");
            invoice.setRemark(cancelReason);
            boolean result = baseMapper.updateById(invoice) > 0;

            if (result) {
                log.info("发票取消成功 - 发票: {}, 取消人: {}, 原因: {}",
                    invoice.getInvoiceCode(), cancelByName, cancelReason);
            }

            return result;
        } catch (Exception e) {
            log.error("发票取消失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票取消失败：" + e.getMessage());
        }
    }

    /**
     * 设置发票为逾期状态
     *
     * @param invoiceId 发票ID
     * @return 是否设置成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean setOverdueStatus(Long invoiceId) {
        try {
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在");
            }

            if (!"APPROVED".equals(invoice.getInvoiceStatus()) && !"PARTIALLY_PAID".equals(invoice.getInvoiceStatus())) {
                return false; // 只有已审批和部分付款状态才能设置为逾期
            }

            // TODO: FinApInvoice实体中没有dueDate字段，需要重新设计逾期判断逻辑
            // 暂时基于发票日期判断（假设30天为付款期限）
            LocalDate dueDate = invoice.getInvoiceDate().plusDays(30);
            if (dueDate.isBefore(LocalDate.now())) {
                invoice.setInvoiceStatus("OVERDUE");
                boolean result = baseMapper.updateById(invoice) > 0;

                if (result) {
                    log.info("发票设置逾期状态成功 - 发票: {}", invoice.getInvoiceCode());
                }

                return result;
            }

            return false;
        } catch (Exception e) {
            log.error("设置发票逾期状态失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("设置发票逾期状态失败：" + e.getMessage());
        }
    }

    /**
     * 批量设置逾期状态
     *
     * @return 设置逾期的发票数量
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchSetOverdueStatus() {
        try {
            // 查询已到期但未设置逾期状态的发票
            LambdaQueryWrapper<FinApInvoice> wrapper = Wrappers.lambdaQuery();
            wrapper.in(FinApInvoice::getInvoiceStatus, "APPROVED", "PARTIALLY_PAID");
            // TODO: FinApInvoice实体中没有dueDate字段，需要重新设计逾期查询逻辑
            // 暂时基于发票日期判断（假设30天为付款期限）
            wrapper.lt(FinApInvoice::getInvoiceDate, LocalDate.now().minusDays(30));

            List<FinApInvoice> overdueInvoices = baseMapper.selectList(wrapper);

            int count = 0;
            for (FinApInvoice invoice : overdueInvoices) {
                invoice.setInvoiceStatus("OVERDUE");
                if (baseMapper.updateById(invoice) > 0) {
                    count++;
                }
            }

            log.info("批量设置发票逾期状态完成 - 处理数量: {}", count);

            return count;
        } catch (Exception e) {
            log.error("批量设置发票逾期状态失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量设置发票逾期状态失败：" + e.getMessage());
        }
    }


    /**
     * 从采购入库单生成应付发票
     *
     * @param inboundId     入库单ID
     * @param invoiceType   发票类型
     * @param invoiceDate   发票日期
     * @param invoiceNumber 发票号码
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 发票ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long generateFromPurchaseInbound(Long inboundId, String invoiceType,
                                            LocalDate invoiceDate, String invoiceNumber,
                                            Long operatorId, String operatorName) {
        try {
            // TODO: 获取入库单信息
            // PurchaseInbound inbound = purchaseInboundService.queryById(inboundId);

            // 创建应付发票
            FinApInvoice invoice = new FinApInvoice();

            // 生成发票编号
            invoice.setInvoiceCode(generateInvoiceCode());
            invoice.setSummary("采购发票-" + invoiceNumber);
            // TODO: FinApInvoice实体中没有invoiceType字段
            // invoice.setInvoiceType(invoiceType);
            invoice.setInvoiceDate(invoiceDate);
            invoice.setInvoiceNumber(invoiceNumber);

            // TODO: 从入库单复制供应商信息
            // invoice.setSupplierId(inbound.getSupplierId());
            // invoice.setSupplierCode(inbound.getSupplierCode());
            // invoice.setSupplierName(inbound.getSupplierName());

            // 设置发票状态
            invoice.setInvoiceStatus("UNMATCHED");

            // TODO: FinApInvoice实体中没有handlerId/handlerName字段
            // invoice.setHandlerId(operatorId);
            // invoice.setHandlerName(operatorName);

            // 插入发票主记录
            baseMapper.insert(invoice);
            Long invoiceId = invoice.getInvoiceId();

            // 生成发票明细
            generateInvoiceItemsFromInbound(invoiceId, inboundId);

            // 汇总发票金额
            summarizeFromItems(invoiceId);

            log.info("从采购入库单生成应付发票成功 - 入库单ID: {}, 发票: {}, 操作人: {}",
                inboundId, invoice.getInvoiceCode(), operatorName);

            return invoiceId;
        } catch (Exception e) {
            log.error("从采购入库单生成应付发票失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("从采购入库单生成应付发票失败：" + e.getMessage());
        }
    }

    /**
     * 批量从入库单生成应付发票
     *
     * @param inboundIds   入库单ID列表
     * @param invoiceType  发票类型
     * @param invoiceDate  发票日期
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 批量生成结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchGenerateFromPurchaseInbounds(List<Long> inboundIds, String invoiceType,
                                                                 LocalDate invoiceDate, Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (Long inboundId : inboundIds) {
                try {
                    // 为每个入库单生成发票号码
                    String invoiceNumber = generateInvoiceNumber();

                    Long invoiceId = generateFromPurchaseInbound(inboundId, invoiceType,
                        invoiceDate, invoiceNumber, operatorId, operatorName);

                    successList.add(Map.of(
                        "inboundId", inboundId,
                        "invoiceId", invoiceId,
                        "invoiceNumber", invoiceNumber,
                        "status", "SUCCESS"
                    ));
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "inboundId", inboundId,
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", inboundIds.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", operatorId);
            result.put("operatorName", operatorName);
            result.put("operationTime", LocalDateTime.now());

            log.info("批量从入库单生成应付发票完成 - 总数: {}, 成功: {}, 失败: {}, 操作人: {}",
                inboundIds.size(), successList.size(), failureList.size(), operatorName);

            return result;
        } catch (Exception e) {
            log.error("批量从入库单生成应付发票失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量从入库单生成应付发票失败：" + e.getMessage());
        }
    }

    /**
     * 生成发票号码
     */
    private String generateInvoiceNumber() {
        return "INV" + System.currentTimeMillis();
    }


    /**
     * 检查入库单是否已生成发票
     *
     * @param inboundId 入库单ID
     * @return 是否已生成发票
     */
    @Override
    public Boolean existsByInboundId(Long inboundId) {
        try {
            if (inboundId == null) {
                return false;
            }

            // 基于现有字段的替代查询逻辑：通过发票明细表查询
            // 查询是否存在直接来源为该入库单的发票明细
            // TODO: 添加existsByDirectSourceId方法
            // if (finApInvoiceItemService.existsByDirectSourceId(inboundId, "PURCHASE_INBOUND")) {
            //     return true;
            // }

            // 备用查询：通过发票名称模糊匹配（包含入库单ID的发票）
            LambdaQueryWrapper<FinApInvoice> wrapper = Wrappers.lambdaQuery();
            wrapper.or().like(FinApInvoice::getRemark, "入库单ID:" + inboundId);
            return baseMapper.selectCount(wrapper) > 0;

        } catch (Exception e) {
            log.error("检查入库单关联发票失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据入库单ID查询发票
     *
     * @param inboundId 入库单ID
     * @return 发票信息
     */
    @Override
    public FinApInvoiceVo queryByInboundId(Long inboundId) {
        try {
            if (inboundId == null) {
                return null;
            }

            // 基于现有字段的替代查询逻辑：通过发票明细表反查发票主表
            // 先查询是否存在直接来源为该入库单的发票明细
            // TODO: 添加getInvoiceIdsByDirectSourceId方法
            // List<Long> invoiceIds = finApInvoiceItemService.getInvoiceIdsByDirectSourceId(inboundId, "PURCHASE_INBOUND");
            List<Long> invoiceIds = new ArrayList<>(); // 临时空列表
            if (!invoiceIds.isEmpty()) {
                // 取第一个发票ID查询发票信息
                FinApInvoice invoice = baseMapper.selectById(invoiceIds.get(0));
                return invoice != null ? MapstructUtils.convert(invoice, FinApInvoiceVo.class) : null;
            }

            // 备用查询：通过发票名称模糊匹配
            LambdaQueryWrapper<FinApInvoice> wrapper = Wrappers.lambdaQuery();
            wrapper.or().like(FinApInvoice::getRemark, "入库单ID:" + inboundId);
            wrapper.last("LIMIT 1");
            FinApInvoice invoice = baseMapper.selectOne(wrapper);
            return invoice != null ? MapstructUtils.convert(invoice, FinApInvoiceVo.class) : null;

        } catch (Exception e) {
            log.error("根据入库单ID查询发票失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从采购订单明细生成应付单明细
     *
     * @param invoiceId            应付单ID
     * @param purchaseOrderItemIds 采购订单明细ID列表
     * @param operatorId           操作人ID（暂不使用）
     * @param operatorName         操作人姓名（暂不使用）
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateInvoiceItemsFromPurchaseOrderItems(Long invoiceId, List<Long> purchaseOrderItemIds,
                                                              Long operatorId, String operatorName) {
        try {
            if (purchaseOrderItemIds == null || purchaseOrderItemIds.isEmpty()) {
                throw new ServiceException("采购订单明细ID列表不能为空");
            }

            // 校验应付单是否存在
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("应付单不存在");
            }

            log.info("开始从采购订单明细生成应付单明细 - 应付单ID: {}, 明细数量: {}",
                invoiceId, purchaseOrderItemIds.size());

            // 获取采购订单明细信息并创建应付单明细
            for (Long purchaseOrderItemId : purchaseOrderItemIds) {
                // 获取采购订单明细信息
                var purchaseOrderItem = purchaseOrderItemService.queryById(purchaseOrderItemId);
                if (purchaseOrderItem == null) {
                    log.warn("采购订单明细不存在，跳过 - 明细ID: {}", purchaseOrderItemId);
                    continue;
                }

                // 创建应付单明细
                FinApInvoiceItem invoiceItem = new FinApInvoiceItem();
                invoiceItem.setInvoiceId(invoiceId);

                // 设置源订单信息（明细表负责维护完整的源订单关联）
                // TODO: 需要从采购订单明细获取正确的订单信息
                // invoiceItem.setSourceId(purchaseOrderItem.getOrderId());        // 采购订单ID
                // invoiceItem.setSourceCode(purchaseOrderItem.getOrderCode());    // 采购订单编号
                // invoiceItem.setSourceType(SourceType.PURCHASE_ORDER);                    // 源类型
                // invoiceItem.setDirectSourceItemId(purchaseOrderItemId);               // 采购订单明细ID

                // TODO: 应付单主表没有directSource字段，需要重新设计关联逻辑
                // invoiceItem.setDirectSourceId(invoice.getDirectSourceId());     // 直接来源ID（如入库单ID）
                // invoiceItem.setDirectSourceCode(invoice.getDirectSourceCode()); // 直接来源编号
                // invoiceItem.setDirectSourceType(invoice.getDirectSourceType()); // 直接来源类型

                // 从采购订单明细复制产品信息和金额信息
                invoiceItem.setProductId(purchaseOrderItem.getProductId());
                invoiceItem.setProductCode(purchaseOrderItem.getProductCode());
                invoiceItem.setProductName(purchaseOrderItem.getProductName());
                invoiceItem.setUnitId(purchaseOrderItem.getUnitId());
                invoiceItem.setUnitCode(purchaseOrderItem.getUnitCode());
                invoiceItem.setUnitName(purchaseOrderItem.getUnitName());
                invoiceItem.setQuantity(purchaseOrderItem.getQuantity());
                invoiceItem.setPrice(purchaseOrderItem.getPrice());
                invoiceItem.setPriceExclusiveTax(purchaseOrderItem.getPriceExclusiveTax());
                invoiceItem.setAmount(purchaseOrderItem.getAmount());
                invoiceItem.setAmountExclusiveTax(purchaseOrderItem.getAmountExclusiveTax());
                invoiceItem.setTaxRate(purchaseOrderItem.getTaxRate());
                invoiceItem.setTaxAmount(purchaseOrderItem.getTaxAmount());

                invoiceItem.setRemark("从采购订单明细生成 - 明细ID: " + purchaseOrderItemId);

                // 插入应付单明细
                var invoiceItemBo = MapstructUtils.convert(invoiceItem, com.iotlaser.spms.erp.domain.bo.FinApInvoiceItemBo.class);
                finApInvoiceItemService.insertByBo(invoiceItemBo);

                log.info("创建应付单明细成功 - 应付单ID: {}, 采购订单明细ID: {}, 产品: {}, 金额: {}",
                    invoiceId, purchaseOrderItemId, purchaseOrderItem.getProductName(), purchaseOrderItem.getAmount());
            }

            // 更新应付单主表金额汇总
            summarizeFromItems(invoiceId);

            log.info("从采购订单明细生成应付单明细完成 - 应付单ID: {}, 生成明细数量: {}",
                invoiceId, purchaseOrderItemIds.size());

            return true;
        } catch (Exception e) {
            log.error("从采购订单明细生成应付单明细失败 - 应付单ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("生成应付单明细失败：" + e.getMessage());
        }
    }

    /**
     * 从采购入库单生成应付单
     *
     * @param inboundId    入库单ID
     * @param orderId      采购订单ID
     * @param supplierId   供应商ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 应付单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long generateFromPurchaseInbound(Long inboundId, Long orderId, Long supplierId,
                                            Long operatorId, String operatorName) {
        try {
            // 获取入库单信息
            PurchaseInboundVo inbound = purchaseInboundService.queryById(inboundId);
            if (inbound == null) {
                throw new ServiceException("采购入库单不存在");
            }

            // 校验入库单状态
            if (PurchaseInboundStatus.COMPLETED == inbound.getInboundStatus()) {
                throw new ServiceException("只有已完成的入库单才能生成应付单");
            }

            // 检查是否已经生成过应付单
            if (existsByInboundId(inboundId)) {
                throw new ServiceException("该入库单已生成应付单，不能重复生成");
            }

            // 创建应付单主记录
            FinApInvoice invoice = new FinApInvoice();

            // 生成应付单编号和名称
            invoice.setInvoiceCode(generateInvoiceCode());
            invoice.setSummary("采购应付-" + inbound.getSupplierName() + "-" + inbound.getInboundCode());

            // 供应商信息
            invoice.setPayeeId(supplierId);
            invoice.setPayeeName(inbound.getSupplierName());

            // 注意：FinApInvoice主表不直接关联采购订单
            // 应付单关联入库单，通过明细表追溯到采购订单

            // 设置应付状态
            invoice.setInvoiceStatus("UNMATCHED");
            invoice.setInvoiceDate(LocalDate.now());

            // 插入应付单主记录
            int result = baseMapper.insert(invoice);
            if (result <= 0) {
                throw new ServiceException("应付单生成失败");
            }

            Long invoiceId = invoice.getInvoiceId();

            log.info("从采购入库单生成应付单成功 - 入库单: {}, 应付单: {}, 操作人: {}",
                inbound.getInboundCode(), invoice.getInvoiceCode(), operatorName);

            return invoiceId;
        } catch (Exception e) {
            log.error("从采购入库单生成应付单失败 - 入库单ID: {}, 错误: {}", inboundId, e.getMessage(), e);
            throw new ServiceException("从采购入库单生成应付单失败：" + e.getMessage());
        }
    }

    /**
     * 从入库单明细生成应付单明细
     *
     * @param invoiceId      应付单ID
     * @param inboundItemIds 入库单明细ID列表
     * @param operatorId     操作人ID（暂不使用）
     * @param operatorName   操作人姓名（暂不使用）
     * @return 是否生成成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean generateInvoiceItemsFromInboundItems(Long invoiceId, List<Long> inboundItemIds,
                                                        Long operatorId, String operatorName) {
        try {
            if (inboundItemIds == null || inboundItemIds.isEmpty()) {
                throw new ServiceException("入库单明细ID列表不能为空");
            }

            // 校验应付单是否存在
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("应付单不存在");
            }

            log.info("开始从入库单明细生成应付单明细 - 应付单ID: {}, 明细数量: {}",
                invoiceId, inboundItemIds.size());

            // 获取入库单明细信息并创建应付单明细
            for (Long inboundItemId : inboundItemIds) {
                // 获取入库单明细信息
                var inboundItem = purchaseInboundItemService.queryById(inboundItemId);
                if (inboundItem == null) {
                    log.warn("入库单明细不存在，跳过 - 明细ID: {}", inboundItemId);
                    continue;
                }


                // 创建应付单明细
                FinApInvoiceItem invoiceItem = new FinApInvoiceItem();
                invoiceItem.setInvoiceId(invoiceId);

                // 设置源订单信息（明细表负责维护完整的源订单关联）
                // TODO: 需要通过入库单明细获取关联的采购订单信息
                // invoiceItem.setSourceId(inboundItem.getOrderId());        // 采购订单ID
                // invoiceItem.setSourceCode(inboundItem.getOrderCode());    // 采购订单编号
                // invoiceItem.setSourceName(inboundItem.getOrderName());    // 采购订单名称
                //invoiceItem.setSourceType("PURCHASE_ORDER");                // 源类型
                // TODO: 需要通过入库单明细获取关联的采购订单明细ID
                // invoiceItem.setSourceItemId(inboundItem.getOrderItemId());

                // 设置直接来源信息（入库单）
                invoiceItem.setDirectSourceId(inboundItem.getInboundId());     // 入库单ID
                // TODO: 需要获取入库单信息
                // invoiceItem.setDirectSourceCode(inbound.getInboundCode()); // 入库单编号
                // invoiceItem.setDirectSourceName(inbound.getInboundName()); // 入库单名称
                //invoiceItem.setDirectSourceType("PURCHASE_INBOUND");          // 直接来源类型
                // TODO: FinApInvoiceItem实体中没有directSourceItemId字段
                // invoiceItem.setDirectSourceItemId(inboundItemId);             // 入库单明细ID

                // 从入库单明细复制产品信息和金额信息
                invoiceItem.setProductId(inboundItem.getProductId());
                invoiceItem.setProductCode(inboundItem.getProductCode());
                invoiceItem.setProductName(inboundItem.getProductName());
                invoiceItem.setUnitId(inboundItem.getUnitId());
                invoiceItem.setUnitCode(inboundItem.getUnitCode());
                invoiceItem.setUnitName(inboundItem.getUnitName());
                invoiceItem.setQuantity(inboundItem.getQuantity());
                invoiceItem.setPrice(inboundItem.getPrice());
                invoiceItem.setPriceExclusiveTax(inboundItem.getPriceExclusiveTax());
                invoiceItem.setAmount(inboundItem.getAmount());
                invoiceItem.setAmountExclusiveTax(inboundItem.getAmountExclusiveTax());
                invoiceItem.setTaxRate(inboundItem.getTaxRate());
                invoiceItem.setTaxAmount(inboundItem.getTaxAmount());

                invoiceItem.setRemark("从入库单明细生成 - 明细ID: " + inboundItemId);

                // 插入应付单明细
                var invoiceItemBo = MapstructUtils.convert(invoiceItem, com.iotlaser.spms.erp.domain.bo.FinApInvoiceItemBo.class);
                finApInvoiceItemService.insertByBo(invoiceItemBo);

                log.info("创建应付单明细成功 - 应付单ID: {}, 入库单明细ID: {}, 产品: {}, 金额: {}",
                    invoiceId, inboundItemId, inboundItem.getProductName(), inboundItem.getAmount());
            }

            // 更新应付单主表金额汇总
            summarizeFromItems(invoiceId);

            log.info("从入库单明细生成应付单明细完成 - 应付单ID: {}, 生成明细数量: {}",
                invoiceId, inboundItemIds.size());

            return true;
        } catch (Exception e) {
            log.error("从入库单明细生成应付单明细失败 - 应付单ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("生成应付单明细失败：" + e.getMessage());
        }
    }

    /**
     * 从应付单生成付款单
     *
     * @param invoiceId     应付单ID
     * @param paymentAmount 付款金额
     * @param accountId     账户ID
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 付款单ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long generatePaymentOrderFromInvoice(Long invoiceId, BigDecimal paymentAmount,
                                                Long accountId, Long operatorId, String operatorName) {
        try {
            // 校验应付单状态
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("应付单不存在");
            }

            if (!"UNMATCHED".equals(invoice.getInvoiceStatus()) && !"APPROVED".equals(invoice.getInvoiceStatus())) {
                throw new ServiceException("只有未匹配或已审核状态的应付单才能生成付款单");
            }

            // 校验付款金额
            if (paymentAmount == null || paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("付款金额必须大于0");
            }

            if (paymentAmount.compareTo(invoice.getAmount()) > 0) {
                throw new ServiceException("付款金额不能超过应付单金额");
            }

            log.info("开始从应付单生成付款单 - 应付单: {}, 付款金额: {}, 操作人: {}",
                invoice.getInvoiceCode(), paymentAmount, operatorName);

            // 创建付款单
            // TODO: 需要注入FinApPaymentOrderService
            // Long paymentId = finApPaymentOrderService.generateFromInvoice(
            //     invoiceId,
            //     invoice.getSupplierId(),
            //     paymentAmount,
            //     accountId,
            //     operatorId,
            //     operatorName
            // );

            // 暂时返回模拟的付款单ID
            Long paymentId = System.currentTimeMillis();

            // 建立付款单与应付单的关联
            // TODO: 需要在FinApPaymentInvoiceLinkService中创建关联记录
            // finApPaymentInvoiceLinkService.applyPaymentToInvoice(
            //     paymentId, invoiceId, paymentAmount, "从应付单生成付款单"
            // );

            log.info("从应付单生成付款单成功 - 应付单: {}, 付款单ID: {}, 付款金额: {}",
                invoice.getInvoiceCode(), paymentId, paymentAmount);

            return paymentId;
        } catch (Exception e) {
            log.error("从应付单生成付款单失败 - 应付单ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("生成付款单失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(FinApInvoiceBo bo) {
        // 填充供应商信息
        if (bo.getPayeeId() != null) {
            CompanyVo supplier = companyService.queryById(bo.getPayeeId());
            if (supplier != null) {
                bo.setPayeeName(supplier.getCompanyName());
            }
        }
    }

    /**
     * 填充责任人信息
     */
    private void fillResponsiblePersonInfo(FinApInvoiceBo bo) {
        Long currentUserId = LoginHelper.getUserId();
        String currentUserName = LoginHelper.getUsername();

        // 如果是新增，设置申请人
        //if (bo.getInvoiceId() == null) {
        //    bo.setApplicantId(currentUserId);
        //    bo.setApplicantName(currentUserName);
        //}

        // 设置经办人（每次更新都更新）
        //bo.setHandlerId(currentUserId);
        //bo.setHandlerName(currentUserName);
    }

    /**
     * 计算金额（价税分离）
     */
    private void calculateAmounts(FinApInvoiceBo bo) {
        // TODO: FinApInvoiceBo中没有taxRate字段，需要从明细中计算或添加字段
        // 如果有含税金额和税率，计算不含税金额和税额
        // if (bo.getAmount() != null && bo.getTaxRate() != null && bo.getTaxRate().compareTo(BigDecimal.ZERO) > 0) {
        //     BigDecimal amount = bo.getAmount(); // 含税金额
        //     BigDecimal taxRate = bo.getTaxRate(); // 税率
        //
        //     // 计算不含税金额：不含税金额 = 含税金额 / (1 + 税率/100)
        //     BigDecimal divisor = BigDecimal.ONE.add(taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
        //     BigDecimal amountExclusiveTax = amount.divide(divisor, 2, RoundingMode.HALF_UP);
        //     BigDecimal taxAmount = amount.subtract(amountExclusiveTax);
        //
        //     bo.setAmountExclusiveTax(amountExclusiveTax);
        //     bo.setTaxAmount(taxAmount);
        // } else
        if (bo.getAmountExclusiveTax() != null) {
            // 如果有不含税金额，确保含税金额和税额的一致性
            BigDecimal amountExclusiveTax = bo.getAmountExclusiveTax();
            BigDecimal taxAmount = bo.getTaxAmount() != null ? bo.getTaxAmount() : BigDecimal.ZERO;
            bo.setAmount(amountExclusiveTax.add(taxAmount));
        }
    }

    /**
     * 从入库单生成发票明细
     *
     * @param invoiceId 发票ID
     * @param inboundId 入库单ID
     */
    private void generateInvoiceItemsFromInbound(Long invoiceId, Long inboundId) {
        try {
            // 获取入库单明细
            PurchaseInboundItemBo queryBo = new PurchaseInboundItemBo();
            queryBo.setInboundId(inboundId);
            List<PurchaseInboundItemVo> inboundItems = purchaseInboundItemService.queryList(queryBo);

            if (inboundItems.isEmpty()) {
                log.warn("入库单没有明细数据 - 入库单ID: {}", inboundId);
                return;
            }

            log.info("开始从入库单生成发票明细 - 发票ID: {}, 入库单ID: {}, 明细数量: {}",
                invoiceId, inboundId, inboundItems.size());

            // 为每个入库明细创建对应的发票明细
            int successCount = 0;
            for (PurchaseInboundItemVo inboundItem : inboundItems) {
                try {
                    FinApInvoiceItem invoiceItem = new FinApInvoiceItem();
                    invoiceItem.setInvoiceId(invoiceId);

                    // 复制产品信息
                    invoiceItem.setProductId(inboundItem.getProductId());
                    invoiceItem.setProductCode(inboundItem.getProductCode());
                    invoiceItem.setProductName(inboundItem.getProductName());
                    invoiceItem.setUnitId(inboundItem.getUnitId());
                    invoiceItem.setUnitCode(inboundItem.getUnitCode());
                    invoiceItem.setUnitName(inboundItem.getUnitName());

                    // 复制数量和金额信息
                    invoiceItem.setQuantity(inboundItem.getQuantity());
                    invoiceItem.setPrice(inboundItem.getPrice());
                    invoiceItem.setPriceExclusiveTax(inboundItem.getPriceExclusiveTax());
                    invoiceItem.setTaxRate(inboundItem.getTaxRate());

                    // 使用统一工具类计算金额
                    if (invoiceItem.getQuantity() != null && invoiceItem.getPrice() != null) {
                        invoiceItem.setAmount(AmountCalculationUtils.calculateLineAmount(
                            invoiceItem.getQuantity(), invoiceItem.getPrice()));
                    }
                    if (invoiceItem.getQuantity() != null && invoiceItem.getPriceExclusiveTax() != null) {
                        invoiceItem.setAmountExclusiveTax(AmountCalculationUtils.calculateLineAmountExcludingTax(
                            invoiceItem.getQuantity(), invoiceItem.getPriceExclusiveTax()));
                    }
                    if (invoiceItem.getAmount() != null && invoiceItem.getAmountExclusiveTax() != null) {
                        invoiceItem.setTaxAmount(AmountCalculationUtils.calculateTaxAmount(
                            invoiceItem.getAmount(), invoiceItem.getAmountExclusiveTax()));
                    }

                    // 验证金额一致性
                    if (!AmountCalculationUtils.validateAmountConsistency(
                        invoiceItem.getAmount(), invoiceItem.getAmountExclusiveTax(), invoiceItem.getTaxAmount())) {
                        log.warn("发票明细金额计算不一致 - 入库明细ID: {}, 含税金额: {}, 不含税金额: {}, 税额: {}",
                            inboundItem.getItemId(), invoiceItem.getAmount(),
                            invoiceItem.getAmountExclusiveTax(), invoiceItem.getTaxAmount());
                    }

                    // 设置来源信息
                    invoiceItem.setDirectSourceId(inboundId);
                    //invoiceItem.setDirectSourceType("PURCHASE_INBOUND");
                    //invoiceItem.setSourceType("PURCHASE_ORDER");

                    // 设置源单据明细ID
                    // TODO: 需要在FinApInvoiceItem中添加directSourceItemId字段
                    // invoiceItem.setDirectSourceItemId(inboundItem.getItemId());

                    // 获取采购订单信息（如果入库明细中有）
                    // TODO: PurchaseInboundItemVo中没有orderId字段，需要通过其他方式获取
                    // if (inboundItem.getOrderId() != null) {
                    //     invoiceItem.setSourceId(inboundItem.getOrderId());

                    // 尝试获取采购订单详细信息
                    try {
                        // TODO: 需要实现根据订单ID查询订单信息的方法
                        // PurchaseOrderVo order = purchaseOrderService.queryById(inboundItem.getOrderId());
                        // if (order != null) {
                        //     invoiceItem.setSourceCode(order.getOrderCode());
                        //     invoiceItem.setSourceName(order.getOrderName());
                        // }

                        // 设置采购订单明细ID（如果入库明细中有）
                        // if (inboundItem.getOrderItemId() != null) {
                        //     invoiceItem.setSourceItemId(inboundItem.getOrderItemId());
                        // }

                        // log.debug("设置采购订单关联信息 - 发票明细: {}, 订单ID: {}",
                        //     invoiceItem.getProductName(), inboundItem.getOrderId());
                    } catch (Exception e) {
                        // log.warn("获取采购订单信息失败 - 订单ID: {}, 错误: {}", inboundItem.getOrderId(), e.getMessage());
                    }
                    // }

                    // 设置批次信息
                    // TODO: PurchaseInboundItemVo中没有batchId和internalBatchNumber字段，需要通过其他方式获取
                    // if (inboundItem.getBatchId() != null) {
                    //     invoiceItem.setSourceBatchId(inboundItem.getBatchId());
                    // }
                    // if (inboundItem.getInternalBatchNumber() != null) {
                    //     invoiceItem.setInternalBatchNumber(inboundItem.getInternalBatchNumber());
                    // }

                    invoiceItem.setRemark("从入库单明细生成 - 入库明细ID: " + inboundItem.getItemId());
                    invoiceItem.setStatus("1"); // 有效状态

                    // 插入明细记录
                    var invoiceItemBo = MapstructUtils.convert(invoiceItem, FinApInvoiceItemBo.class);
                    boolean insertResult = finApInvoiceItemService.insertByBo(invoiceItemBo);

                    if (insertResult) {
                        successCount++;
                        log.debug("发票明细创建成功 - 发票ID: {}, 入库明细ID: {}, 产品: {}, 数量: {}, 金额: {}",
                            invoiceId, inboundItem.getItemId(), inboundItem.getProductName(),
                            inboundItem.getQuantity(), invoiceItem.getAmount());
                    } else {
                        log.error("发票明细创建失败 - 发票ID: {}, 入库明细ID: {}", invoiceId, inboundItem.getItemId());
                    }
                } catch (Exception e) {
                    log.error("处理入库明细失败 - 入库明细ID: {}, 错误: {}", inboundItem.getItemId(), e.getMessage(), e);
                }
            }

            log.info("从入库单生成发票明细完成 - 发票ID: {}, 入库单ID: {}, 总明细: {}, 成功: {}",
                invoiceId, inboundId, inboundItems.size(), successCount);

            if (successCount == 0) {
                throw new ServiceException("所有发票明细创建失败");
            }
        } catch (Exception e) {
            log.error("从入库单生成发票明细失败 - 发票ID: {}, 入库单ID: {}, 错误: {}", invoiceId, inboundId, e.getMessage(), e);
            throw new ServiceException("生成发票明细失败：" + e.getMessage());
        }
    }

    /**
     * 从明细汇总发票金额
     *
     * @param invoiceId 发票ID
     */
    private void summarizeFromItems(Long invoiceId) {
        try {
            // 查询发票明细
            FinApInvoiceItemBo queryBo = new FinApInvoiceItemBo();
            queryBo.setInvoiceId(invoiceId);
            List<FinApInvoiceItemVo> items = finApInvoiceItemService.queryList(queryBo);

            if (items.isEmpty()) {
                log.warn("发票没有明细数据，无法汇总金额 - 发票ID: {}", invoiceId);
                return;
            }

            log.info("开始汇总发票金额 - 发票ID: {}, 明细数量: {}", invoiceId, items.size());

            // 汇总金额
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalAmountExclusiveTax = BigDecimal.ZERO;
            BigDecimal totalTaxAmount = BigDecimal.ZERO;

            for (FinApInvoiceItemVo item : items) {
                // 使用安全的金额加法
                totalAmount = AmountCalculationUtils.safeAdd(totalAmount, item.getAmount());
                totalAmountExclusiveTax = AmountCalculationUtils.safeAdd(totalAmountExclusiveTax, item.getAmountExclusiveTax());
                totalTaxAmount = AmountCalculationUtils.safeAdd(totalTaxAmount, item.getTaxAmount());

                log.debug("累加明细金额 - 明细ID: {}, 产品: {}, 含税金额: {}, 不含税金额: {}, 税额: {}",
                    item.getItemId(), item.getProductName(), item.getAmount(),
                    item.getAmountExclusiveTax(), item.getTaxAmount());
            }

            // 验证金额一致性
            if (!AmountCalculationUtils.validateAmountConsistency(totalAmount, totalAmountExclusiveTax, totalTaxAmount)) {
                log.warn("发票汇总金额不一致 - 发票ID: {}, 含税总额: {}, 不含税总额: {}, 税额总计: {}",
                    invoiceId, totalAmount, totalAmountExclusiveTax, totalTaxAmount);

                // 重新计算税额以确保一致性
                totalTaxAmount = AmountCalculationUtils.calculateTaxAmount(totalAmount, totalAmountExclusiveTax);
                log.info("重新计算税额 - 发票ID: {}, 修正后税额: {}", invoiceId, totalTaxAmount);
            }

            // 更新发票主表金额
            FinApInvoice invoice = baseMapper.selectById(invoiceId);
            if (invoice == null) {
                throw new ServiceException("发票不存在，ID: " + invoiceId);
            }

            // 记录更新前的金额
            BigDecimal oldAmount = invoice.getAmount();
            BigDecimal oldAmountExclusiveTax = invoice.getAmountExclusiveTax();
            BigDecimal oldTaxAmount = invoice.getTaxAmount();

            // 更新金额
            invoice.setAmount(totalAmount);
            invoice.setAmountExclusiveTax(totalAmountExclusiveTax);
            invoice.setTaxAmount(totalTaxAmount);

            boolean updateResult = baseMapper.updateById(invoice) > 0;

            if (updateResult) {
                log.info("发票金额汇总更新成功 - 发票ID: {}, 发票编号: {}", invoiceId, invoice.getInvoiceCode());
                log.info("金额变化 - 含税金额: {} -> {}, 不含税金额: {} -> {}, 税额: {} -> {}",
                    AmountCalculationUtils.formatAmount(oldAmount), AmountCalculationUtils.formatAmount(totalAmount),
                    AmountCalculationUtils.formatAmount(oldAmountExclusiveTax), AmountCalculationUtils.formatAmount(totalAmountExclusiveTax),
                    AmountCalculationUtils.formatAmount(oldTaxAmount), AmountCalculationUtils.formatAmount(totalTaxAmount));
            } else {
                throw new ServiceException("发票金额更新失败");
            }

        } catch (Exception e) {
            log.error("发票金额汇总失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            throw new ServiceException("发票金额汇总失败：" + e.getMessage());
        }
    }

    /**
     * 校验状态流转合法性
     */
    private boolean isValidStatusTransition(FinApInvoiceStatus fromStatus, FinApInvoiceStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return true;
        }

        // 定义合法的状态流转
        switch (fromStatus) {
            case PENDING:
                return toStatus == FinApInvoiceStatus.APPROVED ||
                    toStatus == FinApInvoiceStatus.REJECTED ||
                    toStatus == FinApInvoiceStatus.PENDING;
            case APPROVED:
                return toStatus == FinApInvoiceStatus.PARTIALLY_PAID ||
                    toStatus == FinApInvoiceStatus.FULLY_PAID ||
                    toStatus == FinApInvoiceStatus.CANCELLED;
            case REJECTED:
                return toStatus == FinApInvoiceStatus.PENDING ||
                    toStatus == FinApInvoiceStatus.CANCELLED;
            case PARTIALLY_PAID:
                return toStatus == FinApInvoiceStatus.FULLY_PAID ||
                    toStatus == FinApInvoiceStatus.CANCELLED;
            case FULLY_PAID:
            case CANCELLED:
                return toStatus == fromStatus; // 终态，不能再变更
            default:
                return false;
        }
    }

}
