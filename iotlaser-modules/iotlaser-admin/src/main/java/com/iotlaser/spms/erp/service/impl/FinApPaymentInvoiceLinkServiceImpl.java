package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinApPaymentInvoiceLink;
import com.iotlaser.spms.erp.domain.bo.FinApPaymentInvoiceLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentInvoiceLinkVo;
import com.iotlaser.spms.erp.domain.vo.FinApPaymentOrderVo;
import com.iotlaser.spms.erp.mapper.FinApPaymentInvoiceLinkMapper;
import com.iotlaser.spms.erp.service.IFinApInvoiceService;
import com.iotlaser.spms.erp.service.IFinApPaymentInvoiceLinkService;
import com.iotlaser.spms.erp.service.IFinApPaymentOrderService;
import com.iotlaser.spms.erp.utils.AmountCalculationUtils;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 付款单与发票核销关系Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@Service
public class FinApPaymentInvoiceLinkServiceImpl implements IFinApPaymentInvoiceLinkService {

    private final FinApPaymentInvoiceLinkMapper baseMapper;

    @Autowired
    private ApplicationContext applicationContext;

    // Service依赖注入 - 用于金额校验和状态更新
    private IFinApPaymentOrderService finApPaymentOrderService;
    private IFinApInvoiceService finApInvoiceService;

    public FinApPaymentInvoiceLinkServiceImpl(FinApPaymentInvoiceLinkMapper baseMapper) {
        this.baseMapper = baseMapper;
    }

    /**
     * 延迟获取Service实例，避免循环依赖
     */
    private IFinApPaymentOrderService getPaymentOrderService() {
        if (finApPaymentOrderService == null) {
            try {
                finApPaymentOrderService = applicationContext.getBean(IFinApPaymentOrderService.class);
            } catch (Exception e) {
                log.warn("无法获取FinApPaymentOrderService实例，金额校验功能将受限: {}", e.getMessage());
            }
        }
        return finApPaymentOrderService;
    }

    /**
     * 延迟获取Service实例，避免循环依赖
     */
    private IFinApInvoiceService getInvoiceService() {
        if (finApInvoiceService == null) {
            try {
                finApInvoiceService = applicationContext.getBean(IFinApInvoiceService.class);
            } catch (Exception e) {
                log.warn("无法获取FinApInvoiceService实例，金额校验功能将受限: {}", e.getMessage());
            }
        }
        return finApInvoiceService;
    }

    /**
     * 查询付款单与发票核销关系
     *
     * @param linkId 主键
     * @return 付款单与发票核销关系
     */
    @Override
    public FinApPaymentInvoiceLinkVo queryById(Long linkId) {
        return baseMapper.selectVoById(linkId);
    }

    /**
     * 分页查询付款单与发票核销关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 付款单与发票核销关系分页列表
     */
    @Override
    public TableDataInfo<FinApPaymentInvoiceLinkVo> queryPageList(FinApPaymentInvoiceLinkBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> lqw = buildQueryWrapper(bo);
        Page<FinApPaymentInvoiceLinkVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的付款单与发票核销关系列表
     *
     * @param bo 查询条件
     * @return 付款单与发票核销关系列表
     */
    @Override
    public List<FinApPaymentInvoiceLinkVo> queryList(FinApPaymentInvoiceLinkBo bo) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinApPaymentInvoiceLink> buildQueryWrapper(FinApPaymentInvoiceLinkBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinApPaymentInvoiceLink> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinApPaymentInvoiceLink::getLinkId);
        lqw.eq(bo.getPaymentId() != null, FinApPaymentInvoiceLink::getPaymentId, bo.getPaymentId());
        lqw.eq(bo.getInvoiceId() != null, FinApPaymentInvoiceLink::getInvoiceId, bo.getInvoiceId());
        lqw.eq(bo.getAppliedAmount() != null, FinApPaymentInvoiceLink::getAppliedAmount, bo.getAppliedAmount());
        lqw.eq(bo.getCancellationDate() != null, FinApPaymentInvoiceLink::getCancellationDate, bo.getCancellationDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinApPaymentInvoiceLink::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增付款单与发票核销关系
     *
     * @param bo 付款单与发票核销关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinApPaymentInvoiceLinkBo bo) {
        FinApPaymentInvoiceLink add = MapstructUtils.convert(bo, FinApPaymentInvoiceLink.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLinkId(add.getLinkId());
        }
        return flag;
    }

    /**
     * 修改付款单与发票核销关系
     *
     * @param bo 付款单与发票核销关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinApPaymentInvoiceLinkBo bo) {
        FinApPaymentInvoiceLink update = MapstructUtils.convert(bo, FinApPaymentInvoiceLink.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinApPaymentInvoiceLink entity) {
        // 数据校验：检查唯一约束和必填字段
        validateUniqueConstraint(entity);
        validateRequiredFields(entity);
    }

    /**
     * 校验并批量删除付款单与发票核销关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验付款发票核销关系是否可以删除
            List<FinApPaymentInvoiceLink> links = baseMapper.selectByIds(ids);
            for (FinApPaymentInvoiceLink link : links) {
                // 检查核销状态，已确认的核销关系不能删除
                if ("CONFIRMED".equals(link.getStatus())) {
                    throw new ServiceException("付款发票核销关系【" + link.getLinkId() + "】已确认，不允许删除");
                }

                // 检查关联的付款单状态
                // TODO: 添加对付款单状态的检查
                // 如果付款单已审批，则不允许删除核销关系

                // 检查关联的发票状态
                // TODO: 添加对发票状态的检查
                // 如果发票已审批，则不允许删除核销关系

                log.info("删除付款发票核销关系校验通过：{}", link.getLinkId());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除付款发票核销关系成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除付款发票核销关系失败：{}", e.getMessage(), e);
            throw new ServiceException("删除付款发票核销关系失败：" + e.getMessage());
        }
    }

    /**
     * 获取发票已核销金额
     *
     * @param invoiceId 发票ID
     * @return 已核销金额
     */
    @Override
    public BigDecimal getAppliedAmountByInvoiceId(Long invoiceId) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApPaymentInvoiceLink::getInvoiceId, invoiceId);
        wrapper.eq(FinApPaymentInvoiceLink::getStatus, "1"); // 有效状态

        List<FinApPaymentInvoiceLink> links = baseMapper.selectList(wrapper);
        return links.stream()
            .map(FinApPaymentInvoiceLink::getAppliedAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取付款单已核销金额
     *
     * @param paymentId 付款单ID
     * @return 已核销金额
     */
    @Override
    public BigDecimal getAppliedAmountByPaymentId(Long paymentId) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApPaymentInvoiceLink::getPaymentId, paymentId);
        wrapper.eq(FinApPaymentInvoiceLink::getStatus, "1"); // 有效状态

        List<FinApPaymentInvoiceLink> links = baseMapper.selectList(wrapper);
        return links.stream()
            .map(FinApPaymentInvoiceLink::getAppliedAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 校验唯一约束
     *
     * @param entity 实体对象
     */
    private void validateUniqueConstraint(FinApPaymentInvoiceLink entity) {
        // 检查同一付款单和发票的核销记录是否已存在
        LambdaQueryWrapper<FinApPaymentInvoiceLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApPaymentInvoiceLink::getPaymentId, entity.getPaymentId());
        wrapper.eq(FinApPaymentInvoiceLink::getInvoiceId, entity.getInvoiceId());

        // 如果是更新操作，排除当前记录
        if (entity.getLinkId() != null) {
            wrapper.ne(FinApPaymentInvoiceLink::getLinkId, entity.getLinkId());
        }

        if (baseMapper.exists(wrapper)) {
            throw new ServiceException("该付款单与发票的核销记录已存在，不能重复创建");
        }
    }

    /**
     * 校验必填字段
     *
     * @param entity 实体对象
     */
    private void validateRequiredFields(FinApPaymentInvoiceLink entity) {
        if (entity.getPaymentId() == null) {
            throw new ServiceException("付款单ID不能为空");
        }
        if (entity.getInvoiceId() == null) {
            throw new ServiceException("应付单ID不能为空");
        }
        if (entity.getAppliedAmount() == null || entity.getAppliedAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("核销金额必须大于0");
        }
    }

    /**
     * 付款单与应付单主单据级核销
     *
     * @param paymentId     付款单ID
     * @param invoiceId     应付单ID
     * @param appliedAmount 核销金额
     * @param remark        备注
     * @return 是否核销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyPaymentToInvoice(Long paymentId, Long invoiceId,
                                         BigDecimal appliedAmount, String remark) {
        try {
            // 参数校验
            if (paymentId == null) {
                throw new ServiceException("付款单ID不能为空");
            }
            if (invoiceId == null) {
                throw new ServiceException("应付单ID不能为空");
            }
            if (appliedAmount == null || appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("核销金额必须大于0");
            }

            // 校验付款单和应付单是否存在并获取详细信息
            FinApPaymentOrderVo payment = null;
            FinApInvoiceVo invoice = null;

            IFinApPaymentOrderService paymentService = getPaymentOrderService();
            IFinApInvoiceService invoiceService = getInvoiceService();

            if (paymentService != null) {
                payment = paymentService.queryById(paymentId);
                if (payment == null) {
                    throw new ServiceException("付款单不存在，ID: " + paymentId);
                }
            } else {
                log.warn("付款单服务不可用，跳过付款单存在性校验");
            }

            if (invoiceService != null) {
                invoice = invoiceService.queryById(invoiceId);
                if (invoice == null) {
                    throw new ServiceException("应付发票不存在，ID: " + invoiceId);
                }
            } else {
                log.warn("应付发票服务不可用，跳过发票存在性校验");
            }

            // 校验核销金额不能超过可核销金额
            BigDecimal invoiceApplied = getAppliedAmountByInvoiceId(invoiceId);
            BigDecimal paymentApplied = getAppliedAmountByPaymentId(paymentId);

            // 获取应付单和付款单的总金额进行校验
            if (invoice != null && invoice.getAmount() != null) {
                BigDecimal invoiceAvailable = AmountCalculationUtils.safeSubtract(invoice.getAmount(), invoiceApplied);
                if (AmountCalculationUtils.safeCompare(appliedAmount, invoiceAvailable) > 0) {
                    throw new ServiceException(String.format("核销金额(%s)不能超过应付单可核销金额(%s)",
                        AmountCalculationUtils.formatAmount(appliedAmount),
                        AmountCalculationUtils.formatAmount(invoiceAvailable)));
                }
                log.info("应付发票金额校验通过 - 发票ID: {}, 总金额: {}, 已核销: {}, 可核销: {}, 本次核销: {}",
                    invoiceId, invoice.getAmount(), invoiceApplied, invoiceAvailable, appliedAmount);
            }

            if (payment != null && payment.getPaymentAmount() != null) {
                BigDecimal paymentAvailable = AmountCalculationUtils.safeSubtract(payment.getPaymentAmount(), paymentApplied);
                if (AmountCalculationUtils.safeCompare(appliedAmount, paymentAvailable) > 0) {
                    throw new ServiceException(String.format("核销金额(%s)不能超过付款单可核销金额(%s)",
                        AmountCalculationUtils.formatAmount(appliedAmount),
                        AmountCalculationUtils.formatAmount(paymentAvailable)));
                }
                log.info("付款单金额校验通过 - 付款单ID: {}, 总金额: {}, 已核销: {}, 可核销: {}, 本次核销: {}",
                    paymentId, payment.getPaymentAmount(), paymentApplied, paymentAvailable, appliedAmount);
            }

            // 校验供应商一致性（如果两个单据都有供应商信息）
            if (payment != null && invoice != null && payment.getSupplierId() != null && invoice.getPayeeId() != null) {
                if (!payment.getSupplierId().equals(invoice.getPayeeId())) {
                    throw new ServiceException("付款单与应付发票的供应商不一致，无法核销");
                }
                log.info("供应商一致性校验通过 - 供应商ID: {}", payment.getSupplierId());
            }

            // 检查是否已存在核销记录
            LambdaQueryWrapper<FinApPaymentInvoiceLink> checkWrapper = Wrappers.lambdaQuery();
            checkWrapper.eq(FinApPaymentInvoiceLink::getPaymentId, paymentId);
            checkWrapper.eq(FinApPaymentInvoiceLink::getInvoiceId, invoiceId);
            checkWrapper.eq(FinApPaymentInvoiceLink::getStatus, "1"); // 只检查有效记录
            if (baseMapper.exists(checkWrapper)) {
                throw new ServiceException("该付款单与应付单的核销记录已存在，不能重复核销");
            }

            // 创建核销记录
            FinApPaymentInvoiceLink link = new FinApPaymentInvoiceLink();
            link.setPaymentId(paymentId);
            link.setInvoiceId(invoiceId);
            link.setAppliedAmount(appliedAmount);
            link.setCancellationDate(LocalDate.now());
            link.setRemark(StringUtils.isNotBlank(remark) ? remark : "主单据级核销");

            boolean result = baseMapper.insert(link) > 0;

            if (result) {
                // 更新相关单据状态
                try {
                    updateRelatedDocumentStatus(paymentId, invoiceId);
                } catch (Exception e) {
                    log.warn("核销成功但状态更新失败 - 付款单ID: {}, 应付单ID: {}, 错误: {}",
                        paymentId, invoiceId, e.getMessage());
                }

                log.info("主单据级核销成功 - 付款单ID: {}, 应付单ID: {}, 核销金额: {}",
                    paymentId, invoiceId, appliedAmount);
            }

            return result;
        } catch (Exception e) {
            log.error("主单据级核销失败 - 付款单ID: {}, 应付单ID: {}, 错误: {}",
                paymentId, invoiceId, e.getMessage(), e);
            throw new ServiceException("主单据级核销失败：" + e.getMessage());
        }
    }

    /**
     * 撤销付款单与应付单核销
     *
     * @param linkId 核销关系ID
     * @param reason 撤销原因
     * @return 是否撤销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelPaymentInvoiceLink(Long linkId, String reason) {
        try {
            if (linkId == null) {
                throw new ServiceException("核销关系ID不能为空");
            }

            // 获取核销记录
            FinApPaymentInvoiceLink link = baseMapper.selectById(linkId);
            if (link == null) {
                throw new ServiceException("核销记录不存在");
            }

            // 校验是否可以撤销
            // TODO: 根据业务规则校验是否可以撤销
            // 例如：检查相关单据状态、时间限制等

            // 删除核销记录（逻辑删除）
            boolean result = baseMapper.deleteById(linkId) > 0;

            if (result) {
                log.info("核销撤销成功 - 核销ID: {}, 付款单ID: {}, 应付单ID: {}, 撤销原因: {}",
                    linkId, link.getPaymentId(), link.getInvoiceId(), reason);
            }

            return result;
        } catch (Exception e) {
            log.error("核销撤销失败 - 核销ID: {}, 错误: {}", linkId, e.getMessage(), e);
            throw new ServiceException("核销撤销失败：" + e.getMessage());
        }
    }

    /**
     * 查询应付单的核销状态
     *
     * @param invoiceId 应付单ID
     * @return 核销记录列表
     */
    @Override
    public List<FinApPaymentInvoiceLinkVo> queryInvoiceApplyStatus(Long invoiceId) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApPaymentInvoiceLink::getInvoiceId, invoiceId);
        wrapper.eq(FinApPaymentInvoiceLink::getStatus, "1"); // 有效状态
        wrapper.orderByDesc(FinApPaymentInvoiceLink::getCancellationDate);

        List<FinApPaymentInvoiceLink> links = baseMapper.selectList(wrapper);
        return MapstructUtils.convert(links, FinApPaymentInvoiceLinkVo.class);
    }

    /**
     * 查询付款单的核销记录
     *
     * @param paymentId 付款单ID
     * @return 核销记录列表
     */
    @Override
    public List<FinApPaymentInvoiceLinkVo> queryPaymentApplyRecords(Long paymentId) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApPaymentInvoiceLink::getPaymentId, paymentId);
        wrapper.eq(FinApPaymentInvoiceLink::getStatus, "1"); // 有效状态
        wrapper.orderByDesc(FinApPaymentInvoiceLink::getCancellationDate);

        List<FinApPaymentInvoiceLink> links = baseMapper.selectList(wrapper);
        return MapstructUtils.convert(links, FinApPaymentInvoiceLinkVo.class);
    }

    /**
     * 更新相关单据状态
     *
     * @param paymentId 付款单ID
     * @param invoiceId 应付发票ID
     * @return 是否更新成功
     */
    public Boolean updateRelatedDocumentStatus(Long paymentId, Long invoiceId) {
        try {
            boolean paymentResult = updatePaymentStatus(paymentId);
            boolean invoiceResult = updateInvoiceStatus(invoiceId);

            if (paymentResult && invoiceResult) {
                log.info("相关单据状态更新成功 - 付款单ID: {}, 发票ID: {}", paymentId, invoiceId);
                return true;
            } else {
                log.warn("相关单据状态更新部分失败 - 付款单ID: {}, 发票ID: {}, 付款单更新: {}, 发票更新: {}",
                    paymentId, invoiceId, paymentResult, invoiceResult);
                return false;
            }
        } catch (Exception e) {
            log.error("更新相关单据状态失败 - 付款单ID: {}, 发票ID: {}, 错误: {}", paymentId, invoiceId, e.getMessage(), e);
            throw new ServiceException("更新相关单据状态失败：" + e.getMessage());
        }
    }

    /**
     * 更新付款单状态
     *
     * @param paymentId 付款单ID
     * @return 是否更新成功
     */
    private Boolean updatePaymentStatus(Long paymentId) {
        try {
            // 计算付款单已核销金额
            BigDecimal appliedAmount = getAppliedAmountByPaymentId(paymentId);

            // 获取付款单总金额
            IFinApPaymentOrderService paymentService = getPaymentOrderService();
            if (paymentService != null) {
                FinApPaymentOrderVo payment = paymentService.queryById(paymentId);
                if (payment != null && payment.getPaymentAmount() != null) {
                    BigDecimal totalAmount = payment.getPaymentAmount();

                    // 计算新状态
                    String newStatus;
                    if (AmountCalculationUtils.safeCompare(appliedAmount, BigDecimal.ZERO) == 0) {
                        newStatus = "APPROVED"; // 未核销
                    } else if (AmountCalculationUtils.safeCompare(appliedAmount, totalAmount) >= 0) {
                        newStatus = "FULLY_APPLIED"; // 完全核销
                    } else {
                        newStatus = "PARTIALLY_APPLIED"; // 部分核销
                    }

                    // 更新付款单状态
                    // TODO: 需要在PaymentOrderService中实现updatePaymentStatus方法
                    // boolean updateResult = paymentService.updatePaymentStatus(paymentId, newStatus);

                    log.info("付款单状态计算完成 - 付款单ID: {}, 总金额: {}, 已核销金额: {}, 新状态: {}",
                        paymentId, totalAmount, appliedAmount, newStatus);

                    // 暂时返回true，待实现updatePaymentStatus方法后启用
                    return true;
                } else {
                    log.warn("付款单信息不完整，无法更新状态 - 付款单ID: {}", paymentId);
                    return false;
                }
            } else {
                log.warn("付款单服务不可用，无法更新状态 - 付款单ID: {}", paymentId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新付款单状态失败 - 付款单ID: {}, 错误: {}", paymentId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新应付发票状态
     *
     * @param invoiceId 应付发票ID
     * @return 是否更新成功
     */
    private Boolean updateInvoiceStatus(Long invoiceId) {
        try {
            // 计算应付发票已核销金额
            BigDecimal appliedAmount = getAppliedAmountByInvoiceId(invoiceId);

            // 获取应付发票总金额
            IFinApInvoiceService invoiceService = getInvoiceService();
            if (invoiceService != null) {
                FinApInvoiceVo invoice = invoiceService.queryById(invoiceId);
                if (invoice != null && invoice.getAmount() != null) {
                    BigDecimal totalAmount = invoice.getAmount();

                    // 计算新状态
                    String newStatus;
                    if (AmountCalculationUtils.safeCompare(appliedAmount, BigDecimal.ZERO) == 0) {
                        newStatus = "APPROVED"; // 未付款（保持已审批状态）
                    } else if (AmountCalculationUtils.safeCompare(appliedAmount, totalAmount) >= 0) {
                        newStatus = "FULLY_PAID"; // 完全付款
                    } else {
                        newStatus = "PARTIALLY_PAID"; // 部分付款
                    }

                    // 更新应付发票状态
                    // TODO: 需要在InvoiceService中实现updateInvoiceStatus方法
                    // boolean updateResult = invoiceService.updateInvoiceStatus(invoiceId, newStatus);

                    log.info("应付发票状态计算完成 - 发票ID: {}, 总金额: {}, 已核销金额: {}, 新状态: {}",
                        invoiceId, totalAmount, appliedAmount, newStatus);

                    // 暂时返回true，待实现updateInvoiceStatus方法后启用
                    return true;
                } else {
                    log.warn("应付发票信息不完整，无法更新状态 - 发票ID: {}", invoiceId);
                    return false;
                }
            } else {
                log.warn("应付发票服务不可用，无法更新状态 - 发票ID: {}", invoiceId);
                return false;
            }
        } catch (Exception e) {
            log.error("更新应付发票状态失败 - 发票ID: {}, 错误: {}", invoiceId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查是否存在核销记录
     *
     * @param invoiceId 发票ID
     * @return 是否存在核销记录
     */
    @Override
    public Boolean existsByInvoiceId(Long invoiceId) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApPaymentInvoiceLink::getInvoiceId, invoiceId);
        wrapper.eq(FinApPaymentInvoiceLink::getStatus, "1"); // 有效状态
        return baseMapper.exists(wrapper);
    }

    /**
     * 检查是否存在核销记录
     *
     * @param paymentId 付款单ID
     * @return 是否存在核销记录
     */
    @Override
    public Boolean existsByPaymentId(Long paymentId) {
        LambdaQueryWrapper<FinApPaymentInvoiceLink> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinApPaymentInvoiceLink::getPaymentId, paymentId);
        wrapper.eq(FinApPaymentInvoiceLink::getStatus, "1"); // 有效状态
        return baseMapper.exists(wrapper);
    }
}
