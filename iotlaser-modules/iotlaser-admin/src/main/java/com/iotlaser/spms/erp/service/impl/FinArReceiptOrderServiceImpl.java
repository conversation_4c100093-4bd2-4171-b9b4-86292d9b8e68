package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinArReceiptOrder;
import com.iotlaser.spms.erp.domain.FinArReceiptReceivableLink;
import com.iotlaser.spms.erp.domain.bo.FinArReceiptOrderBo;
import com.iotlaser.spms.erp.domain.bo.FinArReceiptReceivableLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinArReceiptOrderVo;
import com.iotlaser.spms.erp.domain.vo.FinArReceivableVo;
import com.iotlaser.spms.erp.mapper.FinArReceiptOrderMapper;
import com.iotlaser.spms.erp.service.IFinArReceiptOrderService;
import com.iotlaser.spms.erp.service.IFinArReceiptReceivableLinkService;
import com.iotlaser.spms.erp.service.IFinArReceivableService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 收款单Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinArReceiptOrderServiceImpl implements IFinArReceiptOrderService {

    private final FinArReceiptOrderMapper baseMapper;
    private final IFinArReceivableService finArReceivableService;
    private final IFinArReceiptReceivableLinkService finArReceiptReceivableLinkService;

    /**
     * 查询收款单
     *
     * @param receiptId 主键
     * @return 收款单
     */
    @Override
    public FinArReceiptOrderVo queryById(Long receiptId) {
        return baseMapper.selectVoById(receiptId);
    }

    /**
     * 分页查询收款单列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 收款单分页列表
     */
    @Override
    public TableDataInfo<FinArReceiptOrderVo> queryPageList(FinArReceiptOrderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinArReceiptOrder> lqw = buildQueryWrapper(bo);
        Page<FinArReceiptOrderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的收款单列表
     *
     * @param bo 查询条件
     * @return 收款单列表
     */
    @Override
    public List<FinArReceiptOrderVo> queryList(FinArReceiptOrderBo bo) {
        LambdaQueryWrapper<FinArReceiptOrder> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinArReceiptOrder> buildQueryWrapper(FinArReceiptOrderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinArReceiptOrder> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinArReceiptOrder::getReceiptId);
        lqw.eq(StringUtils.isNotBlank(bo.getReceiptCode()), FinArReceiptOrder::getReceiptCode, bo.getReceiptCode());
        lqw.eq(bo.getCustomerId() != null, FinArReceiptOrder::getCustomerId, bo.getCustomerId());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), FinArReceiptOrder::getCustomerName, bo.getCustomerName());
        lqw.eq(bo.getPaymentDate() != null, FinArReceiptOrder::getPaymentDate, bo.getPaymentDate());
        lqw.eq(bo.getPaymentAmount() != null, FinArReceiptOrder::getPaymentAmount, bo.getPaymentAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getPaymentMethod()), FinArReceiptOrder::getPaymentMethod, bo.getPaymentMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getBankSerialNumber()), FinArReceiptOrder::getBankSerialNumber, bo.getBankSerialNumber());
        lqw.eq(bo.getAppliedAmount() != null, FinArReceiptOrder::getAppliedAmount, bo.getAppliedAmount());
        lqw.eq(bo.getUnappliedAmount() != null, FinArReceiptOrder::getUnappliedAmount, bo.getUnappliedAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getReceiptStatus()), FinArReceiptOrder::getReceiptStatus, bo.getReceiptStatus());
        // TODO: FinArReceiptOrder实体需要新增handlerId字段用于经办人ID查询
        // lqw.eq(bo.getHandlerId() != null, FinArReceiptOrder::getHandlerId, bo.getHandlerId());
        // TODO: FinArReceiptOrder实体需要新增handlerName字段用于经办人姓名查询
        // lqw.like(StringUtils.isNotBlank(bo.getHandlerName()), FinArReceiptOrder::getHandlerName, bo.getHandlerName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinArReceiptOrder::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增收款单
     *
     * @param bo 收款单
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinArReceiptOrderBo bo) {
        FinArReceiptOrder add = MapstructUtils.convert(bo, FinArReceiptOrder.class);
        validEntityBeforeSave(add);

        // TODO: 完善来源关联信息的设置
        // 当前FinArReceiptOrder实体中的来源关联字段标记为@TableField(exist = false)
        // 待数据库字段添加后，需要设置以下字段：
        // add.setSourceOrderId(bo.getSourceOrderId());
        // add.setSourceOrderCode(bo.getSourceOrderCode());
        // add.setSourceOrderType(bo.getSourceOrderType());
        // add.setSourceOrderName(bo.getSourceOrderName());

        // TODO: 完善经办人信息的设置
        // add.setHandlerId(LoginHelper.getUserId());
        // add.setHandlerName(LoginHelper.getUsername());
        // add.setHandleTime(LocalDateTime.now());

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setReceiptId(add.getReceiptId());
        }
        return flag;
    }

    /**
     * 从销售订单创建收款单
     * TODO: 当前方法为框架方法，待数据库字段添加后完善实现
     *
     * @param orderId       销售订单ID
     * @param orderCode     销售订单编号
     * @param orderName     销售订单名称
     * @param customerId    客户ID
     * @param customerCode  客户编码
     * @param customerName  客户名称
     * @param paymentAmount 收款金额
     * @param paymentMethod 收款方式
     * @param remark        备注
     * @return 收款单ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createReceiptFromSaleOrder(Long orderId, String orderCode, String orderName,
                                           Long customerId, String customerCode, String customerName,
                                           BigDecimal paymentAmount, String paymentMethod, String remark) {
        try {
            log.info("开始从销售订单创建收款单 - 订单: {}, 客户: {}, 金额: {}", orderCode, customerName, paymentAmount);

            // 参数校验
            if (orderId == null || StringUtils.isBlank(orderCode)) {
                throw new ServiceException("销售订单信息不能为空");
            }
            if (customerId == null || StringUtils.isBlank(customerCode)) {
                throw new ServiceException("客户信息不能为空");
            }
            if (paymentAmount == null || paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("收款金额必须大于0");
            }

            // 创建收款单
            FinArReceiptOrder receipt = new FinArReceiptOrder();

            // 生成收款单编号
            receipt.setReceiptCode(generateReceiptCode());
            receipt.setSummary("销售收款-" + orderCode);

            // 设置客户信息
            receipt.setCustomerId(customerId);
            receipt.setCustomerName(customerName);

            // 设置收款信息
            receipt.setPaymentDate(LocalDate.now());
            receipt.setPaymentAmount(paymentAmount);
            receipt.setPaymentMethod(StringUtils.isNotBlank(paymentMethod) ? paymentMethod : "BANK_TRANSFER");
            receipt.setRemark(remark);

            // 设置状态
            receipt.setReceiptStatus("UNAPPLIED"); // 未核销
            receipt.setAppliedAmount(BigDecimal.ZERO);
            receipt.setUnappliedAmount(paymentAmount);
            receipt.setStatus("1");

            // TODO: 设置来源关联信息（当前为临时变量，不会持久化）
            // 待数据库字段添加后，移除@TableField(exist = false)注解并启用以下代码：
            //receipt.setSourceOrderId(orderId);
            //receipt.setSourceOrderCode(orderCode);
            //receipt.setSourceOrderType("SALE_ORDER");
            //receipt.setSourceOrderName(orderName);

            // TODO: 设置经办人信息（当前为临时变量，不会持久化）
            // 待数据库字段添加后，移除@TableField(exist = false)注解并启用以下代码：
            //receipt.setHandlerId(LoginHelper.getUserId());
            //receipt.setHandlerName(LoginHelper.getUsername());
            //receipt.setHandleTime(LocalDateTime.now());

            // 保存收款单
            int result = baseMapper.insert(receipt);
            if (result <= 0) {
                throw new ServiceException("创建收款单失败");
            }

            log.info("从销售订单创建收款单成功 - 订单: {}, 收款单ID: {}, 收款单编号: {}",
                orderCode, receipt.getReceiptId(), receipt.getReceiptCode());

            return receipt.getReceiptId();

        } catch (Exception e) {
            log.error("从销售订单创建收款单失败 - 订单: {}, 错误: {}", orderCode, e.getMessage(), e);
            throw new ServiceException("从销售订单创建收款单失败：" + e.getMessage());
        }
    }

    /**
     * 生成收款单编号
     * TODO: 可以根据业务规则优化编号生成逻辑
     */
    private String generateReceiptCode() {
        // 简单的编号生成逻辑：SK + 年月日 + 6位序号
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String prefix = "SK" + dateStr;

        // 查询当天最大序号
        LambdaQueryWrapper<FinArReceiptOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.likeRight(FinArReceiptOrder::getReceiptCode, prefix);
        wrapper.orderByDesc(FinArReceiptOrder::getReceiptCode);
        wrapper.last("LIMIT 1");

        FinArReceiptOrder lastReceipt = baseMapper.selectOne(wrapper);
        int sequence = 1;
        if (lastReceipt != null && lastReceipt.getReceiptCode().length() >= prefix.length() + 6) {
            try {
                String lastSequence = lastReceipt.getReceiptCode().substring(prefix.length());
                sequence = Integer.parseInt(lastSequence) + 1;
            } catch (NumberFormatException e) {
                log.warn("解析收款单编号序号失败，使用默认序号1");
            }
        }

        return prefix + String.format("%06d", sequence);
    }

    /**
     * 修改收款单
     *
     * @param bo 收款单
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinArReceiptOrderBo bo) {
        FinArReceiptOrder update = MapstructUtils.convert(bo, FinArReceiptOrder.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinArReceiptOrder entity) {
        // 校验收款单编号唯一性
        if (StringUtils.isNotBlank(entity.getReceiptCode())) {
            LambdaQueryWrapper<FinArReceiptOrder> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinArReceiptOrder::getReceiptCode, entity.getReceiptCode());
            if (entity.getReceiptId() != null) {
                wrapper.ne(FinArReceiptOrder::getReceiptId, entity.getReceiptId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("收款单编号已存在：" + entity.getReceiptCode());
            }
        }

        // 校验收款金额（使用正确的字段名paymentAmount）
        if (entity.getPaymentAmount() != null && entity.getPaymentAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("收款金额必须大于0");
        }
    }

    /**
     * 校验并批量删除收款单信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验收款单是否可以删除
            List<FinArReceiptOrder> receiptOrders = baseMapper.selectByIds(ids);
            for (FinArReceiptOrder receiptOrder : receiptOrders) {
                // 检查收款单状态，只有草稿状态的收款单才能删除
                if (!"DRAFT".equals(receiptOrder.getReceiptStatus())) {
                    throw new ServiceException("收款单【" + receiptOrder.getReceiptCode() + "】状态为【" +
                        receiptOrder.getReceiptStatus() + "】，不允许删除");
                }

                // 检查是否有关联的核销记录
                if (finArReceiptReceivableLinkService.existsByReceiptId(receiptOrder.getReceiptId())) {
                    throw new ServiceException("收款单【" + receiptOrder.getReceiptCode() + "】存在核销记录，不允许删除");
                }

                log.info("删除收款单校验通过：{}", receiptOrder.getReceiptCode());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除收款单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除收款单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除收款单失败：" + e.getMessage());
        }
    }

    /**
     * 创建收款单
     *
     * @param customerId       客户ID
     * @param customerCode     客户编码
     * @param customerName     客户名称
     * @param paymentAmount    收款金额
     * @param paymentMethod    收款方式
     * @param bankSerialNumber 银行流水号
     * @param handlerId        登记员ID
     * @param handlerName      登记员姓名
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createReceiptOrder(Long customerId, String customerCode, String customerName,
                                      BigDecimal paymentAmount, String paymentMethod,
                                      String bankSerialNumber, Long handlerId, String handlerName) {
        try {
            FinArReceiptOrder receiptOrder = new FinArReceiptOrder();

            // 生成收款单编号
            receiptOrder.setReceiptCode(generateReceiptCode());
            receiptOrder.setSummary("收款单-" + customerName);

            // 客户信息
            receiptOrder.setCustomerId(customerId);
            receiptOrder.setCustomerName(customerName);

            // 收款信息（使用原字段名称）
            receiptOrder.setPaymentAmount(paymentAmount);
            receiptOrder.setPaymentMethod(paymentMethod);
            receiptOrder.setPaymentDate(LocalDate.now());
            receiptOrder.setBankSerialNumber(bankSerialNumber);

            // 核销金额初始化
            receiptOrder.setAppliedAmount(BigDecimal.ZERO);
            receiptOrder.setUnappliedAmount(paymentAmount);

            // TODO: FinArReceiptOrder实体需要新增handlerId字段用于记录经办人ID
            // receiptOrder.setHandlerId(handlerId);
            // TODO: FinArReceiptOrder实体需要新增handlerName字段用于记录经办人姓名
            // receiptOrder.setHandlerName(handlerName);

            log.info("经办人信息：ID【{}】姓名【{}】", handlerId, handlerName);

            // 状态信息
            receiptOrder.setReceiptStatus("UNAPPLIED");
            receiptOrder.setStatus("1"); // 有效状态

            boolean result = baseMapper.insert(receiptOrder) > 0;

            if (result) {
                log.info("收款单创建成功 - 客户: {}, 金额: {}, 登记员: {}",
                    customerName, paymentAmount, handlerName);
            }

            return result;
        } catch (Exception e) {
            log.error("收款单创建失败 - 客户: {}, 错误: {}", customerName, e.getMessage(), e);
            throw new ServiceException("收款单创建失败：" + e.getMessage());
        }
    }

    /**
     * 收款单核销到应收账款
     *
     * @param receiptOrderId 收款单ID
     * @param receivableId   应收账款ID
     * @param writeoffAmount 核销金额
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否核销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyToReceivable(Long receiptOrderId, Long receivableId, BigDecimal writeoffAmount,
                                     Long operatorId, String operatorName) {
        try {
            // 获取收款单信息
            FinArReceiptOrder receiptOrder = baseMapper.selectById(receiptOrderId);
            if (receiptOrder == null) {
                throw new ServiceException("收款单不存在");
            }

            if (!"UNAPPLIED".equals(receiptOrder.getReceiptStatus()) &&
                !"PARTIALLY_APPLIED".equals(receiptOrder.getReceiptStatus())) {
                throw new ServiceException("收款单状态不允许核销");
            }

            // 获取应收单信息
            FinArReceivableVo receivable = finArReceivableService.queryById(receivableId);
            if (receivable == null) {
                throw new ServiceException("应收单不存在");
            }

            if (!"PENDING".equals(receivable.getReceivableStatus()) &&
                !"PARTIALLY_PAID".equals(receivable.getReceivableStatus())) {
                throw new ServiceException("应收单状态不允许核销");
            }

            // 校验核销金额
            if (writeoffAmount.compareTo(receiptOrder.getUnappliedAmount()) > 0) {
                throw new ServiceException("核销金额不能超过收款单未核销金额");
            }

            // ✅ 使用现有的amount字段而不是totalAmount
            BigDecimal receivableUnpaidAmount = receivable.getAmount().subtract(
                getReceivableAppliedAmount(receivableId));
            if (writeoffAmount.compareTo(receivableUnpaidAmount) > 0) {
                throw new ServiceException("核销金额不能超过应收单未收金额");
            }

            // 创建核销记录
            boolean linkResult = createReceiptReceivableLink(receiptOrder, receivable,
                writeoffAmount, operatorId, operatorName);

            // 更新收款单核销金额
            receiptOrder.setAppliedAmount(receiptOrder.getAppliedAmount().add(writeoffAmount));
            receiptOrder.setUnappliedAmount(receiptOrder.getUnappliedAmount().subtract(writeoffAmount));

            // 更新收款单状态
            if (receiptOrder.getUnappliedAmount().compareTo(BigDecimal.ZERO) == 0) {
                receiptOrder.setReceiptStatus("FULLY_APPLIED");
            } else {
                receiptOrder.setReceiptStatus("PARTIALLY_APPLIED");
            }

            boolean receiptResult = baseMapper.updateById(receiptOrder) > 0;

            // 更新应收单状态
            BigDecimal newReceivableAppliedAmount = getReceivableAppliedAmount(receivableId);
            String newReceivableStatus;
            // ✅ 使用现有的amount字段而不是totalAmount
            if (newReceivableAppliedAmount.compareTo(receivable.getAmount()) == 0) {
                newReceivableStatus = "FULLY_PAID";
            } else {
                newReceivableStatus = "PARTIALLY_PAID";
            }

            boolean receivableResult = finArReceivableService.updateReceivableStatus(receivableId, newReceivableStatus);

            if (linkResult && receiptResult && receivableResult) {
                log.info("收款核销成功 - 收款单: {}, 应收单: {}, 核销金额: {}",
                    receiptOrder.getReceiptCode(), receivable.getReceivableCode(), writeoffAmount);
                return true;
            } else {
                throw new ServiceException("核销操作失败");
            }

        } catch (Exception e) {
            log.error("收款核销失败 - 收款单ID: {}, 应收单ID: {}, 错误: {}",
                receiptOrderId, receivableId, e.getMessage(), e);
            throw new ServiceException("收款核销失败：" + e.getMessage());
        }
    }

    /**
     * 创建收款应收核销记录
     */
    private boolean createReceiptReceivableLink(FinArReceiptOrder receipt, FinArReceivableVo receivable,
                                                BigDecimal appliedAmount, Long appliedById, String appliedByName) {
        FinArReceiptReceivableLink link = new FinArReceiptReceivableLink();

        // 关联ID（核心字段）
        link.setReceiptId(receipt.getReceiptId());
        link.setReceivableId(receivable.getReceivableId());

        // 核销信息
        link.setAppliedAmount(appliedAmount);
        link.setCancellationDate(LocalDate.now());  // 修复：使用LocalDate而不是Date
        // TODO: FinArReceiptReceivableLink实体需要新增handlerId字段用于记录经办人ID
        // link.setHandlerId(appliedById);
        // TODO: FinArReceiptReceivableLink实体需要新增handlerName字段用于记录经办人姓名
        // link.setHandlerName(appliedByName);

        log.info("核销记录：经办人【{}】核销时间【{}】", appliedByName, LocalDate.now());
        link.setStatus("1"); // 有效状态

        return finArReceiptReceivableLinkService.insertByBo(
            MapstructUtils.convert(link, FinArReceiptReceivableLinkBo.class));
    }

    /**
     * 获取应收单已核销金额
     */
    private BigDecimal getReceivableAppliedAmount(Long receivableId) {
        return finArReceiptReceivableLinkService.getAppliedAmountByReceivableId(receivableId);
    }

    /**
     * 确认收款单
     *
     * @param receiptId     收款单ID
     * @param confirmById   确认人ID
     * @param confirmByName 确认人姓名
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmReceipt(Long receiptId, Long confirmById, String confirmByName) {
        try {
            FinArReceiptOrder receipt = baseMapper.selectById(receiptId);
            if (receipt == null) {
                throw new ServiceException("收款单不存在");
            }

            if (!"UNAPPLIED".equals(receipt.getReceiptStatus())) {
                throw new ServiceException("收款单状态不允许确认");
            }

            receipt.setReceiptStatus("CONFIRMED");
            boolean result = baseMapper.updateById(receipt) > 0;

            if (result) {
                log.info("收款单确认成功 - 收款单: {}, 确认人: {}", receipt.getReceiptCode(), confirmByName);
            }

            return result;
        } catch (Exception e) {
            log.error("收款单确认失败 - 收款单ID: {}, 错误: {}", receiptId, e.getMessage(), e);
            throw new ServiceException("收款单确认失败：" + e.getMessage());
        }
    }

    /**
     * 取消收款单
     *
     * @param receiptId    收款单ID
     * @param cancelById   取消人ID
     * @param cancelByName 取消人姓名
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelReceipt(Long receiptId, Long cancelById, String cancelByName, String cancelReason) {
        try {
            FinArReceiptOrder receipt = baseMapper.selectById(receiptId);
            if (receipt == null) {
                throw new ServiceException("收款单不存在");
            }

            if ("FULLY_APPLIED".equals(receipt.getReceiptStatus()) || "CANCELLED".equals(receipt.getReceiptStatus())) {
                throw new ServiceException("收款单状态不允许取消");
            }

            receipt.setReceiptStatus("CANCELLED");
            receipt.setRemark(cancelReason);
            boolean result = baseMapper.updateById(receipt) > 0;

            if (result) {
                log.info("收款单取消成功 - 收款单: {}, 取消人: {}, 原因: {}",
                    receipt.getReceiptCode(), cancelByName, cancelReason);
            }

            return result;
        } catch (Exception e) {
            log.error("收款单取消失败 - 收款单ID: {}, 错误: {}", receiptId, e.getMessage(), e);
            throw new ServiceException("收款单取消失败：" + e.getMessage());
        }
    }


    /**
     * 预收款处理
     *
     * @param receiptId     收款单ID
     * @param advanceAmount 预收金额
     * @return 是否处理成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleAdvancePayment(Long receiptId, BigDecimal advanceAmount) {
        try {
            FinArReceiptOrder receipt = baseMapper.selectById(receiptId);
            if (receipt == null) {
                throw new ServiceException("收款单不存在");
            }

            if (!"CONFIRMED".equals(receipt.getReceiptStatus())) {
                throw new ServiceException("只有已确认的收款单才能处理预收款");
            }

            if (advanceAmount == null || advanceAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("预收金额必须大于0");
            }

            if (advanceAmount.compareTo(receipt.getPaymentAmount()) > 0) {
                throw new ServiceException("预收金额不能超过收款单总金额");
            }

            // 创建预收款记录
            Long advanceId = createAdvancePaymentRecord(receiptId, advanceAmount);

            // 更新收款单状态
            receipt.setReceiptStatus("ADVANCE_PAYMENT");
            // receipt.setAdvanceAmount(advanceAmount); // 如果有此字段
            baseMapper.updateById(receipt);

            // 更新客户预收款余额
            updateCustomerAdvanceBalance(receipt.getCustomerId(), advanceAmount);

            log.info("预收款处理成功 - 收款单: {}, 预收金额: {}, 预收记录ID: {}",
                receipt.getReceiptCode(), advanceAmount, advanceId);

            return true;
        } catch (Exception e) {
            log.error("预收款处理失败 - 收款单ID: {}, 错误: {}", receiptId, e.getMessage(), e);
            throw new ServiceException("预收款处理失败：" + e.getMessage());
        }
    }

    /**
     * 预收款核销
     *
     * @param receiptId    收款单ID
     * @param receivableId 应收账款ID
     * @param amount       核销金额
     * @return 是否核销成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyAdvancePayment(Long receiptId, Long receivableId, BigDecimal amount) {
        try {
            // 校验收款单
            FinArReceiptOrder receipt = baseMapper.selectById(receiptId);
            if (receipt == null) {
                throw new ServiceException("收款单不存在");
            }

            if (!"ADVANCE_PAYMENT".equals(receipt.getReceiptStatus())) {
                throw new ServiceException("只有预收款状态的收款单才能进行核销");
            }

            // 校验应收账款
            // TODO: 获取应收账款信息进行校验
            // FinArReceivable receivable = finArReceivableService.queryById(receivableId);

            // 校验核销金额
            if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("核销金额必须大于0");
            }

            BigDecimal availableAdvance = getAvailableAdvanceAmount(receiptId);
            if (amount.compareTo(availableAdvance) > 0) {
                throw new ServiceException("核销金额不能超过可用预收款金额：" + availableAdvance);
            }

            // 创建预收款核销记录
            Long linkId = createAdvanceApplyRecord(receiptId, receivableId, amount);

            // 更新预收款和应收账款状态
            updateAdvanceApplyStatus(receiptId, receivableId, amount);

            log.info("预收款核销成功 - 收款单: {}, 应收账款ID: {}, 核销金额: {}, 核销记录ID: {}",
                receipt.getReceiptCode(), receivableId, amount, linkId);

            return true;
        } catch (Exception e) {
            log.error("预收款核销失败 - 收款单ID: {}, 应收账款ID: {}, 错误: {}", receiptId, receivableId, e.getMessage(), e);
            throw new ServiceException("预收款核销失败：" + e.getMessage());
        }
    }

    /**
     * 获取应收核销建议
     *
     * @param receiptId 收款单ID
     * @return 核销建议列表
     */
    public List<ReceivableApplyRecommendation> getReceivableApplyRecommendations(Long receiptId) {
        try {
            FinArReceiptOrder receipt = baseMapper.selectById(receiptId);
            if (receipt == null) {
                throw new ServiceException("收款单不存在");
            }

            if (!"CONFIRMED".equals(receipt.getReceiptStatus())) {
                throw new ServiceException("只有已确认的收款单才能获取核销建议");
            }

            List<ReceivableApplyRecommendation> recommendations = new ArrayList<>();

            // 基于客户查找待核销应收账款
            List<FinArReceivableVo> candidateReceivables = findCandidateReceivables(receipt);

            // 计算每个应收账款的匹配度和建议核销金额
            for (FinArReceivableVo receivable : candidateReceivables) {
                ReceivableApplyRecommendation recommendation = calculateReceivableRecommendation(receipt, receivable);
                if (recommendation.getMatchScore() > 0.5) { // 匹配度大于50%才推荐
                    recommendations.add(recommendation);
                }
            }

            // 按匹配度排序
            recommendations.sort((r1, r2) -> Double.compare(r2.getMatchScore(), r1.getMatchScore()));

            // 限制推荐数量
            int maxRecommendations = 10;
            if (recommendations.size() > maxRecommendations) {
                recommendations = recommendations.subList(0, maxRecommendations);
            }

            log.info("获取收款单核销建议成功 - 收款单: {}, 建议数量: {}",
                receipt.getReceiptCode(), recommendations.size());

            return recommendations;
        } catch (Exception e) {
            log.error("获取收款单核销建议失败 - 收款单ID: {}, 错误: {}", receiptId, e.getMessage(), e);
            throw new ServiceException("获取核销建议失败：" + e.getMessage());
        }
    }

    /**
     * 核销差异处理
     *
     * @param linkId           核销记录ID
     * @param differenceAmount 差异金额
     * @param differenceReason 差异原因
     * @return 是否处理成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleApplyDifference(Long linkId, BigDecimal differenceAmount, String differenceReason) {
        try {
            if (differenceAmount == null || differenceAmount.compareTo(BigDecimal.ZERO) == 0) {
                throw new ServiceException("差异金额不能为0");
            }

            if (StringUtils.isBlank(differenceReason)) {
                throw new ServiceException("差异原因不能为空");
            }

            // 获取核销记录
            // TODO: 获取核销记录详情
            // FinArReceiptReceivableLink link = finArReceiptReceivableLinkService.queryById(linkId);

            // 处理差异
            if (differenceAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 正差异：收款金额大于应收金额
                handlePositiveDifference(linkId, differenceAmount, differenceReason);
            } else {
                // 负差异：收款金额小于应收金额
                handleNegativeDifference(linkId, differenceAmount.abs(), differenceReason);
            }

            log.info("核销差异处理成功 - 核销记录ID: {}, 差异金额: {}",
                linkId, differenceAmount);

            return true;
        } catch (Exception e) {
            log.error("核销差异处理失败 - 核销记录ID: {}, 错误: {}", linkId, e.getMessage(), e);
            throw new ServiceException("核销差异处理失败：" + e.getMessage());
        }
    }

    /**
     * 应收核销撤销
     *
     * @param linkId        核销记录ID
     * @param reverseReason 撤销原因
     * @return 是否撤销成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean reverseReceivableApply(Long linkId, String reverseReason) {
        try {
            // 获取核销记录
            // TODO: 获取核销记录详情
            // FinArReceiptReceivableLink link = finArReceiptReceivableLinkService.queryById(linkId);

            if (StringUtils.isBlank(reverseReason)) {
                throw new ServiceException("撤销原因不能为空");
            }

            // 校验核销记录状态
            // 只有已核销状态的记录才能撤销
            // if (!"APPLIED".equals(link.getLinkStatus())) {
            //     throw new ServiceException("只有已核销状态的记录才能撤销");
            // }

            // 更新核销记录状态为已撤销
            // link.setLinkStatus("REVERSED");
            // link.setReverseReason(reverseReason);
            // link.setReverseTime(LocalDateTime.now());
            // link.setReverseById(LoginHelper.getUserId());
            // link.setReverseByName(LoginHelper.getLoginUser().getNickname());

            // 撤销收款单和应收账款的核销状态
            // reverseReceivableApplyStatus(link.getReceiptId(), link.getReceivableId(), link.getAppliedAmount());

            log.info("应收核销撤销成功 - 核销记录ID: {}, 撤销原因: {}", linkId, reverseReason);

            return true;
        } catch (Exception e) {
            log.error("应收核销撤销失败 - 核销记录ID: {}, 错误: {}", linkId, e.getMessage(), e);
            throw new ServiceException("应收核销撤销失败：" + e.getMessage());
        }
    }

    /**
     * 部分应收核销
     *
     * @param receiptId     收款单ID
     * @param receivableId  应收账款ID
     * @param appliedAmount 核销金额
     * @param applyReason   核销原因
     * @return 是否核销成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean partialReceivableApply(Long receiptId, Long receivableId, BigDecimal appliedAmount, String applyReason) {
        try {
            // 校验收款单
            FinArReceiptOrder receipt = baseMapper.selectById(receiptId);
            if (receipt == null) {
                throw new ServiceException("收款单不存在");
            }

            if (!"CONFIRMED".equals(receipt.getReceiptStatus())) {
                throw new ServiceException("只有已确认的收款单才能核销");
            }

            // 校验应收账款
            // TODO: 获取应收账款信息进行校验
            // FinArReceivable receivable = finArReceivableService.queryById(receivableId);

            // 校验核销金额
            if (appliedAmount == null || appliedAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("核销金额必须大于0");
            }

            // 获取收款单剩余可核销金额
            BigDecimal remainingAmount = getRemainingReceiptAmount(receiptId);
            if (appliedAmount.compareTo(remainingAmount) > 0) {
                throw new ServiceException("核销金额不能超过收款单剩余金额：" + remainingAmount);
            }

            // 创建核销记录
            Long linkId = createReceivableApplyRecord(receiptId, receivableId, appliedAmount, applyReason);

            // 更新收款单和应收账款的核销状态
            updateReceivableApplyStatus(receiptId, receivableId, appliedAmount);

            log.info("部分应收核销成功 - 收款单: {}, 应收账款ID: {}, 核销金额: {}, 核销记录ID: {}",
                receipt.getReceiptCode(), receivableId, appliedAmount, linkId);

            return true;
        } catch (Exception e) {
            log.error("部分应收核销失败 - 收款单ID: {}, 应收账款ID: {}, 错误: {}", receiptId, receivableId, e.getMessage(), e);
            throw new ServiceException("部分应收核销失败：" + e.getMessage());
        }
    }

    /**
     * 批量应收核销
     *
     * @param batchApplyRequests 批量核销请求列表
     * @return 批量核销结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchReceivableApply(List<ReceivableApplyRequest> batchApplyRequests) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (ReceivableApplyRequest request : batchApplyRequests) {
                try {
                    Boolean applyResult = partialReceivableApply(
                        request.getReceiptId(),
                        request.getReceivableId(),
                        request.getAppliedAmount(),
                        request.getApplyReason()
                    );

                    if (applyResult) {
                        successList.add(Map.of(
                            "receiptId", request.getReceiptId(),
                            "receivableId", request.getReceivableId(),
                            "appliedAmount", request.getAppliedAmount(),
                            "status", "SUCCESS"
                        ));
                    } else {
                        failureList.add(Map.of(
                            "receiptId", request.getReceiptId(),
                            "receivableId", request.getReceivableId(),
                            "status", "FAILED",
                            "reason", "核销处理失败"
                        ));
                    }
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "receiptId", request.getReceiptId(),
                        "receivableId", request.getReceivableId(),
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", batchApplyRequests.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", LoginHelper.getUserId());
            result.put("operatorName", LoginHelper.getLoginUser().getNickname());
            result.put("operationTime", LocalDateTime.now());

            log.info("批量应收核销完成 - 总数: {}, 成功: {}, 失败: {}",
                batchApplyRequests.size(), successList.size(), failureList.size());

            return result;
        } catch (Exception e) {
            log.error("批量应收核销失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量应收核销失败：" + e.getMessage());
        }
    }

    /**
     * 创建预收款记录
     */
    private Long createAdvancePaymentRecord(Long receiptId, BigDecimal advanceAmount) {
        // TODO: 创建预收款记录到数据库
        // 返回预收款记录ID
        return System.currentTimeMillis();
    }

    /**
     * 更新客户预收款余额
     */
    private void updateCustomerAdvanceBalance(Long customerId, BigDecimal advanceAmount) {
        // TODO: 更新客户的预收款余额
        log.debug("更新客户预收款余额 - 客户ID: {}, 预收金额: {}", customerId, advanceAmount);
    }

    /**
     * 获取可用预收款金额
     */
    private BigDecimal getAvailableAdvanceAmount(Long receiptId) {
        // TODO: 计算收款单的可用预收款金额
        // 预收款总金额 - 已核销金额
        return new BigDecimal("10000.00"); // 模拟数据
    }

    /**
     * 创建预收款核销记录
     */
    private Long createAdvanceApplyRecord(Long receiptId, Long receivableId, BigDecimal amount) {
        // TODO: 创建预收款核销记录到数据库
        // 返回核销记录ID
        return System.currentTimeMillis();
    }

    /**
     * 更新预收款核销状态
     */
    private void updateAdvanceApplyStatus(Long receiptId, Long receivableId, BigDecimal amount) {
        // TODO: 更新预收款和应收账款的核销状态
        // 更新预收款已核销金额
        // 更新应收账款已收款金额
        // 重新计算核销状态
    }

    /**
     * 查找候选应收账款
     */
    private List<FinArReceivableVo> findCandidateReceivables(FinArReceiptOrder receipt) {
        // TODO: 实现候选应收账款查找逻辑
        // 基于客户ID查找未完全收款的应收账款
        // 基于时间范围过滤
        // 基于金额范围过滤
        // 排除已收款完成的应收账款
        return new ArrayList<>();
    }

    /**
     * 计算应收核销建议 (优化版)
     */
    private ReceivableApplyRecommendation calculateReceivableRecommendation(FinArReceiptOrder receipt, FinArReceivableVo receivable) {
        ReceivableApplyRecommendation recommendation = new ReceivableApplyRecommendation();

        // 计算匹配度 (多维度智能算法)
        double matchScore = 0.0;
        StringBuilder matchReason = new StringBuilder();

        // 客户匹配 (权重35%)
        if (Objects.equals(receipt.getCustomerId(), receivable.getCustomerId())) {
            matchScore += 0.35;
            matchReason.append("客户匹配;");
        }

        // 金额匹配 (权重25%)
        if (receipt.getPaymentAmount() != null && receivable.getAmount() != null) {
            BigDecimal amountDiff = receipt.getPaymentAmount().subtract(receivable.getAmount()).abs();
            BigDecimal tolerance = receipt.getPaymentAmount().multiply(new BigDecimal("0.05")); // 5%容差

            if (amountDiff.compareTo(BigDecimal.ZERO) == 0) {
                // 完全匹配
                matchScore += 0.25;
                matchReason.append("金额完全匹配;");
            } else if (amountDiff.compareTo(tolerance) <= 0) {
                // 容差内匹配
                matchScore += 0.20;
                matchReason.append("金额近似匹配;");
            } else if (amountDiff.compareTo(tolerance.multiply(new BigDecimal("2"))) <= 0) {
                // 10%容差内
                matchScore += 0.10;
                matchReason.append("金额部分匹配;");
            }
        }

        // 时间匹配 (权重20%)
        // TODO: 时间匹配需要统一Date和LocalDate类型
        // if (receipt.getPaymentDate() != null && receivable.getInvoiceDate() != null) {
        //     long daysDiff = Math.abs(receipt.getPaymentDate().getTime() - receivable.getInvoiceDate().getTime()) / (1000 * 60 * 60 * 24);
        //     if (daysDiff <= 7) {
        //         // 7天内
        //         matchScore += 0.20;
        //         matchReason.append("时间匹配(7天内);");
        //     } else if (daysDiff <= 30) {
        //         // 30天内
        //         matchScore += 0.15;
        //         matchReason.append("时间匹配(30天内);");
        //     } else if (daysDiff <= 60) {
        //         // 60天内
        //         matchScore += 0.10;
        //         matchReason.append("时间匹配(60天内);");
        //     }
        // }

        // 逾期优先级 (权重10%)
        // TODO: 逾期优先级需要dueDate字段
        // if (receivable.getDueDate() != null) {
        //     LocalDate currentDate = LocalDate.now();
        //     if (receivable.getDueDate().isBefore(currentDate)) {
        //         // 已逾期，优先处理
        //         long overdueDays = (currentDate.getTime() - receivable.getDueDate().getTime()) / (1000 * 60 * 60 * 24);
        //         if (overdueDays > 90) {
        //             matchScore += 0.10;
        //             matchReason.append("严重逾期优先;");
        //         } else if (overdueDays > 30) {
        //             matchScore += 0.08;
        //             matchReason.append("逾期优先;");
        //         } else {
        //             matchScore += 0.05;
        //             matchReason.append("轻微逾期;");
        //         }
        //     }
        // }

        // 应收状态匹配 (权重10%)
        if ("PENDING".equals(receivable.getReceivableStatus()) || "PARTIALLY_PAID".equals(receivable.getReceivableStatus())) {
            matchScore += 0.10;
            matchReason.append("状态可核销;");
        }

        // 设置推荐信息
        recommendation.setReceivableId(receivable.getReceivableId());
        recommendation.setReceivableCode(receivable.getReceivableCode());
        recommendation.setReceivableAmount(receivable.getAmount());
        recommendation.setMatchScore(matchScore);
        recommendation.setMatchReason(matchReason.toString());

        // 智能计算建议核销金额
        BigDecimal receiptRemaining = getRemainingReceiptAmount(receipt.getReceiptId());
        BigDecimal receivableRemaining = getReceivableRemainingAmount(receivable.getReceivableId());
        BigDecimal recommendedAmount = receiptRemaining.min(receivableRemaining);

        // 如果金额完全匹配，建议全额核销
        if (receipt.getPaymentAmount() != null && receivable.getAmount() != null) {
            BigDecimal amountDiff = receipt.getPaymentAmount().subtract(receivable.getAmount()).abs();
            if (amountDiff.compareTo(new BigDecimal("0.01")) <= 0) { // 1分钱容差
                recommendedAmount = receivable.getAmount();
            }
        }

        recommendation.setRecommendedAmount(recommendedAmount);

        return recommendation;
    }

    /**
     * 获取收款单剩余金额
     */
    private BigDecimal getRemainingReceiptAmount(Long receiptId) {
        // TODO: 计算收款单剩余可核销金额
        // 收款单总金额 - 已核销金额
        return BigDecimal.ZERO;
    }

    /**
     * 获取应收账款剩余金额
     */
    private BigDecimal getReceivableRemainingAmount(Long receivableId) {
        // TODO: 计算应收账款剩余金额
        // 应收账款总金额 - 已收款金额
        return BigDecimal.ZERO;
    }

    /**
     * 处理正差异
     */
    private void handlePositiveDifference(Long linkId, BigDecimal differenceAmount, String differenceReason) {
        // TODO: 处理正差异（收款金额大于应收金额）
        // 记录差异原因
        // 可能转为预收款
        // 或者调整核销金额
    }

    /**
     * 处理负差异
     */
    private void handleNegativeDifference(Long linkId, BigDecimal differenceAmount, String differenceReason) {
        // TODO: 处理负差异（收款金额小于应收金额）
        // 记录差异原因
        // 可能需要补收款
        // 或者调整应收金额
    }

    /**
     * 撤销应收核销状态
     */
    private void reverseReceivableApplyStatus(Long receiptId, Long receivableId, BigDecimal appliedAmount) {
        // TODO: 撤销收款单和应收账款的核销状态
        // 减少收款单已核销金额
        // 减少应收账款已收款金额
        // 重新计算核销状态
    }

    /**
     * 创建应收核销记录
     */
    private Long createReceivableApplyRecord(Long receiptId, Long receivableId, BigDecimal appliedAmount, String applyReason) {
        // TODO: 创建应收核销记录到数据库
        // 返回核销记录ID
        return System.currentTimeMillis();
    }

    /**
     * 更新应收核销状态
     */
    private void updateReceivableApplyStatus(Long receiptId, Long receivableId, BigDecimal appliedAmount) {
        // TODO: 更新收款单和应收账款的核销状态
        // 更新收款单已核销金额和状态
        // 更新应收账款已收款金额和状态
    }

    /**
     * 批量核销收款单到应收账款
     *
     * @param writeoffItems 核销明细列表
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 批量核销结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchApplyToReceivables(List<WriteoffItem> writeoffItems,
                                                       Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (WriteoffItem item : writeoffItems) {
                try {
                    Boolean success = applyToReceivable(item.getReceiptOrderId(), item.getReceivableId(),
                        item.getWriteoffAmount(), operatorId, operatorName);

                    if (success) {
                        successList.add(Map.of(
                            "receiptOrderId", item.getReceiptOrderId(),
                            "receivableId", item.getReceivableId(),
                            "writeoffAmount", item.getWriteoffAmount(),
                            "status", "SUCCESS"
                        ));
                    } else {
                        failureList.add(Map.of(
                            "receiptOrderId", item.getReceiptOrderId(),
                            "receivableId", item.getReceivableId(),
                            "status", "ERROR",
                            "reason", "核销操作失败"
                        ));
                    }
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "receiptOrderId", item.getReceiptOrderId(),
                        "receivableId", item.getReceivableId(),
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", writeoffItems.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", operatorId);
            result.put("operatorName", operatorName);
            result.put("operationTime", LocalDateTime.now());

            log.info("批量收款核销完成 - 总数: {}, 成功: {}, 失败: {}, 操作人: {}",
                writeoffItems.size(), successList.size(), failureList.size(), operatorName);

            return result;
        } catch (Exception e) {
            log.error("批量收款核销失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量收款核销失败：" + e.getMessage());
        }
    }

    /**
     * 撤销收款核销
     *
     * @param writeoffId   核销记录ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否撤销成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelWriteoff(Long writeoffId, Long operatorId, String operatorName) {
        try {
            // TODO: 获取核销记录
            // FinArReceiptReceivableLink link = finArReceiptReceivableLinkService.queryById(writeoffId);

            // 校验核销记录状态
            // 删除核销记录（修复：使用deleteWithValidByIds方法）
            boolean deleteResult = finArReceiptReceivableLinkService.deleteWithValidByIds(List.of(writeoffId), false);

            if (deleteResult) {
                // TODO: 重新计算收款单和应收账款的核销状态
                log.info("撤销收款核销成功 - 核销记录ID: {}, 操作人: {}", writeoffId, operatorName);
                return true;
            } else {
                throw new ServiceException("撤销核销记录失败");
            }
        } catch (Exception e) {
            log.error("撤销收款核销失败 - 核销记录ID: {}, 错误: {}", writeoffId, e.getMessage(), e);
            throw new ServiceException("撤销收款核销失败：" + e.getMessage());
        }
    }

    /**
     * 查询收款单的核销记录
     *
     * @param receiptOrderId 收款单ID
     * @return 核销记录列表
     */
    @Override
    public List<Map<String, Object>> getWriteoffRecords(Long receiptOrderId) {
        try {
            // TODO: 查询核销记录
            List<Map<String, Object>> records = new ArrayList<>();

            // 示例数据结构
            // records = finArReceiptReceivableLinkService.getRecordsByReceiptId(receiptOrderId);

            return records;
        } catch (Exception e) {
            log.error("查询收款单核销记录失败 - 收款单ID: {}, 错误: {}", receiptOrderId, e.getMessage(), e);
            throw new ServiceException("查询核销记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取可核销的应收账款列表
     *
     * @param customerId 客户ID
     * @param amount     收款金额
     * @return 可核销的应收账款列表
     */
    @Override
    public List<FinArReceivableVo> getWriteoffableReceivables(Long customerId, BigDecimal amount) {
        try {
            // TODO: 查询可核销的应收账款
            List<FinArReceivableVo> receivables = new ArrayList<>();

            // 查询条件：
            // 客户匹配
            // 应收账款状态为待收款或部分收款
            // 未完全核销
            // 金额范围合理

            return receivables;
        } catch (Exception e) {
            log.error("获取可核销应收账款列表失败 - 客户ID: {}, 错误: {}", customerId, e.getMessage(), e);
            throw new ServiceException("获取可核销应收账款列表失败：" + e.getMessage());
        }
    }

    /**
     * 更新收款单状态
     *
     * @param receiptOrderId 收款单ID
     * @param newStatus      新状态
     * @return 是否更新成功
     */
    @Override
    public Boolean updateReceiptOrderStatus(Long receiptOrderId, String newStatus) {
        try {
            FinArReceiptOrder receiptOrder = baseMapper.selectById(receiptOrderId);
            if (receiptOrder == null) {
                throw new ServiceException("收款单不存在");
            }

            String oldStatus = receiptOrder.getReceiptStatus();
            receiptOrder.setReceiptStatus(newStatus);

            int result = baseMapper.updateById(receiptOrder);

            if (result > 0) {
                log.info("收款单状态更新成功：收款单【{}】从【{}】更新为【{}】",
                    receiptOrder.getReceiptCode(), oldStatus, newStatus);
                return true;
            } else {
                throw new ServiceException("收款单状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新收款单状态失败 - 收款单ID: {}, 错误: {}", receiptOrderId, e.getMessage(), e);
            throw new ServiceException("更新收款单状态失败：" + e.getMessage());
        }
    }

    /**
     * 判断收款单状态是否允许核销
     * 优化版本：支持更多状态和业务规则
     */
    private boolean isReceiptOrderWriteoffable(String receiptStatus) {
        if (StringUtils.isBlank(receiptStatus)) {
            return false;
        }

        // 允许核销的状态
        return "UNAPPLIED".equals(receiptStatus) ||
            "PARTIALLY_APPLIED".equals(receiptStatus) ||
            "CONFIRMED".equals(receiptStatus);
    }

    /**
     * 判断应收单状态是否允许核销
     * 优化版本：支持更多状态和业务规则
     */
    private boolean isReceivableWriteoffable(String receivableStatus) {
        if (StringUtils.isBlank(receivableStatus)) {
            return false;
        }

        // 允许核销的状态
        return "PENDING".equals(receivableStatus) ||
            "PARTIALLY_PAID".equals(receivableStatus) ||
            "CONFIRMED".equals(receivableStatus);
    }

    // ==================== 收款单状态管理优化方法 ====================

    /**
     * 确定收款单状态
     * 优化版本：基于金额比例智能判断状态
     */
    private String determineReceiptOrderStatus(BigDecimal totalAmount, BigDecimal appliedAmount) {
        if (totalAmount == null || appliedAmount == null) {
            return "UNAPPLIED";
        }

        // 精度处理
        totalAmount = totalAmount.setScale(2, RoundingMode.HALF_UP);
        appliedAmount = appliedAmount.setScale(2, RoundingMode.HALF_UP);

        if (appliedAmount.compareTo(BigDecimal.ZERO) == 0) {
            return "UNAPPLIED";
        } else if (appliedAmount.compareTo(totalAmount) >= 0) {
            return "FULLY_APPLIED";
        } else {
            return "PARTIALLY_APPLIED";
        }
    }

    /**
     * 标准化收款方式
     * 优化版本：支持更多收款方式和校验
     */
    public String standardizePaymentMethod(String paymentMethod) {
        if (StringUtils.isBlank(paymentMethod)) {
            return "BANK_TRANSFER"; // 默认银行转账
        }

        // 标准化处理
        String normalized = paymentMethod.trim().toUpperCase();

        switch (normalized) {
            case "现金":
            case "CASH":
                return "CASH";
            case "银行转账":
            case "转账":
            case "BANK_TRANSFER":
            case "TRANSFER":
                return "BANK_TRANSFER";
            case "支票":
            case "CHECK":
            case "CHEQUE":
                return "CHECK";
            case "信用卡":
            case "CREDIT_CARD":
                return "CREDIT_CARD";
            case "支付宝":
            case "ALIPAY":
                return "ALIPAY";
            case "微信":
            case "微信支付":
            case "WECHAT":
            case "WECHAT_PAY":
                return "WECHAT_PAY";
            case "承兑汇票":
            case "汇票":
            case "BILL":
            case "ACCEPTANCE_BILL":
                return "ACCEPTANCE_BILL";
            default:
                log.warn("未识别的收款方式: {}, 使用默认值: BANK_TRANSFER", paymentMethod);
                return "BANK_TRANSFER";
        }
    }

    /**
     * 校验收款方式的有效性
     */
    public boolean isValidPaymentMethod(String paymentMethod) {
        String standardized = standardizePaymentMethod(paymentMethod);
        return Arrays.asList("CASH", "BANK_TRANSFER", "CHECK", "CREDIT_CARD",
            "ALIPAY", "WECHAT_PAY", "ACCEPTANCE_BILL").contains(standardized);
    }

    /**
     * 获取收款方式的显示名称
     */
    public String getPaymentMethodDisplayName(String paymentMethod) {
        String standardized = standardizePaymentMethod(paymentMethod);

        switch (standardized) {
            case "CASH":
                return "现金";
            case "BANK_TRANSFER":
                return "银行转账";
            case "CHECK":
                return "支票";
            case "CREDIT_CARD":
                return "信用卡";
            case "ALIPAY":
                return "支付宝";
            case "WECHAT_PAY":
                return "微信支付";
            case "ACCEPTANCE_BILL":
                return "承兑汇票";
            default:
                return "其他";
        }
    }

    /**
     * 智能核销金额分配算法
     * 优化版本：支持多种分配策略
     */
    public List<WriteoffAllocation> calculateOptimalWriteoffAllocation(
        BigDecimal totalAmount, List<FinArReceivableVo> receivables) {

        List<WriteoffAllocation> allocations = new ArrayList<>();

        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0 ||
            receivables == null || receivables.isEmpty()) {
            return allocations;
        }

        try {
            // 按优先级排序应收单
            List<FinArReceivableVo> sortedReceivables = receivables.stream()
                .filter(r -> r.getAmount() != null && r.getAmount().compareTo(BigDecimal.ZERO) > 0)
                .sorted(this::compareReceivablePriority)
                .collect(Collectors.toList());

            BigDecimal remainingAmount = totalAmount;

            for (FinArReceivableVo receivable : sortedReceivables) {
                if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }

                // 计算该应收单的未收金额
                BigDecimal appliedAmount = getReceivableAppliedAmount(receivable.getReceivableId());
                BigDecimal unpaidAmount = receivable.getAmount().subtract(appliedAmount);

                if (unpaidAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue; // 已全额收款，跳过
                }

                // 计算分配金额
                BigDecimal allocationAmount = remainingAmount.min(unpaidAmount);

                WriteoffAllocation allocation = new WriteoffAllocation();
                allocation.setReceivableId(receivable.getReceivableId());
                allocation.setReceivableCode(receivable.getReceivableCode());
                allocation.setReceivableAmount(receivable.getAmount());
                allocation.setUnpaidAmount(unpaidAmount);
                allocation.setAllocationAmount(allocationAmount);
                allocation.setPriority(calculateReceivablePriority(receivable));

                allocations.add(allocation);
                remainingAmount = remainingAmount.subtract(allocationAmount);
            }

            log.debug("智能核销分配完成 - 总金额: {}, 分配数: {}, 剩余金额: {}",
                totalAmount, allocations.size(), remainingAmount);

            return allocations;

        } catch (Exception e) {
            log.error("计算核销分配失败 - 总金额: {}, 错误: {}", totalAmount, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 比较应收单优先级
     */
    private int compareReceivablePriority(FinArReceivableVo r1, FinArReceivableVo r2) {
        // 按到期日期排序（越早到期优先级越高）
        // TODO: 在FinArReceivableVo中添加getDueDate()方法
        // if (r1.getDueDate() != null && r2.getDueDate() != null) {
        //     int dateCompare = r1.getDueDate().compareTo(r2.getDueDate());
        //     if (dateCompare != 0) {
        //         return dateCompare;
        //     }
        // }

        // 按金额排序（金额越小优先级越高，便于快速清零）
        if (r1.getAmount() != null && r2.getAmount() != null) {
            return r1.getAmount().compareTo(r2.getAmount());
        }

        // 按创建时间排序（越早创建优先级越高）
        // TODO: 在FinArReceivableVo中添加getCreateTime()方法
        // if (r1.getCreateTime() != null && r2.getCreateTime() != null) {
        //     return r1.getCreateTime().compareTo(r2.getCreateTime());
        // }

        return 0;
    }

    /**
     * 计算应收单优先级分数
     */
    private double calculateReceivablePriority(FinArReceivableVo receivable) {
        double priority = 0.0;

        // 基于到期日的优先级
        // TODO: 在FinArReceivableVo中添加getDueDate()方法
        // if (receivable.getDueDate() != null) {
        //     long daysTodue = ChronoUnit.DAYS.between(LocalDate.now(), receivable.getDueDate());
        //     if (daysTodue < 0) {
        //         priority += 100; // 已逾期，最高优先级
        //     } else if (daysTodue <= 7) {
        //         priority += 50; // 一周内到期，高优先级
        //     } else if (daysTodue <= 30) {
        //         priority += 20; // 一月内到期，中等优先级
        //     }
        // }

        // 基于金额的优先级（小额优先）
        if (receivable.getAmount() != null) {
            if (receivable.getAmount().compareTo(new BigDecimal("1000")) <= 0) {
                priority += 10; // 小额应收，便于快速清零
            }
        }

        return priority;
    }

    // 内部类定义
    public static class ReceivableApplyRecommendation {
        private Long receivableId;
        private String receivableCode;
        private String receivableName;
        private BigDecimal receivableAmount;
        private BigDecimal recommendedAmount;
        private double matchScore;
        private String matchReason;

        // getters and setters
        public Long getReceivableId() {
            return receivableId;
        }

        public void setReceivableId(Long receivableId) {
            this.receivableId = receivableId;
        }

        public String getReceivableCode() {
            return receivableCode;
        }

        public void setReceivableCode(String receivableCode) {
            this.receivableCode = receivableCode;
        }

        public String getReceivableName() {
            return receivableName;
        }

        public void setReceivableName(String receivableName) {
            this.receivableName = receivableName;
        }

        public BigDecimal getReceivableAmount() {
            return receivableAmount;
        }

        public void setReceivableAmount(BigDecimal receivableAmount) {
            this.receivableAmount = receivableAmount;
        }

        public BigDecimal getRecommendedAmount() {
            return recommendedAmount;
        }

        public void setRecommendedAmount(BigDecimal recommendedAmount) {
            this.recommendedAmount = recommendedAmount;
        }

        public double getMatchScore() {
            return matchScore;
        }

        public void setMatchScore(double matchScore) {
            this.matchScore = matchScore;
        }

        public String getMatchReason() {
            return matchReason;
        }

        public void setMatchReason(String matchReason) {
            this.matchReason = matchReason;
        }
    }

    public static class ReceivableApplyRequest {
        private Long receiptId;
        private Long receivableId;
        private BigDecimal appliedAmount;
        private String applyReason;

        // getters and setters
        public Long getReceiptId() {
            return receiptId;
        }

        public void setReceiptId(Long receiptId) {
            this.receiptId = receiptId;
        }

        public Long getReceivableId() {
            return receivableId;
        }

        public void setReceivableId(Long receivableId) {
            this.receivableId = receivableId;
        }

        public BigDecimal getAppliedAmount() {
            return appliedAmount;
        }

        public void setAppliedAmount(BigDecimal appliedAmount) {
            this.appliedAmount = appliedAmount;
        }

        public String getApplyReason() {
            return applyReason;
        }

        public void setApplyReason(String applyReason) {
            this.applyReason = applyReason;
        }
    }

    /**
     * 核销分配结果数据结构
     */
    public static class WriteoffAllocation {
        private Long receivableId;
        private String receivableCode;
        private BigDecimal receivableAmount;
        private BigDecimal unpaidAmount;
        private BigDecimal allocationAmount;
        private double priority;

        // Getters and Setters
        public Long getReceivableId() {
            return receivableId;
        }

        public void setReceivableId(Long receivableId) {
            this.receivableId = receivableId;
        }

        public String getReceivableCode() {
            return receivableCode;
        }

        public void setReceivableCode(String receivableCode) {
            this.receivableCode = receivableCode;
        }

        public BigDecimal getReceivableAmount() {
            return receivableAmount;
        }

        public void setReceivableAmount(BigDecimal receivableAmount) {
            this.receivableAmount = receivableAmount;
        }

        public BigDecimal getUnpaidAmount() {
            return unpaidAmount;
        }

        public void setUnpaidAmount(BigDecimal unpaidAmount) {
            this.unpaidAmount = unpaidAmount;
        }

        public BigDecimal getAllocationAmount() {
            return allocationAmount;
        }

        public void setAllocationAmount(BigDecimal allocationAmount) {
            this.allocationAmount = allocationAmount;
        }

        public double getPriority() {
            return priority;
        }

        public void setPriority(double priority) {
            this.priority = priority;
        }
    }
}
