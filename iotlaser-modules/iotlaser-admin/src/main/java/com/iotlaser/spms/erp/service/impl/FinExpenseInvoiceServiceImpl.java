package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinExpenseInvoice;
import com.iotlaser.spms.erp.domain.bo.FinExpenseInvoiceBo;
import com.iotlaser.spms.erp.domain.vo.FinExpenseInvoiceVo;
import com.iotlaser.spms.erp.mapper.FinExpenseInvoiceMapper;
import com.iotlaser.spms.erp.service.IFinExpenseInvoiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 管理费用Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-20
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinExpenseInvoiceServiceImpl implements IFinExpenseInvoiceService {

    private final FinExpenseInvoiceMapper baseMapper;

    /**
     * 查询管理费用
     *
     * @param invoiceId 主键
     * @return 管理费用
     */
    @Override
    public FinExpenseInvoiceVo queryById(Long invoiceId) {
        return baseMapper.selectVoById(invoiceId);
    }

    /**
     * 分页查询管理费用列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 管理费用分页列表
     */
    @Override
    public TableDataInfo<FinExpenseInvoiceVo> queryPageList(FinExpenseInvoiceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinExpenseInvoice> lqw = buildQueryWrapper(bo);
        Page<FinExpenseInvoiceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的管理费用列表
     *
     * @param bo 查询条件
     * @return 管理费用列表
     */
    @Override
    public List<FinExpenseInvoiceVo> queryList(FinExpenseInvoiceBo bo) {
        LambdaQueryWrapper<FinExpenseInvoice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinExpenseInvoice> buildQueryWrapper(FinExpenseInvoiceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinExpenseInvoice> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinExpenseInvoice::getInvoiceId);
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceCode()), FinExpenseInvoice::getInvoiceCode, bo.getInvoiceCode());
        lqw.like(StringUtils.isNotBlank(bo.getInvoiceName()), FinExpenseInvoice::getInvoiceName, bo.getInvoiceName());
        lqw.eq(StringUtils.isNotBlank(bo.getPayeeType()), FinExpenseInvoice::getPayeeType, bo.getPayeeType());
        lqw.eq(bo.getPayeeId() != null, FinExpenseInvoice::getPayeeId, bo.getPayeeId());
        lqw.eq(StringUtils.isNotBlank(bo.getPayeeCode()), FinExpenseInvoice::getPayeeCode, bo.getPayeeCode());
        lqw.like(StringUtils.isNotBlank(bo.getPayeeName()), FinExpenseInvoice::getPayeeName, bo.getPayeeName());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceNumber()), FinExpenseInvoice::getInvoiceNumber, bo.getInvoiceNumber());
        lqw.eq(bo.getInvoiceDate() != null, FinExpenseInvoice::getInvoiceDate, bo.getInvoiceDate());
        lqw.eq(bo.getAmountExclusiveTax() != null, FinExpenseInvoice::getAmountExclusiveTax, bo.getAmountExclusiveTax());
        lqw.eq(bo.getAmount() != null, FinExpenseInvoice::getAmount, bo.getAmount());
        lqw.eq(bo.getTaxAmount() != null, FinExpenseInvoice::getTaxAmount, bo.getTaxAmount());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceStatus()), FinExpenseInvoice::getInvoiceStatus, bo.getInvoiceStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinExpenseInvoice::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增管理费用
     *
     * @param bo 管理费用
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinExpenseInvoiceBo bo) {
        FinExpenseInvoice add = MapstructUtils.convert(bo, FinExpenseInvoice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setInvoiceId(add.getInvoiceId());
        }
        return flag;
    }

    /**
     * 修改管理费用
     *
     * @param bo 管理费用
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinExpenseInvoiceBo bo) {
        FinExpenseInvoice update = MapstructUtils.convert(bo, FinExpenseInvoice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinExpenseInvoice entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除管理费用信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验管理费用是否可以删除
            List<FinExpenseInvoice> expenses = baseMapper.selectByIds(ids);
            for (FinExpenseInvoice expense : expenses) {
                // 检查费用状态，只有草稿状态的费用才能删除
                if (!"DRAFT".equals(expense.getInvoiceStatus()) && !"PENDING".equals(expense.getInvoiceStatus())) {
                    throw new ServiceException("管理费用【" + expense.getInvoiceName() + "】状态为【" +
                        expense.getInvoiceStatus() + "】，不允许删除");
                }

                // 检查是否有关联的付款核销记录
                // TODO: 添加finExpensePaymentLinkService依赖注入
                // if (finExpensePaymentLinkService.existsByInvoiceId(expense.getInvoiceId())) {
                //     throw new ServiceException("管理费用【" + expense.getInvoiceName() + "】存在付款核销记录，不允许删除");
                // }

                // 级联删除费用明细
                // TODO: 添加finExpenseInvoiceItemService依赖注入
                // if (finExpenseInvoiceItemService.existsByInvoiceId(expense.getInvoiceId())) {
                //     List<Long> itemIds = finExpenseInvoiceItemService.getItemIdsByInvoiceId(expense.getInvoiceId());
                //     if (!itemIds.isEmpty()) {
                //         finExpenseInvoiceItemService.deleteWithValidByIds(itemIds, false);
                //         log.info("级联删除管理费用明细，费用：{}，明细数量：{}", expense.getInvoiceName(), itemIds.size());
                //     }
                // }

                log.info("删除管理费用校验通过：{}", expense.getInvoiceName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除管理费用成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除管理费用失败：{}", e.getMessage(), e);
            throw new ServiceException("删除管理费用失败：" + e.getMessage());
        }
    }
}
