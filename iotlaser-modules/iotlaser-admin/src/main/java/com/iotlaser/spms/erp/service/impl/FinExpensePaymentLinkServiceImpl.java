package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinExpensePaymentLink;
import com.iotlaser.spms.erp.domain.bo.FinExpensePaymentLinkBo;
import com.iotlaser.spms.erp.domain.vo.FinExpensePaymentLinkVo;
import com.iotlaser.spms.erp.mapper.FinExpensePaymentLinkMapper;
import com.iotlaser.spms.erp.service.IFinExpensePaymentLinkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 管理费用与付款单核销关系Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-20
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinExpensePaymentLinkServiceImpl implements IFinExpensePaymentLinkService {

    private final FinExpensePaymentLinkMapper baseMapper;

    /**
     * 查询管理费用与付款单核销关系
     *
     * @param linkId 主键
     * @return 管理费用与付款单核销关系
     */
    @Override
    public FinExpensePaymentLinkVo queryById(Long linkId) {
        return baseMapper.selectVoById(linkId);
    }

    /**
     * 分页查询管理费用与付款单核销关系列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 管理费用与付款单核销关系分页列表
     */
    @Override
    public TableDataInfo<FinExpensePaymentLinkVo> queryPageList(FinExpensePaymentLinkBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinExpensePaymentLink> lqw = buildQueryWrapper(bo);
        Page<FinExpensePaymentLinkVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的管理费用与付款单核销关系列表
     *
     * @param bo 查询条件
     * @return 管理费用与付款单核销关系列表
     */
    @Override
    public List<FinExpensePaymentLinkVo> queryList(FinExpensePaymentLinkBo bo) {
        LambdaQueryWrapper<FinExpensePaymentLink> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinExpensePaymentLink> buildQueryWrapper(FinExpensePaymentLinkBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinExpensePaymentLink> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinExpensePaymentLink::getLinkId);
        lqw.eq(bo.getPaymentId() != null, FinExpensePaymentLink::getPaymentId, bo.getPaymentId());
        lqw.eq(bo.getInvoiceId() != null, FinExpensePaymentLink::getInvoiceId, bo.getInvoiceId());
        lqw.eq(bo.getAppliedAmount() != null, FinExpensePaymentLink::getAppliedAmount, bo.getAppliedAmount());
        lqw.eq(bo.getCancellationDate() != null, FinExpensePaymentLink::getCancellationDate, bo.getCancellationDate());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinExpensePaymentLink::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增管理费用与付款单核销关系
     *
     * @param bo 管理费用与付款单核销关系
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinExpensePaymentLinkBo bo) {
        FinExpensePaymentLink add = MapstructUtils.convert(bo, FinExpensePaymentLink.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setLinkId(add.getLinkId());
        }
        return flag;
    }

    /**
     * 修改管理费用与付款单核销关系
     *
     * @param bo 管理费用与付款单核销关系
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinExpensePaymentLinkBo bo) {
        FinExpensePaymentLink update = MapstructUtils.convert(bo, FinExpensePaymentLink.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinExpensePaymentLink entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除管理费用与付款单核销关系信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验核销关系是否可以删除
            List<FinExpensePaymentLink> links = baseMapper.selectByIds(ids);
            for (FinExpensePaymentLink link : links) {
                // 检查核销状态，已确认的核销关系不能删除
                if ("CONFIRMED".equals(link.getStatus())) {
                    throw new ServiceException("核销关系【" + link.getLinkId() + "】已确认，不允许删除");
                }

                // 检查关联的付款单和发票状态
                // TODO: 添加对付款单和发票状态的检查
                // 如果付款单或发票已审批，则不允许删除核销关系

                log.info("删除管理费用核销关系校验通过：{}", link.getLinkId());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除管理费用核销关系成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除管理费用核销关系失败：{}", e.getMessage(), e);
            throw new ServiceException("删除管理费用核销关系失败：" + e.getMessage());
        }
    }
}
