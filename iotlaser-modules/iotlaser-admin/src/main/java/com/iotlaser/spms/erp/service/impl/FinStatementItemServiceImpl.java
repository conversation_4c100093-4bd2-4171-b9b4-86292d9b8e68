package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.erp.domain.FinStatementItem;
import com.iotlaser.spms.erp.domain.bo.FinStatementItemBo;
import com.iotlaser.spms.erp.domain.vo.FinStatementItemVo;
import com.iotlaser.spms.erp.mapper.FinStatementItemMapper;
import com.iotlaser.spms.erp.service.IFinStatementItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 对账单明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-06-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class FinStatementItemServiceImpl implements IFinStatementItemService {

    private final FinStatementItemMapper baseMapper;

    /**
     * 查询对账单明细
     *
     * @param itemId 主键
     * @return 对账单明细
     */
    @Override
    public FinStatementItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询对账单明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 对账单明细分页列表
     */
    @Override
    public TableDataInfo<FinStatementItemVo> queryPageList(FinStatementItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<FinStatementItem> lqw = buildQueryWrapper(bo);
        Page<FinStatementItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的对账单明细列表
     *
     * @param bo 查询条件
     * @return 对账单明细列表
     */
    @Override
    public List<FinStatementItemVo> queryList(FinStatementItemBo bo) {
        LambdaQueryWrapper<FinStatementItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<FinStatementItem> buildQueryWrapper(FinStatementItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<FinStatementItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(FinStatementItem::getItemId);
        lqw.eq(bo.getStatementId() != null, FinStatementItem::getStatementId, bo.getStatementId());
        lqw.eq(StringUtils.isNotBlank(bo.getGroupId()), FinStatementItem::getGroupId, bo.getGroupId());
        lqw.eq(bo.getGroupBalance() != null, FinStatementItem::getGroupBalance, bo.getGroupBalance());
        lqw.eq(bo.getSourceId() != null, FinStatementItem::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), FinStatementItem::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(FinStatementItem::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, FinStatementItem::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), FinStatementItem::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(FinStatementItem::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getDirectSourceItemId() != null, FinStatementItem::getDirectSourceItemId, bo.getDirectSourceItemId());
        lqw.eq(bo.getAmountDebit() != null, FinStatementItem::getAmountDebit, bo.getAmountDebit());
        lqw.eq(bo.getAmountCredit() != null, FinStatementItem::getAmountCredit, bo.getAmountCredit());
        lqw.eq(StringUtils.isNotBlank(bo.getMarkFlag()), FinStatementItem::getMarkFlag, bo.getMarkFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), FinStatementItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增对账单明细
     *
     * @param bo 对账单明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FinStatementItemBo bo) {
        FinStatementItem add = MapstructUtils.convert(bo, FinStatementItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改对账单明细
     *
     * @param bo 对账单明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(FinStatementItemBo bo) {
        FinStatementItem update = MapstructUtils.convert(bo, FinStatementItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(FinStatementItem entity) {
        // 数据校验：检查唯一约束和必填字段
        validateUniqueConstraint(entity);
        validateRequiredFields(entity);
    }

    /**
     * 校验并批量删除对账单明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 业务校验：检查是否可以删除
            validateBeforeDelete(ids);
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据对账单ID查询明细
     *
     * @param statementId 对账单ID
     * @return 对账明细列表
     */
    public List<FinStatementItemVo> selectByStatementId(Long statementId) {
        LambdaQueryWrapper<FinStatementItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinStatementItem::getStatementId, statementId);
        // TODO: FinStatementItem实体中没有itemDate字段，暂时使用itemId排序
        // 待实体完善后改为按日期排序
        wrapper.orderByAsc(FinStatementItem::getItemId);

        List<FinStatementItem> items = baseMapper.selectList(wrapper);
        return MapstructUtils.convert(items, FinStatementItemVo.class);
    }

    /**
     * 批量创建对账明细
     *
     * @param statementId 对账单ID
     * @param items       明细列表
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchCreateItems(Long statementId, List<FinStatementItemBo> items) {
        try {
            List<FinStatementItem> entities = new ArrayList<>();

            for (FinStatementItemBo itemBo : items) {
                FinStatementItem item = MapstructUtils.convert(itemBo, FinStatementItem.class);
                item.setStatementId(statementId);
                entities.add(item);
            }

            // TODO: saveBatch方法不存在，需要使用正确的批量插入方法
            // 暂时使用逐个插入，待框架方法确认后优化
            boolean result = true;
            for (FinStatementItem entity : entities) {
                int insertResult = baseMapper.insert(entity);
                if (insertResult <= 0) {
                    result = false;
                    break;
                }
            }
            // 原逻辑（待方法确认后启用）：
            // boolean result = saveBatch(entities);

            if (result) {
                log.info("对账明细批量创建成功 - 对账单ID: {}, 明细数量: {}", statementId, items.size());
            }

            return result;
        } catch (Exception e) {
            log.error("对账明细批量创建失败 - 对账单ID: {}, 错误: {}", statementId, e.getMessage(), e);
            throw new ServiceException("对账明细批量创建失败：" + e.getMessage());
        }
    }

    /**
     * 计算对账单余额
     *
     * @param statementId    对账单ID
     * @param openingBalance 期初余额
     * @return 期末余额
     */
    public BigDecimal calculateStatementBalance(Long statementId, BigDecimal openingBalance) {
        LambdaQueryWrapper<FinStatementItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(FinStatementItem::getStatementId, statementId);

        List<FinStatementItem> items = baseMapper.selectList(wrapper);

        BigDecimal totalDebit = items.stream()
            .map(item -> item.getAmountDebit() != null ? item.getAmountDebit() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal totalCredit = items.stream()
            .map(item -> item.getAmountCredit() != null ? item.getAmountCredit() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        return openingBalance.add(totalDebit).subtract(totalCredit);
    }

    /**
     * 校验唯一约束
     *
     * @param entity 实体对象
     */
    private void validateUniqueConstraint(FinStatementItem entity) {
        // 检查同一对账单下的业务单据是否重复
        // TODO 需完善: FinStatementItem实体中没有businessId和businessType字段，使用sourceId和sourceType替代
        /*if (entity.getSourceId() != null && entity.getSourceType() != null) {
            LambdaQueryWrapper<FinStatementItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(FinStatementItem::getStatementId, entity.getStatementId());
            wrapper.eq(FinStatementItem::getSourceId, entity.getSourceId());        // 原: getBusinessId()
            wrapper.eq(FinStatementItem::getSourceType, entity.getSourceType());    // 原: getBusinessType()

            // 如果是更新操作，排除当前记录
            if (entity.getItemId() != null) {
                wrapper.ne(FinStatementItem::getItemId, entity.getItemId());
            }

            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("该对账单中已存在相同业务单据的明细记录");
            }
        }*/
    }

    /**
     * 校验必填字段
     *
     * @param entity 实体对象
     */
    private void validateRequiredFields(FinStatementItem entity) {
        if (entity.getStatementId() == null) {
            throw new ServiceException("对账单ID不能为空");
        }
        // TODO: FinStatementItem实体中没有itemDate字段，需要重新设计日期校验逻辑
        // 暂时注释掉日期校验，待实体完善后启用
        // if (entity.getItemDate() == null) {
        //     throw new ServiceException("明细日期不能为空");
        // }
        log.warn("明细日期校验需要重新设计 - 明细ID: {}", entity.getItemId());

        // 借贷金额至少有一个不为空且大于0
        BigDecimal debit = entity.getAmountDebit() != null ? entity.getAmountDebit() : BigDecimal.ZERO;
        BigDecimal credit = entity.getAmountCredit() != null ? entity.getAmountCredit() : BigDecimal.ZERO;

        if (debit.compareTo(BigDecimal.ZERO) == 0 && credit.compareTo(BigDecimal.ZERO) == 0) {
            throw new ServiceException("借方金额和贷方金额不能同时为0");
        }

        if (debit.compareTo(BigDecimal.ZERO) < 0 || credit.compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("借方金额和贷方金额不能为负数");
        }
    }

    /**
     * 删除前校验
     *
     * @param ids 待删除的ID集合
     */
    private void validateBeforeDelete(Collection<Long> ids) {
        // 检查对账明细是否已确认，如果已确认则不能删除
        for (Long id : ids) {
            FinStatementItem item = baseMapper.selectById(id);
            if (item != null) {
                // TODO: 检查对账单状态，如果已确认则不能删除明细
                // 暂时允许删除，后续根据业务需求完善
            }
        }
    }
}
