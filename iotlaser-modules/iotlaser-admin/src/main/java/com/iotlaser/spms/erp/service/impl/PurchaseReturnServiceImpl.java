package com.iotlaser.spms.erp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.PurchaseReturn;
import com.iotlaser.spms.erp.domain.PurchaseReturnItem;
import com.iotlaser.spms.erp.domain.bo.PurchaseReturnBo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundItemVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseReturnVo;
import com.iotlaser.spms.erp.enums.PurchaseReturnStatus;
import com.iotlaser.spms.erp.mapper.PurchaseReturnItemMapper;
import com.iotlaser.spms.erp.mapper.PurchaseReturnMapper;
import com.iotlaser.spms.erp.service.IPurchaseReturnService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.ERP_PURCHASE_RETURN_CODE;

/**
 * 采购退货Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseReturnServiceImpl implements IPurchaseReturnService {

    private final PurchaseReturnMapper baseMapper;
    private final PurchaseReturnItemMapper itemMapper;

    private final Gen gen;

    /**
     * 查询采购退货
     *
     * @param returnId 主键
     * @return 采购退货
     */
    @Override
    public PurchaseReturnVo queryById(Long returnId) {
        return baseMapper.selectVoById(returnId);
    }

    /**
     * 分页查询采购退货列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 采购退货分页列表
     */
    @Override
    public TableDataInfo<PurchaseReturnVo> queryPageList(PurchaseReturnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PurchaseReturn> lqw = buildQueryWrapper(bo);
        Page<PurchaseReturnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的采购退货列表
     *
     * @param bo 查询条件
     * @return 采购退货列表
     */
    @Override
    public List<PurchaseReturnVo> queryList(PurchaseReturnBo bo) {
        LambdaQueryWrapper<PurchaseReturn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseReturn> buildQueryWrapper(PurchaseReturnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseReturn> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PurchaseReturn::getReturnId);
        lqw.eq(StringUtils.isNotBlank(bo.getReturnCode()), PurchaseReturn::getReturnCode, bo.getReturnCode());
        lqw.eq(bo.getSourceId() != null, PurchaseReturn::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), PurchaseReturn::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(PurchaseReturn::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, PurchaseReturn::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), PurchaseReturn::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(PurchaseReturn::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getSupplierId() != null, PurchaseReturn::getSupplierId, bo.getSupplierId());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), PurchaseReturn::getSupplierName, bo.getSupplierName());
        lqw.eq(bo.getReturnDate() != null, PurchaseReturn::getReturnDate, bo.getReturnDate());
        if (bo.getReturnStatus() != null) {
            lqw.eq(PurchaseReturn::getReturnStatus, bo.getReturnStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), PurchaseReturn::getStatus, bo.getStatus());
        lqw.between(params.get("beginReturnDate") != null && params.get("endReturnDate") != null,
            PurchaseReturn::getReturnDate, params.get("beginReturnDate"), params.get("endReturnDate"));
        return lqw;
    }

    /**
     * 新增采购退货
     *
     * @param bo 采购退货
     * @return 创建的采购退货
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseReturnVo insertByBo(PurchaseReturnBo bo) {
        try {
            // 生成退货单号
            if (StringUtils.isEmpty(bo.getReturnCode())) {
                bo.setReturnCode(gen.code(ERP_PURCHASE_RETURN_CODE));
            }
            // 设置初始状态
            if (bo.getReturnStatus() == null) {
                bo.setReturnStatus(PurchaseReturnStatus.DRAFT);
            }
            // 设置退货日期
            if (bo.getReturnDate() == null) {
                bo.setReturnDate(LocalDate.now());
            }
            // 填充冗余字段
            //fillRedundantFields(bo);
            // 填充退货人信息
            //fillResponsiblePersonInfo(bo);
            // 转换为实体并校验
            PurchaseReturn add = MapstructUtils.convert(bo, PurchaseReturn.class);
            validEntityBeforeSave(add);
            // 插入数据库
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增采购退货失败");
            }
            log.info("新增采购退货成功：{}", add.getReturnCode());
            return MapstructUtils.convert(add, PurchaseReturnVo.class);
        } catch (Exception e) {
            log.error("新增采购退货失败：{}", e.getMessage(), e);
            throw new ServiceException("新增采购退货失败：" + e.getMessage());
        }
    }

    /**
     * 修改采购退货
     *
     * @param bo 采购退货
     * @return 修改后的采购退货
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public PurchaseReturnVo updateByBo(PurchaseReturnBo bo) {
        try {
            // 填充冗余字段
            //fillRedundantFields(bo);
            // 填充申请人信息
            //fillResponsiblePersonInfo(bo);
            // 转换为实体并校验
            PurchaseReturn update = MapstructUtils.convert(bo, PurchaseReturn.class);
            validEntityBeforeSave(update);
            // 更新主表
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改采购退货失败");
            }
            log.info("修改采购退货成功：{}", update.getReturnCode());
            return MapstructUtils.convert(update, PurchaseReturnVo.class);

        } catch (Exception e) {
            log.error("修改采购退货失败：{}", e.getMessage(), e);
            throw new ServiceException("修改采购退货失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseReturn entity) {
        // 校验退货单编号唯一性
        if (StringUtils.isNotBlank(entity.getReturnCode())) {
            LambdaQueryWrapper<PurchaseReturn> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(PurchaseReturn::getReturnCode, entity.getReturnCode());
            if (entity.getReturnId() != null) {
                wrapper.ne(PurchaseReturn::getReturnId, entity.getReturnId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("退货单编号已存在：" + entity.getReturnCode());
            }
        }
    }

    /**
     * 校验并批量删除采购退货信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验退货单状态，只有草稿状态的退货单才能删除
            List<PurchaseReturn> returns = baseMapper.selectByIds(ids);
            for (PurchaseReturn purchaseReturn : returns) {
                if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.DRAFT) {
                    throw new ServiceException("退货单【" + purchaseReturn.getReturnCode() + "】状态为【" + purchaseReturn.getReturnStatus() + "】，不允许删除");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


    /**
     * 检查是否存在指定订单ID的退货单
     *
     * @param orderId 订单ID
     * @return 是否存在
     */
    @Override
    public Boolean existsByDirectSourceId(Long orderId) {
        return baseMapper.existsByDirectSourceId(orderId);
    }

    /**
     * 确认采购退货单
     *
     * @param returnId 退货单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmReturn(Long returnId) {
        PurchaseReturn purchaseReturn = baseMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new ServiceException("退货单不存在");
        }
        // 校验状态
        if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.DRAFT) {
            throw new ServiceException("退货单【" + purchaseReturn.getReturnCode() + "】状态为【" + purchaseReturn.getReturnStatus() + "】，不允许确认");
        }
        // 更新状态为待出库
        purchaseReturn.setReturnStatus(PurchaseReturnStatus.PENDING_WAREHOUSE);
        boolean result = baseMapper.updateById(purchaseReturn) > 0;
        if (result) {
            log.info("采购退货单【{}】确认成功", purchaseReturn.getReturnCode());
        }

        return result;
    }

    /**
     * 批量确认采购退货单
     *
     * @param returnIds 退货单ID集合
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmReturns(Collection<Long> returnIds) {
        for (Long returnId : returnIds) {
            confirmReturn(returnId);
        }
        return true;
    }

    /**
     * 完成采购退货出库
     *
     * @param returnId 退货单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeReturn(Long returnId) {
        PurchaseReturn purchaseReturn = baseMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new ServiceException("退货单不存在");
        }
        // 校验状态
        if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("退货单【" + purchaseReturn.getReturnCode() + "】状态为【" + purchaseReturn.getReturnStatus() + "】，不允许完成出库");
        }
        // 更新状态为已出库
        purchaseReturn.setReturnStatus(PurchaseReturnStatus.COMPLETED);
        boolean result = baseMapper.updateById(purchaseReturn) > 0;
        if (result) {
            log.info("采购退货单【{}】出库完成", purchaseReturn.getReturnCode());
            // 处理库存减少逻辑
            processInventoryDecrease(purchaseReturn);
        }
        return result;
    }

    /**
     * 取消采购退货单
     *
     * @param returnId 退货单ID
     * @param reason   取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelReturn(Long returnId, String reason) {
        PurchaseReturn purchaseReturn = baseMapper.selectById(returnId);
        if (purchaseReturn == null) {
            throw new ServiceException("退货单不存在");
        }
        // 校验状态，只有草稿和待出库状态可以取消
        if (purchaseReturn.getReturnStatus() != PurchaseReturnStatus.DRAFT &&
            purchaseReturn.getReturnStatus() != PurchaseReturnStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("退货单【" + purchaseReturn.getReturnCode() + "】状态为【" + purchaseReturn.getReturnStatus() + "】，不允许取消");
        }
        // 更新状态为草稿，并记录取消原因
        purchaseReturn.setReturnStatus(PurchaseReturnStatus.DRAFT);
        if (StringUtils.isNotBlank(reason)) {
            purchaseReturn.setRemark(StringUtils.isBlank(purchaseReturn.getRemark()) ? "取消原因：" + reason : purchaseReturn.getRemark() + "；取消原因：" + reason);
        }
        boolean result = baseMapper.updateById(purchaseReturn) > 0;
        if (result) {
            log.info("采购退货单【{}】取消成功，原因：{}", purchaseReturn.getReturnCode(), reason);
        }
        return result;
    }

    /**
     * 根据采购入库单创建退货单
     *
     * @param inboundVo 采购入库单
     * @return 创建的退货单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromPurchaseInbound(PurchaseInboundVo inboundVo) {
        // 创建退货单
        PurchaseReturn add = new PurchaseReturn();
        add.setReturnCode(gen.code(ERP_PURCHASE_RETURN_CODE));

        add.setSourceId(inboundVo.getSourceId());
        add.setSourceCode(inboundVo.getInboundCode());
        add.setSourceType(inboundVo.getSourceType());

        add.setDirectSourceId(inboundVo.getInboundId());
        add.setDirectSourceCode(inboundVo.getInboundCode());
        add.setDirectSourceType(DirectSourceType.PURCHASE_INBOUND);

        add.setSupplierId(inboundVo.getSupplierId());
        add.setSupplierName(inboundVo.getSupplierName());
        add.setReturnDate(LocalDate.now());
        add.setReturnStatus(PurchaseReturnStatus.DRAFT);
        add.setSummary("基于采购入库单【" + inboundVo.getInboundCode() + "】创建");
        // 插入退货单
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("创建采购退货单失败");
        }
        // 创建退货明细
        if (inboundVo.getItems() != null && !inboundVo.getItems().isEmpty()) {
            List<PurchaseReturnItem> returnItems = new ArrayList<>();
            for (PurchaseInboundItemVo itemVo : inboundVo.getItems()) {
                PurchaseReturnItem returnItem = new PurchaseReturnItem();
                returnItem.setReturnId(add.getReturnId());
                returnItem.setInventoryId(itemVo.getInventoryId());
                returnItem.setProductId(itemVo.getProductId());
                returnItem.setProductCode(itemVo.getProductCode());
                returnItem.setProductName(itemVo.getProductName());
                returnItem.setUnitId(itemVo.getUnitId());
                returnItem.setUnitCode(itemVo.getUnitCode());
                returnItem.setUnitName(itemVo.getUnitName());
                returnItem.setQuantity(itemVo.getQuantity());
                returnItem.setPrice(itemVo.getPrice());
                returnItem.setLocationId(itemVo.getLocationId());
                returnItem.setLocationCode(itemVo.getLocationCode());
                returnItem.setLocationName(itemVo.getLocationName());
                returnItem.setQuantity(itemVo.getQuantity());
                returnItem.setPrice(itemVo.getPrice());
                returnItem.setPriceExclusiveTax(itemVo.getPriceExclusiveTax());
                returnItem.setAmount(itemVo.getAmount());
                returnItem.setAmountExclusiveTax(itemVo.getAmountExclusiveTax());
                returnItem.setTaxRate(itemVo.getTaxRate());
                returnItem.setTaxAmount(itemVo.getTaxAmount());
                returnItem.setRemark("基于采购入库单【" + inboundVo.getInboundCode() + "】创建");
                returnItems.add(returnItem);
            }
            boolean insertItem = itemMapper.insertBatch(returnItems);
            if (!insertItem) {
                throw new ServiceException("基于采购入库单创建采购退货单失败");
            }
        }
        log.info("基于采购入库单【{}】创建采购退货单【{}】成功", inboundVo.getInboundCode(), add.getReturnCode());
        return true;
    }

    /**
     * 处理库存减少逻辑
     *
     * @param purchaseReturn 采购退货单
     */
    private void processInventoryDecrease(PurchaseReturn purchaseReturn) {
        try {
            // 获取退货明细
            // 注意：这里需要根据实际的退货明细Service进行调用
            // List<PurchaseReturnItem> items = itemService.getByReturnId(purchaseReturn.getReturnId());

            // 由于当前可能没有退货明细Service，这里记录日志
            log.info("采购退货库存减少：退货单【{}】", purchaseReturn.getReturnCode());

            // TODO: 实际项目中需要：
            // 获取采购退货明细
            // 遍历明细，调用库存服务减少库存
            // 记录库存变动日志
            //
            // for (PurchaseReturnItem item : items) {
            //     inventoryService.decreaseInventory(item.getProductId(), item.getLocationId(), item.getQuantity());
            //     log.info("采购退货库存减少：产品【{}】数量【{}】", item.getProductName(), item.getQuantity());
            // }

            log.info("采购退货单【{}】库存减少处理完成", purchaseReturn.getReturnCode());
        } catch (Exception e) {
            log.error("采购退货单【{}】库存减少失败：{}", purchaseReturn.getReturnCode(), e.getMessage(), e);
            throw new ServiceException("库存减少失败：" + e.getMessage());
        }
    }
}
