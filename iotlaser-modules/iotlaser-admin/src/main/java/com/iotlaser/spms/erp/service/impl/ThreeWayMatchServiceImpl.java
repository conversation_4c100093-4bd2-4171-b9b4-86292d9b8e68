package com.iotlaser.spms.erp.service.impl;

import com.iotlaser.spms.erp.domain.bo.PurchaseInboundBo;
import com.iotlaser.spms.erp.domain.bo.PurchaseOrderBo;
import com.iotlaser.spms.erp.domain.vo.FinApInvoiceVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import com.iotlaser.spms.erp.enums.PurchaseOrderStatus;
import com.iotlaser.spms.erp.service.IFinApInvoiceService;
import com.iotlaser.spms.erp.service.IPurchaseInboundService;
import com.iotlaser.spms.erp.service.IPurchaseOrderService;
import com.iotlaser.spms.erp.service.IThreeWayMatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 三单匹配服务实现
 *
 * <AUTHOR> Kai
 * @date 2025/06/21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ThreeWayMatchServiceImpl implements IThreeWayMatchService {

    private final IPurchaseOrderService purchaseOrderService;
    private final IPurchaseInboundService purchaseInboundService;
    private final IFinApInvoiceService finApInvoiceService;

    /**
     * 获取三单匹配工作台数据
     *
     * @param supplierId 供应商ID（可选）
     * @param status     匹配状态（可选）
     * @return 三单匹配工作台数据
     */
    @Override
    public Map<String, Object> getThreeWayMatchWorkbench(Long supplierId, String status) {
        try {
            Map<String, Object> workbench = new HashMap<>();

            // 统计待匹配的单据数量
            Map<String, Integer> pendingCounts = getPendingMatchCounts(supplierId);
            workbench.put("pendingCounts", pendingCounts);

            // 获取待匹配的采购订单列表
            List<PurchaseOrderVo> pendingOrders = getPendingMatchOrders(supplierId);
            workbench.put("pendingOrders", pendingOrders);

            // 获取匹配异常列表
            List<Map<String, Object>> matchExceptions = getMatchExceptions(supplierId);
            workbench.put("matchExceptions", matchExceptions);

            // 获取匹配统计信息
            Map<String, Object> matchStatistics = getMatchStatistics(supplierId);
            workbench.put("matchStatistics", matchStatistics);

            workbench.put("queryTime", LocalDateTime.now());
            workbench.put("supplierId", supplierId);
            workbench.put("status", status);

            log.info("获取三单匹配工作台数据成功 - 供应商ID: {}, 状态: {}", supplierId, status);

            return workbench;
        } catch (Exception e) {
            log.error("获取三单匹配工作台数据失败 - 供应商ID: {}, 错误: {}", supplierId, e.getMessage(), e);
            throw new ServiceException("获取三单匹配工作台数据失败：" + e.getMessage());
        }
    }

    /**
     * 执行三单匹配
     *
     * @param orderId      采购订单ID
     * @param inboundId    入库单ID
     * @param invoiceId    发票ID
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 匹配结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> performThreeWayMatch(Long orderId, Long inboundId, Long invoiceId,
                                                    Long operatorId, String operatorName) {
        // TODO: [三单匹配-核心实现] - 参考文档 docs/design/README_FINANCE.md
        // 1. 数据获取: 分别调用 Service 查询采购订单、入库单、发票的完整信息（包括明细）。
        // 2. 业务校验:
        //    - 检查三张单据的供应商是否一致。
        //    - 检查入库单和发票的状态是否允许匹配 (例如，入库单已完成，发票已审核)。
        // 3. 核心匹配 (明细层):
        //    - 遍历发票的每一条明细 (InvoiceItem)。
        //    - 对于每条发票明细，在采购订单和入库单的明细中找到对应的物料。
        //    - 严格比对三方的“数量”和“不含税单价”。
        //    - 允许在配置的容差范围内进行匹配。
        // 4. 结果处理:
        //    - 如果所有明细都匹配成功，则认为三单匹配成功。
        //    - 如果部分匹配，记录差异项。
        //    - 如果完全不匹配，则标记为失败。
        // 5. 数据持久化:
        //    - 创建三单匹配记录，并保存到数据库。
        //    - 更新采购订单、入库单、发票的匹配状态。
        try {
            // 获取三单数据
            PurchaseOrderVo order = purchaseOrderService.queryById(orderId);
            PurchaseInboundVo inbound = purchaseInboundService.queryById(inboundId);
            FinApInvoiceVo invoice = finApInvoiceService.queryById(invoiceId);

            if (order == null || inbound == null || invoice == null) {
                throw new ServiceException("单据数据不完整，无法执行三单匹配");
            }

            // 执行匹配验证
            Map<String, Object> matchResult = validateThreeWayMatch(order, inbound, invoice);

            // 如果匹配成功，创建匹配记录
            if ((Boolean) matchResult.get("success")) {
                Long matchId = createMatchRecord(order, inbound, invoice, operatorId, operatorName);
                matchResult.put("matchId", matchId);

                // 更新单据状态
                updateDocumentStatus(orderId, inboundId, invoiceId);

                log.info("三单匹配成功 - 订单: {}, 入库: {}, 发票: {}, 操作人: {}",
                    order.getOrderCode(), inbound.getInboundCode(), invoice.getInvoiceCode(), operatorName);
            } else {
                log.warn("三单匹配失败 - 订单: {}, 入库: {}, 发票: {}, 原因: {}",
                    order.getOrderCode(), inbound.getInboundCode(), invoice.getInvoiceCode(),
                    matchResult.get("reason"));
            }

            matchResult.put("operatorId", operatorId);
            matchResult.put("operatorName", operatorName);
            matchResult.put("matchTime", LocalDateTime.now());

            return matchResult;
        } catch (Exception e) {
            log.error("执行三单匹配失败 - 订单ID: {}, 入库ID: {}, 发票ID: {}, 错误: {}",
                orderId, inboundId, invoiceId, e.getMessage(), e);
            throw new ServiceException("执行三单匹配失败：" + e.getMessage());
        }
    }

    /**
     * 获取智能匹配建议
     *
     * @param orderId 采购订单ID
     * @return 匹配建议列表
     */
    @Override
    public List<ThreeWayMatchSuggestion> getMatchSuggestions(Long orderId) {
        try {
            List<ThreeWayMatchSuggestion> suggestions = new ArrayList<>();

            // 获取采购订单信息
            PurchaseOrderVo order = purchaseOrderService.queryById(orderId);
            if (order == null) {
                throw new ServiceException("采购订单不存在");
            }

            // 查找候选入库单
            List<PurchaseInboundVo> candidateInbounds = findCandidateInbounds(order);

            // 查找候选发票
            List<FinApInvoiceVo> candidateInvoices = findCandidateInvoices(order);

            // 计算匹配建议
            for (PurchaseInboundVo inbound : candidateInbounds) {
                for (FinApInvoiceVo invoice : candidateInvoices) {
                    ThreeWayMatchSuggestion suggestion = calculateMatchSuggestion(order, inbound, invoice);
                    if (suggestion.getMatchScore() > 0.6) { // 匹配度大于60%才推荐
                        suggestions.add(suggestion);
                    }
                }
            }

            // 按匹配度排序
            suggestions.sort((s1, s2) -> Double.compare(s2.getMatchScore(), s1.getMatchScore()));

            // 限制推荐数量
            int maxSuggestions = 10;
            if (suggestions.size() > maxSuggestions) {
                suggestions = suggestions.subList(0, maxSuggestions);
            }

            log.info("获取三单匹配建议成功 - 订单: {}, 建议数量: {}",
                order.getOrderCode(), suggestions.size());

            return suggestions;
        } catch (Exception e) {
            log.error("获取三单匹配建议失败 - 订单ID: {}, 错误: {}", orderId, e.getMessage(), e);
            throw new ServiceException("获取三单匹配建议失败：" + e.getMessage());
        }
    }

    /**
     * 批量三单匹配
     *
     * @param matchRequests 匹配请求列表
     * @param operatorId    操作人ID
     * @param operatorName  操作人姓名
     * @return 批量匹配结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchThreeWayMatch(List<ThreeWayMatchRequest> matchRequests,
                                                  Long operatorId, String operatorName) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> successList = new ArrayList<>();
            List<Map<String, Object>> failureList = new ArrayList<>();

            for (ThreeWayMatchRequest request : matchRequests) {
                try {
                    Map<String, Object> matchResult = performThreeWayMatch(
                        request.getOrderId(), request.getInboundId(), request.getInvoiceId(),
                        operatorId, operatorName);

                    if ((Boolean) matchResult.get("success")) {
                        successList.add(Map.of(
                            "orderId", request.getOrderId(),
                            "inboundId", request.getInboundId(),
                            "invoiceId", request.getInvoiceId(),
                            "matchId", matchResult.get("matchId"),
                            "status", "SUCCESS"
                        ));
                    } else {
                        failureList.add(Map.of(
                            "orderId", request.getOrderId(),
                            "inboundId", request.getInboundId(),
                            "invoiceId", request.getInvoiceId(),
                            "status", "FAILED",
                            "reason", matchResult.get("reason")
                        ));
                    }
                } catch (Exception e) {
                    failureList.add(Map.of(
                        "orderId", request.getOrderId(),
                        "inboundId", request.getInboundId(),
                        "invoiceId", request.getInvoiceId(),
                        "status", "ERROR",
                        "reason", e.getMessage()
                    ));
                }
            }

            result.put("total", matchRequests.size());
            result.put("successCount", successList.size());
            result.put("failureCount", failureList.size());
            result.put("successList", successList);
            result.put("failureList", failureList);
            result.put("operatorId", operatorId);
            result.put("operatorName", operatorName);
            result.put("operationTime", LocalDateTime.now());

            log.info("批量三单匹配完成 - 总数: {}, 成功: {}, 失败: {}, 操作人: {}",
                matchRequests.size(), successList.size(), failureList.size(), operatorName);

            return result;
        } catch (Exception e) {
            log.error("批量三单匹配失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("批量三单匹配失败：" + e.getMessage());
        }
    }

    /**
     * 获取匹配差异分析
     *
     * @param orderId   采购订单ID
     * @param inboundId 入库单ID
     * @param invoiceId 发票ID
     * @return 差异分析结果
     */
    @Override
    public Map<String, Object> analyzeMatchDifferences(Long orderId, Long inboundId, Long invoiceId) {
        try {
            // 获取三单数据
            PurchaseOrderVo order = purchaseOrderService.queryById(orderId);
            PurchaseInboundVo inbound = purchaseInboundService.queryById(inboundId);
            FinApInvoiceVo invoice = finApInvoiceService.queryById(invoiceId);

            Map<String, Object> analysis = new HashMap<>();
            List<Map<String, Object>> differences = new ArrayList<>();

            // 供应商差异分析
            if (!Objects.equals(order.getSupplierId(), inbound.getSupplierId()) ||
                !Objects.equals(order.getSupplierId(), invoice.getPayeeId())) {
                differences.add(Map.of(
                    "type", "SUPPLIER",
                    "description", "供应商不一致",
                    "orderValue", order.getSupplierName(),
                    "inboundValue", inbound.getSupplierName(),
                    "invoiceValue", invoice.getPayeeId(),
                    "severity", "HIGH"
                ));
            }

            // 金额差异分析
            // TODO: PurchaseOrderVo、PurchaseInboundVo、FinApInvoiceVo中没有totalAmount字段
            // 暂时使用默认值，待实体完善后通过明细汇总计算
            BigDecimal orderAmount = BigDecimal.ZERO; // 原: order.getTotalAmount()
            BigDecimal inboundAmount = BigDecimal.ZERO; // 原: inbound.getTotalAmount()
            BigDecimal invoiceAmount = BigDecimal.ZERO; // 原: invoice.getTotalAmount()

            BigDecimal tolerance = orderAmount.multiply(new BigDecimal("0.05")); // 5%容差

            if (orderAmount.subtract(inboundAmount).abs().compareTo(tolerance) > 0) {
                differences.add(Map.of(
                    "type", "AMOUNT",
                    "description", "订单与入库金额差异超过容差",
                    "orderValue", orderAmount,
                    "inboundValue", inboundAmount,
                    "difference", orderAmount.subtract(inboundAmount),
                    "severity", "MEDIUM"
                ));
            }

            if (orderAmount.subtract(invoiceAmount).abs().compareTo(tolerance) > 0) {
                differences.add(Map.of(
                    "type", "AMOUNT",
                    "description", "订单与发票金额差异超过容差",
                    "orderValue", orderAmount,
                    "invoiceValue", invoiceAmount,
                    "difference", orderAmount.subtract(invoiceAmount),
                    "severity", "MEDIUM"
                ));
            }

            // 时间差异分析
            // TODO: 添加时间差异分析逻辑

            analysis.put("differences", differences);
            analysis.put("differenceCount", differences.size());
            analysis.put("hasCriticalDifferences", differences.stream()
                .anyMatch(d -> "HIGH".equals(d.get("severity"))));
            analysis.put("analysisTime", LocalDateTime.now());

            return analysis;
        } catch (Exception e) {
            log.error("分析三单匹配差异失败 - 错误: {}", e.getMessage(), e);
            throw new ServiceException("分析三单匹配差异失败：" + e.getMessage());
        }
    }

    /**
     * 处理匹配差异
     *
     * @param matchId        匹配记录ID
     * @param differenceType 差异类型
     * @param handleMethod   处理方式
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleMatchDifference(Long matchId, String differenceType, String handleMethod,
                                         Long operatorId, String operatorName) {
        try {
            // 校验参数
            if (matchId == null) {
                throw new ServiceException("匹配记录ID不能为空");
            }
            if (StringUtils.isBlank(differenceType)) {
                throw new ServiceException("差异类型不能为空");
            }
            if (StringUtils.isBlank(handleMethod)) {
                throw new ServiceException("处理方式不能为空");
            }

            // 获取匹配记录
            // TODO: 需要实现获取三单匹配记录的方法
            // ThreeWayMatchRecord matchRecord = threeWayMatchRecordService.queryById(matchId);
            // if (matchRecord == null) {
            //     throw new ServiceException("匹配记录不存在");
            // }

            // 根据差异类型和处理方式执行相应操作
            boolean result = false;
            switch (differenceType) {
                case "SUPPLIER":
                    result = handleSupplierDifference(matchId, handleMethod, operatorId, operatorName);
                    break;
                case "AMOUNT":
                    result = handleAmountDifference(matchId, handleMethod, operatorId, operatorName);
                    break;
                case "QUANTITY":
                    result = handleQuantityDifference(matchId, handleMethod, operatorId, operatorName);
                    break;
                case "DATE":
                    result = handleDateDifference(matchId, handleMethod, operatorId, operatorName);
                    break;
                default:
                    throw new ServiceException("不支持的差异类型：" + differenceType);
            }

            // 更新匹配记录状态
            if (result) {
                // TODO: 更新匹配记录的处理状态
                // threeWayMatchRecordService.updateHandleStatus(matchId, "HANDLED", operatorId, operatorName);

                // 记录处理日志
                // threeWayMatchLogService.recordHandleLog(matchId, differenceType, handleMethod,
                //     "SUCCESS", "差异处理成功", operatorId, operatorName);
            }

            log.info("处理匹配差异成功 - 匹配ID: {}, 差异类型: {}, 处理方式: {}, 操作人: {}",
                matchId, differenceType, handleMethod, operatorName);

            return result;
        } catch (Exception e) {
            log.error("处理匹配差异失败 - 匹配ID: {}, 错误: {}", matchId, e.getMessage(), e);
            throw new ServiceException("处理匹配差异失败：" + e.getMessage());
        }
    }

    /**
     * 处理供应商差异
     */
    private boolean handleSupplierDifference(Long matchId, String handleMethod, Long operatorId, String operatorName) {
        switch (handleMethod) {
            case "IGNORE":
                // 忽略差异，强制匹配
                log.info("忽略供应商差异 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            case "MANUAL_CORRECT":
                // 手工修正，需要人工介入
                log.info("供应商差异需要手工修正 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            case "REJECT":
                // 拒绝匹配
                log.info("拒绝供应商差异匹配 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            default:
                throw new ServiceException("不支持的供应商差异处理方式：" + handleMethod);
        }
    }

    /**
     * 处理金额差异
     */
    private boolean handleAmountDifference(Long matchId, String handleMethod, Long operatorId, String operatorName) {
        switch (handleMethod) {
            case "TOLERANCE_ACCEPT":
                // 在容差范围内接受
                log.info("容差范围内接受金额差异 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            case "PARTIAL_MATCH":
                // 部分匹配
                log.info("金额差异部分匹配 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            case "MANUAL_ADJUST":
                // 手工调整
                log.info("金额差异需要手工调整 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            default:
                throw new ServiceException("不支持的金额差异处理方式：" + handleMethod);
        }
    }

    /**
     * 处理数量差异
     */
    private boolean handleQuantityDifference(Long matchId, String handleMethod, Long operatorId, String operatorName) {
        switch (handleMethod) {
            case "PARTIAL_DELIVERY":
                // 部分交货
                log.info("数量差异按部分交货处理 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            case "SHORTAGE_CLAIM":
                // 短缺索赔
                log.info("数量差异提起短缺索赔 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            case "ACCEPT_VARIANCE":
                // 接受差异
                log.info("接受数量差异 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            default:
                throw new ServiceException("不支持的数量差异处理方式：" + handleMethod);
        }
    }

    /**
     * 处理日期差异
     */
    private boolean handleDateDifference(Long matchId, String handleMethod, Long operatorId, String operatorName) {
        switch (handleMethod) {
            case "IGNORE":
                // 忽略日期差异
                log.info("忽略日期差异 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            case "UPDATE_SCHEDULE":
                // 更新计划
                log.info("日期差异更新计划 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            case "PENALTY_APPLY":
                // 应用罚金
                log.info("日期差异应用罚金 - 匹配ID: {}, 操作人: {}", matchId, operatorName);
                return true;
            default:
                throw new ServiceException("不支持的日期差异处理方式：" + handleMethod);
        }
    }

    // 私有辅助方法

    /**
     * 获取待匹配单据数量统计
     * ✅ 完善：实现实际的数据库查询统计
     */
    private Map<String, Integer> getPendingMatchCounts(Long supplierId) {
        Map<String, Integer> counts = new HashMap<>();

        try {
            // 统计待匹配的采购订单数量
            PurchaseOrderBo orderQuery = new PurchaseOrderBo();
            orderQuery.setSupplierId(supplierId);
            orderQuery.setOrderStatus(PurchaseOrderStatus.CONFIRMED);
            List<PurchaseOrderVo> pendingOrders = purchaseOrderService.queryList(orderQuery);
            counts.put("pendingOrders", pendingOrders.size());

            // 统计待匹配的入库单数量
            PurchaseInboundBo inboundQuery = new PurchaseInboundBo();
            inboundQuery.setSupplierId(supplierId);
            // TODO: 修复PurchaseInboundBo的setInboundStatus方法类型
            // inboundQuery.setInboundStatus(PurchaseInboundStatus.COMPLETED.getValue());
            List<PurchaseInboundVo> pendingInbounds = purchaseInboundService.queryList(inboundQuery);
            counts.put("pendingInbounds", pendingInbounds.size());

            // 统计待匹配的发票数量
            // TODO: 待FinApInvoiceService完善后实现
            counts.put("pendingInvoices", 0);

            // 统计匹配异常数量
            // TODO: 待三单匹配记录表完善后实现
            counts.put("matchExceptions", 0);

            log.debug("获取待匹配单据统计 - 供应商: {}, 结果: {}", supplierId, counts);
        } catch (Exception e) {
            log.error("获取待匹配单据统计失败 - 供应商: {}, 错误: {}", supplierId, e.getMessage(), e);
            // 返回默认值
            counts.put("pendingOrders", 0);
            counts.put("pendingInbounds", 0);
            counts.put("pendingInvoices", 0);
            counts.put("matchExceptions", 0);
        }

        return counts;
    }

    /**
     * 获取待匹配的采购订单列表
     * ✅ 完善：实现实际的数据库查询
     */
    private List<PurchaseOrderVo> getPendingMatchOrders(Long supplierId) {
        try {
            PurchaseOrderBo queryBo = new PurchaseOrderBo();
            queryBo.setSupplierId(supplierId);
            queryBo.setOrderStatus(PurchaseOrderStatus.CONFIRMED);

            // 查询已确认但未完全匹配的采购订单
            List<PurchaseOrderVo> orders = purchaseOrderService.queryList(queryBo);

            // TODO: 过滤已完全匹配的订单（待三单匹配记录表完善后实现）
            // orders = orders.stream()
            //     .filter(order -> !isOrderFullyMatched(order.getOrderId()))
            //     .collect(Collectors.toList());

            log.debug("获取待匹配采购订单 - 供应商: {}, 数量: {}", supplierId, orders.size());
            return orders;
        } catch (Exception e) {
            log.error("获取待匹配采购订单失败 - 供应商: {}, 错误: {}", supplierId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取匹配异常列表
     * ✅ 完善：实现基础的异常检测逻辑
     */
    private List<Map<String, Object>> getMatchExceptions(Long supplierId) {
        List<Map<String, Object>> exceptions = new ArrayList<>();

        try {
            // 查找长期未匹配的采购订单（超过30天）
            PurchaseOrderBo orderQuery = new PurchaseOrderBo();
            orderQuery.setSupplierId(supplierId);
            orderQuery.setOrderStatus(PurchaseOrderStatus.CONFIRMED);

            List<PurchaseOrderVo> orders = purchaseOrderService.queryList(orderQuery);
            LocalDate thirtyDaysAgo = LocalDate.now().minusDays(30);

            for (PurchaseOrderVo order : orders) {
                if (order.getOrderDate() != null && order.getOrderDate().isBefore(thirtyDaysAgo)) {
                    Map<String, Object> exception = new HashMap<>();
                    exception.put("type", "LONG_PENDING_ORDER");
                    exception.put("orderId", order.getOrderId());
                    exception.put("orderCode", order.getOrderCode());
                    exception.put("orderDate", order.getOrderDate());
                    exception.put("description", "采购订单超过30天未匹配");
                    exception.put("severity", "MEDIUM");
                    exceptions.add(exception);
                }
            }

            // 查找金额异常的入库单
            // TODO: 待实现金额异常检测逻辑

            // 查找状态异常的单据
            // TODO: 待实现状态异常检测逻辑

            log.debug("获取匹配异常 - 供应商: {}, 异常数量: {}", supplierId, exceptions.size());
        } catch (Exception e) {
            log.error("获取匹配异常失败 - 供应商: {}, 错误: {}", supplierId, e.getMessage(), e);
        }

        return exceptions;
    }

    /**
     * 获取匹配统计信息
     * ✅ 完善：实现基础的统计计算逻辑
     */
    private Map<String, Object> getMatchStatistics(Long supplierId) {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 统计总订单数量
            PurchaseOrderBo orderQuery = new PurchaseOrderBo();
            orderQuery.setSupplierId(supplierId);
            List<PurchaseOrderVo> allOrders = purchaseOrderService.queryList(orderQuery);
            int totalOrders = allOrders.size();

            // 统计已确认订单数量
            long confirmedOrders = allOrders.stream()
                .filter(order -> PurchaseOrderStatus.CONFIRMED.equals(order.getOrderStatus()) ||
                    PurchaseOrderStatus.FULLY_RECEIVED.equals(order.getOrderStatus()) ||
                    PurchaseOrderStatus.PARTIALLY_RECEIVED.equals(order.getOrderStatus()))
                .count();

            // 计算成功率（暂时以已确认订单比例计算）
            double successRate = totalOrders > 0 ? (double) confirmedOrders / totalOrders : 0.0;

            // 统计平均处理时间（暂时使用固定值，待完善）
            double avgMatchTime = 2.5; // 天

            statistics.put("totalOrders", totalOrders);
            statistics.put("confirmedOrders", confirmedOrders);
            statistics.put("successRate", Math.round(successRate * 100.0) / 100.0);
            statistics.put("avgMatchTime", avgMatchTime);

            // TODO: 待三单匹配记录表完善后，实现真实的匹配统计
            statistics.put("totalMatched", 0);
            statistics.put("pendingMatch", confirmedOrders);

            log.debug("获取匹配统计 - 供应商: {}, 统计: {}", supplierId, statistics);
        } catch (Exception e) {
            log.error("获取匹配统计失败 - 供应商: {}, 错误: {}", supplierId, e.getMessage(), e);
            // 返回默认统计
            statistics.put("totalOrders", 0);
            statistics.put("confirmedOrders", 0);
            statistics.put("successRate", 0.0);
            statistics.put("avgMatchTime", 0.0);
            statistics.put("totalMatched", 0);
            statistics.put("pendingMatch", 0);
        }

        return statistics;
    }

    /**
     * 验证三单匹配
     */
    private Map<String, Object> validateThreeWayMatch(PurchaseOrderVo order,
                                                      PurchaseInboundVo inbound,
                                                      FinApInvoiceVo invoice) {
        Map<String, Object> result = new HashMap<>();
        List<String> errors = new ArrayList<>();

        // 供应商一致性检查
        if (!Objects.equals(order.getSupplierId(), inbound.getSupplierId()) ||
            !Objects.equals(order.getSupplierId(), invoice.getPayeeId())) {
            errors.add("供应商不一致");
        }

        // 金额容差检查
        // TODO: 在PurchaseOrderVo和PurchaseInboundVo中添加getAmount()方法
        // 使用amount字段进行金额验证（如果VO中有该字段）
        // if (order.getAmount() != null && inbound.getAmount() != null && invoice.getAmount() != null) {
        //     BigDecimal tolerance = new BigDecimal("5"); // 5%容差
        //
        //     // 检查订单与入库金额差异
        //     if (!AmountCalculationUtils.isWithinTolerance(order.getAmount(), inbound.getAmount(), tolerance)) {
        //         errors.add("订单与入库金额差异超过容差(5%)");
        //     }
        //
        //     // 检查订单与发票金额差异
        //     if (!AmountCalculationUtils.isWithinTolerance(order.getAmount(), invoice.getAmount(), tolerance)) {
        //         errors.add("订单与发票金额差异超过容差(5%)");
        //     }
        //
        //     // 检查入库与发票金额差异
        //     if (!AmountCalculationUtils.isWithinTolerance(inbound.getAmount(), invoice.getAmount(), tolerance)) {
        //         errors.add("入库与发票金额差异超过容差(5%)");
        //     }
        // } else {
        //     log.warn("金额字段缺失，跳过金额容差检查 - 订单ID: {}", order.getOrderId());
        // }

        // 状态检查
        if (!"COMPLETED".equals(inbound.getInboundStatus())) {
            errors.add("入库单状态不正确");
        }

        if (!"APPROVED".equals(invoice.getInvoiceStatus())) {
            errors.add("发票状态不正确");
        }

        result.put("success", errors.isEmpty());
        result.put("errors", errors);
        result.put("reason", errors.isEmpty() ? "匹配成功" : String.join(", ", errors));

        return result;
    }

    /**
     * 创建匹配记录
     */
    private Long createMatchRecord(PurchaseOrderVo order, PurchaseInboundVo inbound,
                                   FinApInvoiceVo invoice, Long operatorId, String operatorName) {
        // TODO: [三单匹配记录表设计与实现] - 参考文档 docs/design/README_FINANCE.md
        // 需要创建三单匹配记录表 erp_fin_three_way_match，包含以下字段：
        // 1. match_id (主键)
        // 2. purchase_order_id, purchase_order_code (采购订单信息)
        // 3. purchase_inbound_id, purchase_inbound_code (采购入库单信息)
        // 4. ap_invoice_id, ap_invoice_code (应付发票信息)
        // 5. match_status (匹配状态：SUCCESS/PARTIAL/FAILED)
        // 6. match_time, operator_id, operator_name (匹配时间和操作人)
        // 7. total_amount, matched_amount (总金额和匹配金额)
        // 8. difference_amount, difference_reason (差异金额和原因)
        // 9. remark (备注)

        // 临时返回时间戳作为匹配记录ID
        return System.currentTimeMillis();
    }

    /**
     * 更新单据状态
     */
    private void updateDocumentStatus(Long orderId, Long inboundId, Long invoiceId) {
        // TODO: 更新相关单据的匹配状态
        // 更新采购订单状态为已匹配
        // 更新入库单状态为已匹配
        // 更新发票状态为已匹配
    }

    /**
     * 查找候选入库单
     */
    private List<PurchaseInboundVo> findCandidateInbounds(PurchaseOrderVo order) {
        try {
            List<PurchaseInboundVo> candidates = new ArrayList<>();

            // 根据采购订单查找直接关联的入库单
            // TODO: 需要实现根据订单ID查询入库单的方法
            // List<PurchaseInboundVo> directInbounds = purchaseInboundService.queryByOrderId(order.getOrderId());
            // candidates.addAll(directInbounds);

            // 根据供应商查找候选入库单（时间范围内）
            // PurchaseInboundBo queryBo = new PurchaseInboundBo();
            // queryBo.setSupplierId(order.getSupplierId());
            // queryBo.setInboundStatus("COMPLETED"); // 只考虑已完成的入库单
            //
            // // 设置时间范围：订单日期前后30天
            // LocalDate startDate = order.getOrderDate().minusDays(30);
            // LocalDate endDate = order.getOrderDate().plusDays(30);
            // queryBo.setInboundTimeStart(startDate);
            // queryBo.setInboundTimeEnd(endDate);
            //
            // List<PurchaseInboundVo> supplierInbounds = purchaseInboundService.queryList(queryBo);
            // candidates.addAll(supplierInbounds);

            // 去重
            // candidates = candidates.stream()
            //     .collect(Collectors.toMap(
            //         PurchaseInboundVo::getInboundId,
            //         Function.identity(),
            //         (existing, replacement) -> existing))
            //     .values()
            //     .stream()
            //     .collect(Collectors.toList());

            log.info("查找候选入库单 - 订单: {}, 候选数量: {}", order.getOrderCode(), candidates.size());
            return candidates;
        } catch (Exception e) {
            log.error("查找候选入库单失败 - 订单ID: {}, 错误: {}", order.getOrderId(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 查找候选发票
     */
    private List<FinApInvoiceVo> findCandidateInvoices(PurchaseOrderVo order) {
        try {
            List<FinApInvoiceVo> candidates = new ArrayList<>();

            // 根据供应商查找候选发票
            // FinApInvoiceBo queryBo = new FinApInvoiceBo();
            // queryBo.setSupplierId(order.getSupplierId());
            // queryBo.setInvoiceStatus("APPROVED"); // 只考虑已审批的发票
            //
            // // 设置时间范围：订单日期前后60天
            // LocalDate startDate = order.getOrderDate().minusDays(60);
            // LocalDate endDate = order.getOrderDate().plusDays(60);
            // queryBo.setInvoiceDateStart(startDate);
            // queryBo.setInvoiceDateEnd(endDate);
            //
            // List<FinApInvoiceVo> supplierInvoices = finApInvoiceService.queryList(queryBo);
            // candidates.addAll(supplierInvoices);

            // 过滤已匹配的发票
            // candidates = candidates.stream()
            //     .filter(invoice -> !isInvoiceAlreadyMatched(invoice.getInvoiceId()))
            //     .collect(Collectors.toList());

            log.info("查找候选发票 - 订单: {}, 候选数量: {}", order.getOrderCode(), candidates.size());
            return candidates;
        } catch (Exception e) {
            log.error("查找候选发票失败 - 订单ID: {}, 错误: {}", order.getOrderId(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查发票是否已经匹配
     */
    private boolean isInvoiceAlreadyMatched(Long invoiceId) {
        // TODO: 检查发票是否已经在三单匹配记录中
        // 可以查询三单匹配记录表，看是否存在该发票的匹配记录
        return false;
    }

    /**
     * 计算匹配建议
     */
    private ThreeWayMatchSuggestion calculateMatchSuggestion(PurchaseOrderVo order,
                                                             PurchaseInboundVo inbound,
                                                             FinApInvoiceVo invoice) {
        ThreeWayMatchSuggestion suggestion = new ThreeWayMatchSuggestion();

        suggestion.setOrderId(order.getOrderId());
        suggestion.setOrderCode(order.getOrderCode());
        suggestion.setInboundId(inbound.getInboundId());
        suggestion.setInboundCode(inbound.getInboundCode());
        suggestion.setInvoiceId(invoice.getInvoiceId());
        suggestion.setInvoiceCode(invoice.getInvoiceCode());

        // 计算匹配度
        double score = 0.0;
        List<String> differences = new ArrayList<>();

        // 供应商匹配 (40%)
        if (Objects.equals(order.getSupplierId(), inbound.getSupplierId()) &&
            Objects.equals(order.getSupplierId(), invoice.getPayeeId())) {
            score += 0.4;
        } else {
            differences.add("供应商不一致");
        }

        // 金额匹配 (30%)
        // TODO: VO中没有totalAmount字段，暂时给予满分
        // BigDecimal tolerance = order.getTotalAmount().multiply(new BigDecimal("0.05"));
        // if (order.getTotalAmount().subtract(inbound.getTotalAmount()).abs().compareTo(tolerance) <= 0 &&
        //     order.getTotalAmount().subtract(invoice.getTotalAmount()).abs().compareTo(tolerance) <= 0) {
        //     score += 0.3;
        // } else {
        //     differences.add("金额差异超过容差");
        // }
        score += 0.3; // 暂时给予满分，待实体完善后修正

        // 时间匹配 (20%)
        // TODO: 添加时间匹配逻辑
        score += 0.2;

        // 状态匹配 (10%)
        if ("COMPLETED".equals(inbound.getInboundStatus()) && "APPROVED".equals(invoice.getInvoiceStatus())) {
            score += 0.1;
        } else {
            differences.add("单据状态不正确");
        }

        suggestion.setMatchScore(score);
        suggestion.setMatchReason(score > 0.8 ? "高度匹配" : score > 0.6 ? "中度匹配" : "低度匹配");
        suggestion.setDifferences(differences);

        return suggestion;
    }
}
