package com.iotlaser.spms.mes.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.mes.domain.ProductionInboundItem;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 生产入库明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/23
 */
public interface ProductionInboundItemMapper extends BaseMapperPlus<ProductionInboundItem, ProductionInboundItemVo> {

    /**
     * 删除生产入库明细表
     */
    default int deleteByInboundId(Long inboundId) {
        return delete(new LambdaQueryWrapper<ProductionInboundItem>().eq(ProductionInboundItem::getInboundId, inboundId));
    }
}
