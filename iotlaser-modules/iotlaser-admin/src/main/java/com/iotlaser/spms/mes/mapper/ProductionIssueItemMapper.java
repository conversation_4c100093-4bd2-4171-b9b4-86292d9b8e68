package com.iotlaser.spms.mes.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.mes.domain.ProductionIssueItem;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 生产领料明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/07
 */
public interface ProductionIssueItemMapper extends BaseMapperPlus<ProductionIssueItem, ProductionIssueItemVo> {

    default List<ProductionIssueItemVo> selectListByIssueId(Long issueId) {
        return selectVoList(new LambdaQueryWrapper<ProductionIssueItem>().eq(ProductionIssueItem::getIssueId, issueId));
    }

    default int deleteByIssueId(Long issueId) {
        return delete(new LambdaQueryWrapper<ProductionIssueItem>().eq(ProductionIssueItem::getIssueId, issueId));
    }
}
