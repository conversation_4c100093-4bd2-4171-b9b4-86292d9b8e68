package com.iotlaser.spms.mes.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.mes.domain.ProductionIssue;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 生产领料Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
public interface ProductionIssueMapper extends BaseMapperPlus<ProductionIssue, ProductionIssueVo> {
    default Boolean existsByDirectSourceId(Long directSourceId) {
        return exists(new LambdaQueryWrapper<ProductionIssue>().eq(ProductionIssue::getDirectSourceId, directSourceId));
    }
}
