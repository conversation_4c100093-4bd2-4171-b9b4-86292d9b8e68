package com.iotlaser.spms.mes.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.mes.domain.ProductionReturnItem;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnItemVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 生产退料明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
public interface ProductionReturnItemMapper extends BaseMapperPlus<ProductionReturnItem, ProductionReturnItemVo> {
    default List<ProductionReturnItemVo> selectListByReturnId(Long returnId) {
        return selectVoList(new LambdaQueryWrapper<ProductionReturnItem>().eq(ProductionReturnItem::getReturnId, returnId));
    }

    default int deleteByReturnId(Long returnId) {
        return delete(new LambdaQueryWrapper<ProductionReturnItem>().eq(ProductionReturnItem::getReturnId, returnId));
    }
}
