package com.iotlaser.spms.mes.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.mes.domain.ProductionReturn;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 生产退料Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
public interface ProductionReturnMapper extends BaseMapperPlus<ProductionReturn, ProductionReturnVo> {
    default Boolean existsByDirectSourceId(Long directSourceId) {
        return exists(new LambdaQueryWrapper<ProductionReturn>().eq(ProductionReturn::getDirectSourceId, directSourceId));
    }
}
