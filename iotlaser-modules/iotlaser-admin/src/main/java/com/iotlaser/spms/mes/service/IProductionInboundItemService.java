package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.ProductionInboundItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产入库明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/23
 */
public interface IProductionInboundItemService {

    /**
     * 查询生产入库明细
     *
     * @param itemId 主键
     * @return 生产入库明细
     */
    ProductionInboundItemVo queryById(Long itemId);

    /**
     * 分页查询生产入库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产入库明细分页列表
     */
    TableDataInfo<ProductionInboundItemVo> queryPageList(ProductionInboundItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产入库明细列表
     *
     * @param bo 查询条件
     * @return 生产入库明细列表
     */
    List<ProductionInboundItemVo> queryList(ProductionInboundItemBo bo);

    /**
     * 新增生产入库明细
     *
     * @param bo 生产入库明细
     * @return 是否新增成功
     */
    Boolean insertByBo(ProductionInboundItemBo bo);

    /**
     * 修改生产入库明细
     *
     * @param bo 生产入库明细
     * @return 是否修改成功
     */
    Boolean updateByBo(ProductionInboundItemBo bo);

    /**
     * 校验并批量删除生产入库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入或更新生产入库明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<ProductionInboundItemBo> items);

}
