package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.ProductionInboundBo;
import com.iotlaser.spms.mes.domain.vo.ProductionInboundVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产入库Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/23
 */
public interface IProductionInboundService {

    /**
     * 查询生产入库
     *
     * @param inboundId 主键
     * @return 生产入库
     */
    ProductionInboundVo queryById(Long inboundId);

    /**
     * 分页查询生产入库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产入库分页列表
     */
    TableDataInfo<ProductionInboundVo> queryPageList(ProductionInboundBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产入库列表
     *
     * @param bo 查询条件
     * @return 生产入库列表
     */
    List<ProductionInboundVo> queryList(ProductionInboundBo bo);

    /**
     * 新增生产入库
     *
     * @param bo 生产入库
     * @return 是否新增成功
     */
    ProductionInboundVo insertByBo(ProductionInboundBo bo);

    /**
     * 修改生产入库
     *
     * @param bo 生产入库
     * @return 是否修改成功
     */
    ProductionInboundVo updateByBo(ProductionInboundBo bo);

    /**
     * 校验并批量删除生产入库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 确认生产入库单
     *
     * @param inboundId 入库单ID
     * @return 是否确认成功
     */
    Boolean confirmInbound(Long inboundId);

    /**
     * 批量确认生产入库单
     *
     * @param inboundIds 入库单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmInbounds(Collection<Long> inboundIds);

    /**
     * 完成生产入库
     *
     * @param inboundId 入库单ID
     * @return 是否完成成功
     */
    Boolean completeInbound(Long inboundId);

    /**
     * 根据生产订单创建入库单
     *
     * @param productionOrderId 生产订单ID
     * @return 创建的入库单
     */
    ProductionInboundVo createFromProductionOrder(Long productionOrderId);
}
