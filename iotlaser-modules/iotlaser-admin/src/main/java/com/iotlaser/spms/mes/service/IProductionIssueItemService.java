package com.iotlaser.spms.mes.service;

import com.iotlaser.spms.mes.domain.bo.ProductionIssueItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 生产领料明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
public interface IProductionIssueItemService {

    /**
     * 查询生产领料明细
     *
     * @param itemId 主键
     * @return 生产领料明细
     */
    ProductionIssueItemVo queryById(Long itemId);

    /**
     * 分页查询生产领料明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产领料明细分页列表
     */
    TableDataInfo<ProductionIssueItemVo> queryPageList(ProductionIssueItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的生产领料明细列表
     *
     * @param bo 查询条件
     * @return 生产领料明细列表
     */
    List<ProductionIssueItemVo> queryList(ProductionIssueItemBo bo);

    /**
     * 新增生产领料明细
     *
     * @param bo 生产领料明细
     * @return 是否新增成功
     */
    Boolean insertByBo(ProductionIssueItemBo bo);

    /**
     * 修改生产领料明细
     *
     * @param bo 生产领料明细
     * @return 是否修改成功
     */
    Boolean updateByBo(ProductionIssueItemBo bo);

    /**
     * 校验并批量删除生产领料明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量新增生产领料明细
     *
     * @param items 生产领料明细列表
     * @return 是否新增成功
     */
    Boolean insertBatch(List<ProductionIssueItemBo> items);

    /**
     * 根据领料单ID查询明细ID列表
     *
     * @param issueId 领料单ID
     * @return 生产领料明细ID列表
     */
    List<Long> selectItemIdsByIssueId(Long issueId);

    /**
     * 批量新增或更新生产领料明细
     *
     * @param items 生产领料明细列表
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<ProductionIssueItemBo> items);

    /**
     * 根据ID集合删除生产领料明细
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    Boolean deleteByIds(Collection<Long> ids);

}
