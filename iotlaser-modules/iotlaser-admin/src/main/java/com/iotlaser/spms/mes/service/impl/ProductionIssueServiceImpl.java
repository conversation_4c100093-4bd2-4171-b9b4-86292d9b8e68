package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.mes.domain.ProductionIssue;
import com.iotlaser.spms.mes.domain.bo.ProductionIssueBo;
import com.iotlaser.spms.mes.domain.bo.ProductionIssueItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueItemVo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueVo;
import com.iotlaser.spms.mes.domain.vo.ProductionOrderVo;
import com.iotlaser.spms.mes.enums.ProductionIssueStatus;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import com.iotlaser.spms.mes.mapper.ProductionIssueMapper;
import com.iotlaser.spms.mes.service.IProductionIssueItemService;
import com.iotlaser.spms.mes.service.IProductionIssueService;
import com.iotlaser.spms.mes.service.IProductionOrderService;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.MES_PRODUCTION_ISSUE_CODE;

/**
 * 生产领料Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025-05-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionIssueServiceImpl implements IProductionIssueService {

    private final ProductionIssueMapper baseMapper;
    private final IProductionIssueItemService itemService;
    private final IInventoryService inventoryService;
    private final IProductionOrderService productionOrderService;
    private final IProductService productService;
    private final ILocationService locationService;
    private final Gen gen;

    /**
     * 查询生产领料
     *
     * @param issueId 主键
     * @return 生产领料
     */
    @Override
    public ProductionIssueVo queryById(Long issueId) {
        return baseMapper.selectVoById(issueId);
    }

    /**
     * 分页查询生产领料列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产领料分页列表
     */
    @Override
    public TableDataInfo<ProductionIssueVo> queryPageList(ProductionIssueBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionIssue> lqw = buildQueryWrapper(bo);
        Page<ProductionIssueVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产领料列表
     *
     * @param bo 查询条件
     * @return 生产领料列表
     */
    @Override
    public List<ProductionIssueVo> queryList(ProductionIssueBo bo) {
        LambdaQueryWrapper<ProductionIssue> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionIssue> buildQueryWrapper(ProductionIssueBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionIssue> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionIssue::getIssueId);
        lqw.eq(StringUtils.isNotBlank(bo.getIssueCode()), ProductionIssue::getIssueCode, bo.getIssueCode());
        lqw.eq(bo.getSourceId() != null, ProductionIssue::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), ProductionIssue::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(ProductionIssue::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, ProductionIssue::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), ProductionIssue::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(ProductionIssue::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getIssueTime() != null, ProductionIssue::getIssueTime, bo.getIssueTime());
        if (bo.getIssueStatus() != null) {
            lqw.eq(ProductionIssue::getIssueStatus, bo.getIssueStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionIssue::getStatus, bo.getStatus());
        lqw.between(params.get("beginIssueTime") != null && params.get("endIssueTime") != null,
            ProductionIssue::getIssueTime, params.get("beginIssueTime"), params.get("endIssueTime"));
        return lqw;
    }

    /**
     * 新增生产领料
     *
     * @param bo 生产领料
     * @return 创建的生产领料
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductionIssueVo insertByBo(ProductionIssueBo bo) {
        try {
            // 生成编码
            if (StringUtils.isEmpty(bo.getIssueCode())) {
                bo.setIssueCode(gen.code(MES_PRODUCTION_ISSUE_CODE));
            }

            // 设置初始状态
            if (bo.getIssueStatus() == null) {
                bo.setIssueStatus(ProductionIssueStatus.DRAFT);
            }
            if (bo.getIssueTime() == null) {
                bo.setIssueTime(LocalDateTime.now());
            }

            // 填充冗余字段
            fillRedundantFields(bo);

            // 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 转换为实体并校验
            ProductionIssue add = MapstructUtils.convert(bo, ProductionIssue.class);
            validEntityBeforeSave(add);

            // 插入主表
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("生产领料单创建失败");
            }

            log.info("生产领料单【{}】创建成功", add.getIssueCode());
            return MapstructUtils.convert(add, ProductionIssueVo.class);
        } catch (Exception e) {
            log.error("生产领料单创建失败：{}", e.getMessage(), e);
            throw new ServiceException("生产领料单创建失败：" + e.getMessage());
        }
    }

    /**
     * 修改生产领料
     *
     * @param bo 生产领料
     * @return 修改后的生产领料
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductionIssueVo updateByBo(ProductionIssueBo bo) {
        try {
            // 填充冗余字段
            fillRedundantFields(bo);

            // 填充责任人信息
            fillResponsiblePersonInfo(bo);

            // 转换为实体并校验
            ProductionIssue update = MapstructUtils.convert(bo, ProductionIssue.class);
            validEntityBeforeSave(update);

            // 更新主表
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("生产领料单更新失败");
            }

            log.info("生产领料单【{}】更新成功", update.getIssueCode());
            return MapstructUtils.convert(update, ProductionIssueVo.class);
        } catch (Exception e) {
            log.error("生产领料单更新失败：{}", e.getMessage(), e);
            throw new ServiceException("生产领料单更新失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionIssue entity) {
        // 校验领料单编号唯一性
        if (StringUtils.isNotBlank(entity.getIssueCode())) {
            LambdaQueryWrapper<ProductionIssue> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductionIssue::getIssueCode, entity.getIssueCode());
            if (entity.getIssueId() != null) {
                wrapper.ne(ProductionIssue::getIssueId, entity.getIssueId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("领料单编号已存在：" + entity.getIssueCode());
            }
        }

        // 校验状态流转合法性
        if (entity.getIssueId() != null) {
            ProductionIssue existing = baseMapper.selectById(entity.getIssueId());
            if (existing != null && !isValidStatusTransition(existing.getIssueStatus(), entity.getIssueStatus())) {
                throw new ServiceException("状态流转不合法");
            }
        }

        // 校验生产订单状态（如果有关联）
        if (entity.getDirectSourceId() != null && entity.getDirectSourceType() == DirectSourceType.PRODUCTION_ORDER) {
            ProductionOrderVo productionOrder = productionOrderService.queryById(entity.getDirectSourceId());
            if (productionOrder == null) {
                throw new ServiceException("关联的生产订单不存在");
            }
            // 只有确认状态的生产订单才能进行领料
            if (productionOrder.getOrderStatus() != ProductionOrderStatus.CONFIRMED
                && productionOrder.getOrderStatus() != ProductionOrderStatus.IN_PROGRESS) {
                throw new ServiceException("生产订单状态不允许领料");
            }
        }
    }

    /**
     * 校验并批量删除生产领料信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验领料单状态，只有草稿状态的领料单才能删除
            List<ProductionIssue> issues = baseMapper.selectByIds(ids);
            for (ProductionIssue issue : issues) {
                if (issue.getIssueStatus() != ProductionIssueStatus.DRAFT) {
                    throw new ServiceException("领料单【" + issue.getIssueCode() + "】状态为【" +
                        issue.getIssueStatus() + "】，不允许删除");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 确认生产领料单
     *
     * @param issueId 领料单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmIssue(Long issueId) {
        ProductionIssue issue = baseMapper.selectById(issueId);
        if (issue == null) {
            throw new ServiceException("领料单不存在");
        }

        // 校验状态
        if (issue.getIssueStatus() != ProductionIssueStatus.DRAFT) {
            throw new ServiceException("领料单【" + issue.getIssueCode() + "】状态为【" +
                issue.getIssueStatus() + "】，不允许确认");
        }

        try {
            // 在确认前进行库存预留检查
            processInventoryReservation(issue);

            // 更新状态为待出库
            issue.setIssueStatus(ProductionIssueStatus.PENDING_WAREHOUSE);
            issue.setIssueTime(LocalDateTime.now());

            int result = baseMapper.updateById(issue);
            if (result <= 0) {
                throw new ServiceException("确认领料单失败：领料单【" + issue.getIssueCode() + "】");
            }

            log.info("生产领料单【{}】确认成功", issue.getIssueCode());
            return true;
        } catch (Exception e) {
            log.error("生产领料单【{}】确认失败：{}", issue.getIssueCode(), e.getMessage(), e);
            throw new ServiceException("确认领料单失败：" + e.getMessage());
        }
    }

    /**
     * 批量确认生产领料单
     *
     * @param issueIds 领料单ID集合
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmIssues(Collection<Long> issueIds) {
        for (Long issueId : issueIds) {
            confirmIssue(issueId);
        }
        return true;
    }

    /**
     * 完成生产领料出库
     *
     * @param issueId 领料单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeIssue(Long issueId) {
        ProductionIssue issue = baseMapper.selectById(issueId);
        if (issue == null) {
            throw new ServiceException("领料单不存在");
        }

        // 校验状态
        if (issue.getIssueStatus() != ProductionIssueStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("领料单【" + issue.getIssueCode() + "】状态为【" +
                issue.getIssueStatus() + "】，不允许完成出库");
        }

        try {
            // 先处理库存扣减逻辑
            processInventoryDeduction(issue);

            // 更新状态为已出库
            issue.setIssueStatus(ProductionIssueStatus.COMPLETED);

            int result = baseMapper.updateById(issue);
            if (result <= 0) {
                throw new ServiceException("完成领料出库失败：领料单【" + issue.getIssueCode() + "】");
            }

            log.info("生产领料单【{}】出库完成", issue.getIssueCode());
            return true;
        } catch (Exception e) {
            log.error("生产领料单【{}】出库失败：{}", issue.getIssueCode(), e.getMessage(), e);
            throw new ServiceException("完成领料出库失败：" + e.getMessage());
        }
    }

    /**
     * 取消生产领料单
     *
     * @param issueId 领料单ID
     * @param reason  取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelIssue(Long issueId, String reason) {
        ProductionIssue issue = baseMapper.selectById(issueId);
        if (issue == null) {
            throw new ServiceException("领料单不存在");
        }

        // 校验状态，只有草稿和待出库状态可以取消
        if (issue.getIssueStatus() != ProductionIssueStatus.DRAFT &&
            issue.getIssueStatus() != ProductionIssueStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("领料单【" + issue.getIssueCode() + "】状态为【" +
                issue.getIssueStatus() + "】，不允许取消");
        }

        // 如果是从待出库状态取消，需要释放库存预留
        if (issue.getIssueStatus() == ProductionIssueStatus.PENDING_WAREHOUSE) {
            releaseInventoryReservation(issue);
        }

        // 更新状态为草稿，并记录取消原因
        issue.setIssueStatus(ProductionIssueStatus.DRAFT);
        if (StringUtils.isNotBlank(reason)) {
            issue.setRemark(StringUtils.isBlank(issue.getRemark()) ?
                "取消原因：" + reason : issue.getRemark() + "；取消原因：" + reason);
        }
        boolean result = baseMapper.updateById(issue) > 0;

        if (result) {
            log.info("生产领料单【{}】取消成功，原因：{}", issue.getIssueCode(), reason);
        }

        return result;
    }

    /**
     * 根据生产订单创建领料单
     *
     * @param productionOrderId 生产订单ID
     * @return 创建的领料单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductionIssueVo createFromProductionOrder(Long productionOrderId) {
        // 获取生产订单信息
        ProductionOrderVo order = productionOrderService.queryById(productionOrderId);
        if (order == null) {
            throw new ServiceException("生产订单不存在");
        }

        // 创建领料单
        ProductionIssue issue = new ProductionIssue();
        issue.setIssueCode(gen.code(MES_PRODUCTION_ISSUE_CODE));
        issue.setSourceId(order.getSourceId());
        issue.setSourceCode(order.getSourceCode());
        issue.setSourceType(order.getSourceType());

        issue.setDirectSourceId(order.getOrderId());
        issue.setDirectSourceCode(order.getOrderCode());
        issue.setDirectSourceType(DirectSourceType.PRODUCTION_ORDER);

        issue.setIssueTime(LocalDateTime.now());
        issue.setIssueStatus(ProductionIssueStatus.DRAFT);
        issue.setSummary("基于生产订单【" + order.getOrderCode() + "】创建");

        // 插入领料单
        boolean flag = baseMapper.insert(issue) > 0;
        if (!flag) {
            throw new ServiceException("创建领料单失败");
        }

        // 根据产品BOM创建领料明细
        createIssueItemsFromBOM(issue, order);

        log.info("基于生产订单【{}】创建领料单【{}】成功", order.getOrderCode(), issue.getIssueCode());

        return queryById(issue.getIssueId());
    }

    /**
     * 处理库存扣减逻辑
     *
     * @param issue 生产领料单
     */
    private void processInventoryDeduction(ProductionIssue issue) {
        try {
            // 获取生产领料明细
            ProductionIssueItemBo queryBo = new ProductionIssueItemBo();
            queryBo.setIssueId(issue.getIssueId());
            List<ProductionIssueItemVo> items = itemService.queryList(queryBo);

            if (items.isEmpty()) {
                log.warn("生产领料单【{}】没有明细，跳过库存扣减", issue.getIssueCode());
                return;
            }

            // 基于生产领料明细进行库存扣减（遵循领料单→明细→批次的标准结构）
            for (ProductionIssueItemVo item : items) {
                // TODO: 调用WMS模块的库存扣减服务（基于明细的产品和库位）
                // boolean deductResult = inventoryService.adjustInventory(
                //     item.getProductId(),
                //     item.getLocationId(),
                //     item.getQuantity().negate(), // 负数表示扣减
                //     "生产领料扣减 - 领料单：" + issue.getIssueCode() + " 明细：" + item.getItemId(),
                //     issue.getCreateBy(), // 使用创建人作为操作人
                //     issue.getCreateByName() // 使用创建人姓名
                // );
                boolean deductResult = true; // 临时实现

                if (!deductResult) {
                    throw new ServiceException("产品【" + item.getProductName() + "】库存扣减失败");
                }

                log.info("生产领料库存扣减成功：产品【{}】数量【{}】库位【{}】",
                    item.getProductName(), item.getQuantity(), item.getLocationCode());

                // 处理生产领料明细的批次扣减（遵循明细→批次的标准结构，FIFO原则）
                if (item.getProductId() != null) {
                    processProductionIssueItemBatches(item, issue);
                }
            }

            log.info("生产领料单【{}】库存扣减处理完成", issue.getIssueCode());
        } catch (Exception e) {
            log.error("生产领料单【{}】库存扣减失败：{}", issue.getIssueCode(), e.getMessage(), e);
            throw new ServiceException("库存扣减失败：" + e.getMessage());
        }
    }

    /**
     * 处理生产领料明细的批次扣减（遵循明细→批次的标准结构，FIFO原则）
     *
     * @param item  领料明细
     * @param issue 领料单
     */
    private void processProductionIssueItemBatches(ProductionIssueItemVo item, ProductionIssue issue) {
        try {
            // 获取该产品在该库位的所有可用批次（按先进先出排序）
            List<InventoryVo> availableBatches = inventoryService.getExpiringBatches(
                365, item.getProductId(), item.getLocationId());

            BigDecimal remainingQty = item.getQuantity();

            for (InventoryVo batch : availableBatches) {
                if (remainingQty.compareTo(BigDecimal.ZERO) <= 0) {
                    break; // 已扣减完毕
                }

                // 检查批次可用数量
                // TODO: Inventory实体中没有allocatedQuantity字段，需要重新设计库存分配逻辑
                // 暂时使用全部数量作为可用数量，待实体完善后修正
                BigDecimal batchAvailableQty = batch.getQuantity();
                // 原逻辑（待实体完善后启用）：
                // BigDecimal batchAvailableQty = batch.getQuantity().subtract(
                //     batch.getAllocatedQuantity() != null ? batch.getAllocatedQuantity() : BigDecimal.ZERO);

                if (batchAvailableQty.compareTo(BigDecimal.ZERO) <= 0) {
                    continue; // 该批次无可用库存
                }

                // 计算本批次扣减数量
                BigDecimal deductQty = remainingQty.min(batchAvailableQty);

                // 记录生产用料追溯信息
                recordProductionUsage(issue, item, batch, deductQty);

                remainingQty = remainingQty.subtract(deductQty);

                log.info("生产领料批次扣减：批次【{}】扣减数量【{}】剩余需扣减【{}】",
                    batch.getInternalBatchNumber(), deductQty, remainingQty);
            }

            if (remainingQty.compareTo(BigDecimal.ZERO) > 0) {
                log.warn("产品【{}】批次库存不足，未扣减数量：{}", item.getProductName(), remainingQty);
            }
        } catch (Exception e) {
            log.error("生产领料批次扣减失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 记录生产用料追溯信息
     *
     * @param issue    领料单
     * @param item     领料明细
     * @param batch    库存
     * @param usageQty 使用数量
     */
    private void recordProductionUsage(ProductionIssue issue, ProductionIssueItemVo item,
                                       InventoryVo batch, BigDecimal usageQty) {
        try {
            // TODO: 创建生产用料追溯记录
            // ProInstanceUsage usage = new ProInstanceUsage();
            // usage.setInstanceId(issue.getInstanceId()); // 产品实例ID
            // usage.setProductId(item.getProductId());
            // usage.setBatchId(batch.getBatchId());
            // usage.setUsageQuantity(usageQty);
            // usage.setUsageTime(LocalDateTime.now());
            // usage.setUsageType("PRODUCTION_ISSUE");
            // usage.setSourceId(issue.getIssueId());
            // usage.setSourceCode(issue.getIssueCode());
            // proInstanceUsageService.insert(usage);

            log.debug("记录生产用料追溯：领料单【{}】产品【{}】批次【{}】用量【{}】",
                issue.getIssueCode(), item.getProductName(), batch.getInternalBatchNumber(), usageQty);
        } catch (Exception e) {
            log.error("记录生产用料追溯失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 根据产品BOM创建领料明细
     *
     * @param issue 生产领料单
     * @param order 生产订单
     */
    private void createIssueItemsFromBOM(ProductionIssue issue, ProductionOrderVo order) {
        try {
            // 检查生产订单是否有产品信息
            if (order.getProductId() == null) {
                log.warn("生产订单【{}】缺少产品信息，无法创建领料明细", order.getOrderCode());
                return;
            }

            // TODO: 集成BOM模块，获取产品的BOM信息
            // 当前简化实现：基于产品信息创建基础的领料明细
            // 实际应该调用：List<BomItem> bomItems = bomService.getByProductId(order.getProductId());

            // 简化实现：为主要产品创建一个基础的领料明细
            List<ProductionIssueItemBo> items = new ArrayList<>();

            // 创建主要产品的领料明细（简化逻辑）
            ProductionIssueItemBo mainItem = new ProductionIssueItemBo();
            mainItem.setIssueId(issue.getIssueId());
            mainItem.setProductId(order.getProductId());
            mainItem.setProductCode(order.getProductCode());
            mainItem.setProductName(order.getProductName());

            // 计算领料数量：生产数量 * BOM用量（这里简化为1:1）
            BigDecimal issueQuantity = order.getQuantity() != null ? order.getQuantity() : BigDecimal.ONE;
            mainItem.setQuantity(issueQuantity);

            // 设置默认库位（TODO: 应该从BOM或产品主数据获取）
            // mainItem.setLocationId(getDefaultLocationId(order.getProductId()));

            // 获取产品成本价格
            BigDecimal unitPrice = getProductCostPrice(order.getProductId());
            if (unitPrice != null) {
                BigDecimal totalAmount = unitPrice.multiply(issueQuantity).setScale(2, RoundingMode.HALF_UP);
                mainItem.setAmount(totalAmount);
            }

            items.add(mainItem);

            // 填充明细的冗余字段
            fillItemsData(items, issue.getIssueId());

            // 批量插入明细
            if (!items.isEmpty()) {
                itemService.insertBatch(items);

                log.info("基于生产订单【{}】创建领料明细{}条", order.getOrderCode(), items.size());
            }

        } catch (Exception e) {
            log.error("创建领料明细失败：{}", e.getMessage(), e);
            // 创建失败不影响主流程，只记录日志
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(ProductionIssueBo bo) {
        // 填充生产订单信息
//        if (bo.getOrderId() != null) {
//            ProductionOrderVo productionOrder = productionOrderService.queryById(bo.getOrderId());
//            if (productionOrder != null) {
//                bo.setOrderCode(productionOrder.getOrderCode());
//            }
//        }
    }

    /**
     * 填充责任人信息
     */
    private void fillResponsiblePersonInfo(ProductionIssueBo bo) {
        Long currentUserId = LoginHelper.getUserId();
        String currentUserName = LoginHelper.getUsername();

        // TODO 如果是新增，设置申请人
        if (bo.getIssueId() == null) {
            //bo.setApplicantId(currentUserId);
            //bo.setApplicantName(currentUserName);
        }

        // TODO 设置经办人（每次更新都更新）
        //bo.setHandlerId(currentUserId);
        //bo.setHandlerName(currentUserName);
    }

    /**
     * 填充明细数据
     */
    private void fillItemsData(List<ProductionIssueItemBo> items, Long issueId) {
        for (ProductionIssueItemBo item : items) {
            item.setIssueId(issueId);

            // 填充产品信息
            if (item.getProductId() != null) {
                ProductVo product = productService.queryById(item.getProductId());
                if (product != null) {
                    item.setProductCode(product.getProductCode());
                    item.setProductName(product.getProductName());
                    item.setUnitId(product.getUnitId());
                    item.setUnitCode(product.getUnitCode());
                    item.setUnitName(product.getUnitName());
                }
            }

            // 填充库位信息
            if (item.getLocationId() != null) {
                LocationVo location = locationService.queryById(item.getLocationId());
                if (location != null) {
                    item.setLocationCode(location.getLocationCode());
                    item.setLocationName(location.getLocationName());
                }
            }

            // 生成批次信息（如果需要）
        }
    }

    /**
     * 计算明细金额
     */
    private void calculateItemAmounts(List<ProductionIssueItemBo> items) {
        for (ProductionIssueItemBo item : items) {
            // 获取产品成本价格
            BigDecimal unitPrice = getProductCostPrice(item.getProductId());
            if (unitPrice != null && item.getQuantity() != null) {
                // 计算总金额
                BigDecimal totalAmount = unitPrice.multiply(item.getQuantity())
                    .setScale(2, RoundingMode.HALF_UP);
                item.setAmount(totalAmount);
            }
        }
    }

    /**
     * 校验状态流转合法性
     */
    private boolean isValidStatusTransition(ProductionIssueStatus fromStatus, ProductionIssueStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return true;
        }

        // 定义合法的状态流转
        switch (fromStatus) {
            case DRAFT:
                return toStatus == ProductionIssueStatus.PENDING_WAREHOUSE ||
                    toStatus == ProductionIssueStatus.DRAFT;
            case PENDING_WAREHOUSE:
                return toStatus == ProductionIssueStatus.COMPLETED ||
                    toStatus == ProductionIssueStatus.DRAFT;
            case COMPLETED:
                return toStatus == ProductionIssueStatus.COMPLETED; // 只能保持完成状态
            default:
                return false;
        }
    }

    /**
     * 生成批次号
     */
    private String generateBatchCode(String productCode) {
        // 简单的批次号生成逻辑：产品编码 + 时间戳
        return productCode + "_" + System.currentTimeMillis();
    }

    /**
     * TODO 获取产品成本价格 产品缺少成本价字段
     */
    private BigDecimal getProductCostPrice(Long productId) {
        if (productId == null) {
            return BigDecimal.ZERO;
        }

        /*ProductVo product = productService.queryById(productId);
        if (product != null && product.getCostPrice() != null) {
            return product.getCostPrice();
        }*/

        // 如果没有成本价格，返回0
        return BigDecimal.ZERO;
    }

    /**
     * 处理库存预留
     *
     * @param issue 生产领料单
     */
    private void processInventoryReservation(ProductionIssue issue) {
        try {
            // 获取生产领料明细
            ProductionIssueItemBo queryBo = new ProductionIssueItemBo();
            queryBo.setIssueId(issue.getIssueId());
            List<ProductionIssueItemVo> items = itemService.queryList(queryBo);

            if (items.isEmpty()) {
                log.warn("生产领料单【{}】没有明细，跳过库存预留", issue.getIssueCode());
                return;
            }

            // 检查每个明细的库存可用性并进行预留
            for (ProductionIssueItemVo item : items) {
                // 检查库存可用性
                boolean isAvailable = inventoryService.checkInventoryAvailability(item.getProductId(), item.getLocationId(), item.getQuantity());

                if (!isAvailable) {
                    throw new ServiceException(String.format(
                        "产品【%s】在库位【%s】的可用库存不足，需要数量：%s",
                        item.getProductName(), item.getLocationCode(), item.getQuantity()
                    ));
                }

                // 使用现有字段实现库存预留
                // TODO: 这里使用finishQuantity字段临时存储预留数量
                // 实际应该有专门的预留字段，但按约束不能新增字段
                reserveInventoryForItem(item, issue);

                log.info("库存预留成功：产品【{}】数量【{}】库位【{}】",
                    item.getProductName(), item.getQuantity(), item.getLocationCode());
            }

            log.info("生产领料单【{}】库存预留处理完成", issue.getIssueCode());
        } catch (Exception e) {
            log.error("生产领料单【{}】库存预留失败：{}", issue.getIssueCode(), e.getMessage(), e);
            throw new ServiceException("库存预留失败：" + e.getMessage());
        }
    }

    /**
     * 为明细项预留库存
     *
     * @param item  领料明细
     * @param issue 领料单
     */
    private void reserveInventoryForItem(ProductionIssueItemVo item, ProductionIssue issue) {
        try {
            // TODO: 调用WMS模块的库存预留服务
            // 当前简化实现：使用现有字段记录预留信息
            // boolean reserveResult = inventoryService.reserveInventory(
            //     item.getProductId(),
            //     item.getLocationId(),
            //     item.getQuantity(),
            //     "生产领料预留 - 领料单：" + issue.getIssueCode(),
            //     issue.getCreateBy()
            // );

            // 临时实现：记录预留日志
            log.debug("预留库存：产品【{}】数量【{}】库位【{}】领料单【{}】",
                item.getProductName(), item.getQuantity(), item.getLocationCode(), issue.getIssueCode());

        } catch (Exception e) {
            log.error("预留库存失败：{}", e.getMessage(), e);
            throw new ServiceException("预留库存失败：" + e.getMessage());
        }
    }

    /**
     * 释放库存预留
     *
     * @param issue 生产领料单
     */
    private void releaseInventoryReservation(ProductionIssue issue) {
        try {
            // 获取生产领料明细
            ProductionIssueItemBo queryBo = new ProductionIssueItemBo();
            queryBo.setIssueId(issue.getIssueId());
            List<ProductionIssueItemVo> items = itemService.queryList(queryBo);

            if (items.isEmpty()) {
                log.warn("生产领料单【{}】没有明细，跳过库存预留释放", issue.getIssueCode());
                return;
            }

            // 释放每个明细的库存预留
            for (ProductionIssueItemVo item : items) {
                // TODO: 调用WMS模块的库存预留释放服务
                // boolean releaseResult = inventoryService.releaseReservation(
                //     item.getProductId(),
                //     item.getLocationId(),
                //     item.getQuantity(),
                //     "生产领料取消预留 - 领料单：" + issue.getIssueCode(),
                //     issue.getUpdateBy()
                // );

                // 临时实现：记录释放日志
                log.info("释放库存预留：产品【{}】数量【{}】库位【{}】",
                    item.getProductName(), item.getQuantity(), item.getLocationCode());
            }

            log.info("生产领料单【{}】库存预留释放完成", issue.getIssueCode());
        } catch (Exception e) {
            log.error("生产领料单【{}】库存预留释放失败：{}", issue.getIssueCode(), e.getMessage(), e);
            // 预留释放失败不影响主流程，只记录日志
        }
    }
}
