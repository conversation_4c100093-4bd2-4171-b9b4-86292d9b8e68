package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.mes.domain.ProductionReturnItem;
import com.iotlaser.spms.mes.domain.bo.ProductionReturnItemBo;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnItemVo;
import com.iotlaser.spms.mes.mapper.ProductionReturnItemMapper;
import com.iotlaser.spms.mes.service.IProductionReturnItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 生产退料明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionReturnItemServiceImpl implements IProductionReturnItemService {

    private final ProductionReturnItemMapper baseMapper;
    private final ILocationService locationService;

    /**
     * 查询生产退料明细
     *
     * @param itemId 主键
     * @return 生产退料明细
     */
    @Override
    public ProductionReturnItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询生产退料明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产退料明细分页列表
     */
    @Override
    public TableDataInfo<ProductionReturnItemVo> queryPageList(ProductionReturnItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionReturnItem> lqw = buildQueryWrapper(bo);
        Page<ProductionReturnItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产退料明细列表
     *
     * @param bo 查询条件
     * @return 生产退料明细列表
     */
    @Override
    public List<ProductionReturnItemVo> queryList(ProductionReturnItemBo bo) {
        LambdaQueryWrapper<ProductionReturnItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionReturnItem> buildQueryWrapper(ProductionReturnItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionReturnItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionReturnItem::getItemId);
        lqw.eq(bo.getReturnId() != null, ProductionReturnItem::getReturnId, bo.getReturnId());
        lqw.eq(bo.getProductId() != null, ProductionReturnItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), ProductionReturnItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), ProductionReturnItem::getProductName, bo.getProductName());
        lqw.eq(bo.getQuantity() != null, ProductionReturnItem::getQuantity, bo.getQuantity());
        lqw.eq(bo.getFinishQuantity() != null, ProductionReturnItem::getFinishQuantity, bo.getFinishQuantity());
        lqw.eq(bo.getLocationId() != null, ProductionReturnItem::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), ProductionReturnItem::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), ProductionReturnItem::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionReturnItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增生产退料明细
     *
     * @param bo 生产退料明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProductionReturnItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        ProductionReturnItem add = MapstructUtils.convert(bo, ProductionReturnItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setItemId(add.getItemId());
        }
        return flag;
    }

    /**
     * 修改生产退料明细
     *
     * @param bo 生产退料明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProductionReturnItemBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        ProductionReturnItem update = MapstructUtils.convert(bo, ProductionReturnItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionReturnItem entity) {
        // 校验必填字段
        if (entity.getReturnId() == null) {
            throw new ServiceException("退料单不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("产品不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("退料数量必须大于0");
        }
        if (entity.getLocationId() == null) {
            throw new ServiceException("库位不能为空");
        }

        // 校验同一退料单中产品不能重复
        if (entity.getReturnId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<ProductionReturnItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductionReturnItem::getReturnId, entity.getReturnId());
            wrapper.eq(ProductionReturnItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(ProductionReturnItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一生产退料单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除生产退料明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验明细是否可以删除
            List<ProductionReturnItem> items = baseMapper.selectByIds(ids);
            for (ProductionReturnItem item : items) {
                log.info("删除生产退料明细，产品：{}", item.getProductName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据退料单ID查询明细ID列表
     *
     * @param returnId 退料单ID
     * @return 明细ID列表
     */
    @Override
    public List<Long> selectItemIdsByReturnId(Long returnId) {
        LambdaQueryWrapper<ProductionReturnItem> wrapper = Wrappers.lambdaQuery();
        wrapper.select(ProductionReturnItem::getItemId);
        wrapper.eq(ProductionReturnItem::getReturnId, returnId);
        return baseMapper.selectList(wrapper).stream()
            .map(ProductionReturnItem::getItemId)
            .collect(Collectors.toList());
    }

    /**
     * 批量插入或更新生产退料明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertOrUpdateBatch(List<ProductionReturnItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<ProductionReturnItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(bo, ProductionReturnItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新生产退料明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新生产退料明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID集合删除生产退料明细
     *
     * @param ids ID集合
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(Collection<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return true;
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除生产退料明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除生产退料明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(ProductionReturnItemBo bo) {
        // 填充位置信息
        if (bo.getLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getLocationId());
            if (vo != null) {
                bo.setLocationCode(vo.getLocationCode());
                bo.setLocationName(vo.getLocationName());
            }
        }
    }

}
