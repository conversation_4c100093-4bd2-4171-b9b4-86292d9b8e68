package com.iotlaser.spms.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.mes.domain.ProductionReturn;
import com.iotlaser.spms.mes.domain.bo.ProductionReturnBo;
import com.iotlaser.spms.mes.domain.vo.ProductionIssueVo;
import com.iotlaser.spms.mes.domain.vo.ProductionReturnVo;
import com.iotlaser.spms.mes.enums.ProductionReturnStatus;
import com.iotlaser.spms.mes.mapper.ProductionReturnItemMapper;
import com.iotlaser.spms.mes.mapper.ProductionReturnMapper;
import com.iotlaser.spms.mes.service.IProductionIssueService;
import com.iotlaser.spms.mes.service.IProductionReturnItemService;
import com.iotlaser.spms.mes.service.IProductionReturnService;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.MES_PRODUCTION_RETURN_CODE;

/**
 * 生产退料Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductionReturnServiceImpl implements IProductionReturnService {

    private final ProductionReturnMapper baseMapper;
    private final ProductionReturnItemMapper itemMapper;
    private final IProductionIssueService productionIssueService;
    private final IProductionReturnItemService itemService;
    private final Gen gen;

    /**
     * 查询生产退料
     *
     * @param returnId 主键
     * @return 生产退料
     */
    @Override
    public ProductionReturnVo queryById(Long returnId) {
        return baseMapper.selectVoById(returnId);
    }

    /**
     * 分页查询生产退料列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 生产退料分页列表
     */
    @Override
    public TableDataInfo<ProductionReturnVo> queryPageList(ProductionReturnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProductionReturn> lqw = buildQueryWrapper(bo);
        Page<ProductionReturnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的生产退料列表
     *
     * @param bo 查询条件
     * @return 生产退料列表
     */
    @Override
    public List<ProductionReturnVo> queryList(ProductionReturnBo bo) {
        LambdaQueryWrapper<ProductionReturn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductionReturn> buildQueryWrapper(ProductionReturnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductionReturn> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductionReturn::getReturnId);
        lqw.eq(StringUtils.isNotBlank(bo.getReturnCode()), ProductionReturn::getReturnCode, bo.getReturnCode());
        lqw.eq(bo.getSourceId() != null, ProductionReturn::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), ProductionReturn::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(ProductionReturn::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getDirectSourceId() != null, ProductionReturn::getDirectSourceId, bo.getDirectSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getDirectSourceCode()), ProductionReturn::getDirectSourceCode, bo.getDirectSourceCode());
        if (bo.getDirectSourceType() != null) {
            lqw.eq(ProductionReturn::getDirectSourceType, bo.getDirectSourceType());
        }
        lqw.eq(bo.getReturnTime() != null, ProductionReturn::getReturnTime, bo.getReturnTime());
        if (bo.getReturnStatus() != null) {
            lqw.eq(ProductionReturn::getReturnStatus, bo.getReturnStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductionReturn::getStatus, bo.getStatus());
        lqw.between(params.get("beginReturnTime") != null && params.get("endReturnTime") != null,
            ProductionReturn::getReturnTime, params.get("beginReturnTime"), params.get("endReturnTime"));
        return lqw;
    }

    /**
     * 新增生产退料
     *
     * @param bo 生产退料
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductionReturnVo insertByBo(ProductionReturnBo bo) {
        try {
            if (StringUtils.isEmpty(bo.getReturnCode())) {
                bo.setReturnCode(gen.code(MES_PRODUCTION_RETURN_CODE));
            }
            if (bo.getReturnStatus() == null) {
                bo.setReturnStatus(ProductionReturnStatus.DRAFT);
            }
            if (bo.getReturnTime() == null) {
                bo.setReturnTime(LocalDateTime.now());
            }
            ProductionReturn add = MapstructUtils.convert(bo, ProductionReturn.class);
            validEntityBeforeSave(add);
            // 插入数据库
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("生产退料单创建失败");
            }

            log.info("生产退料单【{}】创建成功", add.getReturnCode());
            return MapstructUtils.convert(add, ProductionReturnVo.class);
        } catch (Exception e) {
            log.error("生产退料单创建失败：{}", e.getMessage(), e);
            throw new ServiceException("生产退料单创建失败：" + e.getMessage());
        }
    }

    /**
     * 修改生产退料
     *
     * @param bo 生产退料
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductionReturnVo updateByBo(ProductionReturnBo bo) {
        try {
            // 将传入的BO对象转换为实体类对象
            ProductionReturn update = MapstructUtils.convert(bo, ProductionReturn.class);
            // 在保存前验证实体是否有效
            validEntityBeforeSave(update);
            // 更新生产退料记录，判断更新是否成功
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("生产退料记录更新失败");
            }
            log.info("生产退库单【{}】更新成功", update.getReturnCode());
            return MapstructUtils.convert(update, ProductionReturnVo.class);
        } catch (Exception e) {
            log.error("生产退库单更新失败：{}", e.getMessage(), e);
            throw new ServiceException("生产退库单更新失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductionReturn entity) {
        // 校验退料单编号唯一性
        if (StringUtils.isNotBlank(entity.getReturnCode())) {
            LambdaQueryWrapper<ProductionReturn> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductionReturn::getReturnCode, entity.getReturnCode());
            if (entity.getReturnId() != null) {
                wrapper.ne(ProductionReturn::getReturnId, entity.getReturnId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("退料单编号已存在：" + entity.getReturnCode());
            }
        }
    }

    /**
     * 校验并批量删除生产退料信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验退料单状态，只有草稿状态的退料单才能删除
            List<ProductionReturn> returns = baseMapper.selectByIds(ids);
            for (ProductionReturn productionReturn : returns) {
                if (productionReturn.getReturnStatus() != ProductionReturnStatus.DRAFT) {
                    throw new ServiceException("退料单【" + productionReturn.getReturnCode() + "】状态为【" +
                        productionReturn.getReturnStatus() + "】，不允许删除");
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 确认生产退料单
     *
     * @param returnId 退料单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmReturn(Long returnId) {
        ProductionReturn productionReturn = baseMapper.selectById(returnId);
        if (productionReturn == null) {
            throw new ServiceException("退料单不存在");
        }

        // 校验状态
        if (productionReturn.getReturnStatus() != ProductionReturnStatus.DRAFT) {
            throw new ServiceException("退料单【" + productionReturn.getReturnCode() + "】状态为【" +
                productionReturn.getReturnStatus() + "】，不允许确认");
        }

        // 更新状态为待入库
        productionReturn.setReturnStatus(ProductionReturnStatus.PENDING_WAREHOUSE);
        boolean result = baseMapper.updateById(productionReturn) > 0;

        if (result) {
            log.info("生产退料单【{}】确认成功", productionReturn.getReturnCode());
        }

        return result;
    }

    /**
     * 批量确认生产退料单
     *
     * @param returnIds 退料单ID集合
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmReturns(Collection<Long> returnIds) {
        for (Long returnId : returnIds) {
            confirmReturn(returnId);
        }
        return true;
    }

    /**
     * 完成生产退料入库
     *
     * @param returnId 退料单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeReturn(Long returnId) {
        ProductionReturn productionReturn = baseMapper.selectById(returnId);
        if (productionReturn == null) {
            throw new ServiceException("退料单不存在");
        }

        // 校验状态
        if (productionReturn.getReturnStatus() != ProductionReturnStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("退料单【" + productionReturn.getReturnCode() + "】状态为【" +
                productionReturn.getReturnStatus() + "】，不允许完成入库");
        }

        // 更新状态为已入库
        productionReturn.setReturnStatus(ProductionReturnStatus.COMPLETED);
        boolean result = baseMapper.updateById(productionReturn) > 0;

        if (result) {
            log.info("生产退料单【{}】入库完成", productionReturn.getReturnCode());
            // 处理库存增加逻辑
            processInventoryIncrease(productionReturn);
        }

        return result;
    }

    /**
     * 取消生产退料单
     *
     * @param returnId 退料单ID
     * @param reason   取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelReturn(Long returnId, String reason) {
        ProductionReturn productionReturn = baseMapper.selectById(returnId);
        if (productionReturn == null) {
            throw new ServiceException("退料单不存在");
        }

        // 校验状态，只有草稿和待入库状态可以取消
        if (productionReturn.getReturnStatus() != ProductionReturnStatus.DRAFT &&
            productionReturn.getReturnStatus() != ProductionReturnStatus.PENDING_WAREHOUSE) {
            throw new ServiceException("退料单【" + productionReturn.getReturnCode() + "】状态为【" +
                productionReturn.getReturnStatus() + "】，不允许取消");
        }

        // 更新状态为已取消，并记录取消原因
        productionReturn.setReturnStatus(ProductionReturnStatus.CANCELLED);
        if (StringUtils.isNotBlank(reason)) {
            productionReturn.setRemark(StringUtils.isBlank(productionReturn.getRemark()) ?
                "取消原因：" + reason : productionReturn.getRemark() + "；取消原因：" + reason);
        }
        boolean result = baseMapper.updateById(productionReturn) > 0;

        if (result) {
            log.info("生产退料单【{}】取消成功，原因：{}", productionReturn.getReturnCode(), reason);
        }

        return result;
    }

    /**
     * 根据生产领料单创建退料单
     *
     * @param productionIssueId 生产领料单ID
     * @return 创建的退料单
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ProductionReturnVo createFromProductionIssue(Long productionIssueId) {
        // 获取生产领料单信息
        ProductionIssueVo issue = productionIssueService.queryById(productionIssueId);
        if (issue == null) {
            throw new ServiceException("生产领料单不存在");
        }

        // 创建退料单
        ProductionReturn productionReturn = new ProductionReturn();
        productionReturn.setReturnCode(gen.code(MES_PRODUCTION_RETURN_CODE));

        productionReturn.setSourceId(issue.getSourceId());
        productionReturn.setSourceCode(issue.getSourceCode());
        productionReturn.setSourceType(issue.getSourceType());

        productionReturn.setDirectSourceId(issue.getIssueId());
        productionReturn.setDirectSourceCode(issue.getIssueCode());
        productionReturn.setDirectSourceType(DirectSourceType.PRODUCTION_ISSUE);

        productionReturn.setReturnTime(LocalDateTime.now());
        productionReturn.setReturnStatus(ProductionReturnStatus.DRAFT);
        productionReturn.setSummary("基于生产领料单【" + issue.getIssueCode() + "】创建");

        // 插入退料单
        boolean flag = baseMapper.insert(productionReturn) > 0;
        if (!flag) {
            throw new ServiceException("创建退料单失败");
        }

        // 创建退料明细 TODO 需完善
        /*if (issue.getItems() != null && !issue.getItems().isEmpty()) {
            List<ProductionReturnItemBo> returnItems = issue.getItems().stream().map(issueItem -> {
                ProductionReturnItemBo returnItem = new ProductionReturnItemBo();
                returnItem.setReturnId(productionReturn.getReturnId());
                returnItem.setProductId(issueItem.getProductId());
                returnItem.setProductCode(issueItem.getProductCode());
                returnItem.setProductName(issueItem.getProductName());
                returnItem.setUnitId(issueItem.getUnitId());
                returnItem.setUnitCode(issueItem.getUnitCode());
                returnItem.setUnitName(issueItem.getUnitName());
                returnItem.setQuantity(issueItem.getQuantity());
                returnItem.setPrice(issueItem.getPrice());
                returnItem.setLocationId(issueItem.getLocationId());
                returnItem.setLocationCode(issueItem.getLocationCode());
                returnItem.setLocationName(issueItem.getLocationName());
                returnItem.setRemark("基于领料明细创建");
                return returnItem;
            }).collect(Collectors.toList());

            itemService.insertOrUpdateBatch(returnItems);
        }*/

        log.info("基于生产领料单【{}】创建退料单【{}】成功", issue.getIssueCode(), productionReturn.getReturnCode());

        return queryById(productionReturn.getReturnId());
    }

    /**
     * 处理库存增加逻辑
     *
     * @param productionReturn 生产退料单
     */
    private void processInventoryIncrease(ProductionReturn productionReturn) {
        try {
            // 获取退料明细
            log.info("生产退料库存增加：退料单【{}】", productionReturn.getReturnCode());

            // TODO: 实际项目中需要：
            // 获取生产退料明细
            // 遍历明细，调用库存服务增加库存
            // 记录库存变动日志
            // 处理批次库存增加
            //
            // 示例代码：
            // List<ProductionReturnItem> items = itemService.getByReturnId(productionReturn.getReturnId());
            // for (ProductionReturnItem item : items) {
            //     inventoryService.increaseInventory(item.getProductId(), item.getLocationId(), item.getQuantity());
            //     log.info("生产退料库存增加：产品【{}】数量【{}】", item.getProductName(), item.getQuantity());
            // }

            log.info("生产退料单【{}】库存增加处理完成", productionReturn.getReturnCode());
        } catch (Exception e) {
            log.error("生产退料单【{}】库存增加失败：{}", productionReturn.getReturnCode(), e.getMessage(), e);
            throw new ServiceException("库存增加失败：" + e.getMessage());
        }
    }
}
