package com.iotlaser.spms.pro.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.pro.domain.bo.BomItemBo;
import com.iotlaser.spms.pro.domain.vo.BomItemVo;
import com.iotlaser.spms.pro.service.IBomItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * BOM明细
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/pro/bomItem")
public class BomItemController extends BaseController {

    private final IBomItemService bomItemService;

    /**
     * 查询BOM明细列表
     */
    @SaCheckPermission("pro:bomItem:list")
    @GetMapping("/list")
    public TableDataInfo<BomItemVo> list(BomItemBo bo, PageQuery pageQuery) {
        return bomItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出BOM明细列表
     */
    @SaCheckPermission("pro:bomItem:export")
    @Log(title = "BOM明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BomItemBo bo, HttpServletResponse response) {
        List<BomItemVo> list = bomItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "BOM明细", BomItemVo.class, response);
    }

    /**
     * 获取BOM明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("pro:bomItem:query")
    @GetMapping("/{itemId}")
    public R<BomItemVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long itemId) {
        return R.ok(bomItemService.queryById(itemId));
    }

    /**
     * 新增BOM明细
     */
    @SaCheckPermission("pro:bomItem:add")
    @Log(title = "BOM明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BomItemBo bo) {
        return toAjax(bomItemService.insertByBo(bo));
    }

    /**
     * 新增BOM明细
     */
    @SaCheckPermission("pro:bomItem:add")
    @Log(title = "BOM明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("batchSave")
    public R<Void> batchSave(@Validated({AddGroup.class, EditGroup.class}) @RequestBody List<BomItemBo> bos) {
        return toAjax(bomItemService.batchSave(bos));
    }

    /**
     * 修改BOM明细
     */
    @SaCheckPermission("pro:bomItem:edit")
    @Log(title = "BOM明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BomItemBo bo) {
        return toAjax(bomItemService.updateByBo(bo));
    }

    /**
     * 删除BOM明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("pro:bomItem:remove")
    @Log(title = "BOM明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(bomItemService.deleteWithValidByIds(List.of(itemIds), true));
    }

    /**
     * 批量选择产品关联
     */
    @SaCheckPermission("pro:bomItem:add")
    @Log(title = "产品BOM清单", businessType = BusinessType.INSERT)
    @PutMapping("/allocatedSelect")
    public R<Void> allocatedSelect(Long bomId, Long[] productIds) {
        return toAjax(bomItemService.allocatedSelect(bomId, productIds));
    }

    /**
     * 查询未关联产品BOM清单列表
     */
    @SaCheckPermission("pro:bomItem:list")
    @GetMapping("/unallocatedList")
    public TableDataInfo<BomItemVo> unallocatedList(BomItemBo bo, PageQuery pageQuery) {
        return bomItemService.unallocatedList(bo, pageQuery);
    }


    /**
     * 获取BOM明细表以及关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pro:bomItem:query")
    @GetMapping("with/{id}")
    public R<BomItemVo> queryByIdWith(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(bomItemService.queryByIdWith(id));
    }

    /**
     * 查询BOM明细表列表以及关联详细信息
     */
    @SaCheckPermission("pro:bomItem:list")
    @GetMapping("with/list")
    public TableDataInfo<BomItemVo> queryPageListWith(BomItemBo bo, PageQuery pageQuery) {
        return bomItemService.queryPageListWith(bo, pageQuery);
    }


}
