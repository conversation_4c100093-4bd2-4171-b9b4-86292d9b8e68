package com.iotlaser.spms.pro.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.iotlaser.spms.base.domain.bo.MeasureUnitBo;
import com.iotlaser.spms.base.service.IMeasureUnitService;
import com.iotlaser.spms.pro.domain.bo.ProductBo;
import com.iotlaser.spms.pro.domain.bo.ProductCategoryBo;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.service.IProductCategoryService;
import com.iotlaser.spms.pro.service.IProductService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产品信息
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/pro/product")
public class ProductController extends BaseController {

    private final IProductService proProductService;
    private final IProductCategoryService proProductCategoryService;
    private final IMeasureUnitService baseMeasureUnitService;

    /**
     * 查询产品信息列表
     */
    @SaCheckPermission("pro:product:list")
    @GetMapping("/list")
    public TableDataInfo<ProductVo> list(ProductBo bo, PageQuery pageQuery) {
        return proProductService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品信息列表
     */
    @SaCheckPermission("pro:product:export")
    @Log(title = "产品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProductBo bo, HttpServletResponse response) {
        List<ProductVo> list = proProductService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品信息", ProductVo.class, response);
    }

    /**
     * 获取产品信息详细信息
     *
     * @param productId 主键
     */
    @SaCheckPermission("pro:product:query")
    @GetMapping("/{productId}")
    public R<ProductVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long productId) {
        return R.ok(proProductService.queryById(productId));
    }

    /**
     * 新增产品信息
     */
    @SaCheckPermission("pro:product:add")
    @Log(title = "产品信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProductBo bo) {
        return toAjax(proProductService.insertByBo(bo));
    }

    /**
     * 修改产品信息
     */
    @SaCheckPermission("pro:product:edit")
    @Log(title = "产品信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProductBo bo) {
        return toAjax(proProductService.updateByBo(bo));
    }

    /**
     * 删除产品信息
     *
     * @param productIds 主键串
     */
    @SaCheckPermission("pro:product:remove")
    @Log(title = "产品信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{productIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] productIds) {
        return toAjax(proProductService.deleteWithValidByIds(List.of(productIds), true));
    }

    /**
     * 获取分类树列表
     */
    @SaCheckPermission("base:product:list")
    @GetMapping("/productCategoryTree")
    public R<List<Tree<Long>>> productCategoryTree(ProductCategoryBo bo) {
        return R.ok(proProductCategoryService.selectTreeList(bo));
    }

    /**
     * 计算含税价格
     *
     * @param exclusiveTaxPrice 不含税价格
     * @param taxRate           税率
     */
    @SaCheckPermission("pro:product:query")
    @GetMapping("/calculateInclusiveTaxPrice")
    public R<BigDecimal> calculateInclusiveTaxPrice(
        @RequestParam BigDecimal exclusiveTaxPrice,
        @RequestParam BigDecimal taxRate) {
        BigDecimal result = proProductService.calculateInclusiveTaxPrice(exclusiveTaxPrice, taxRate);
        return R.ok(result);
    }

    /**
     * 计算不含税价格
     *
     * @param inclusiveTaxPrice 含税价格
     * @param taxRate           税率
     */
    @SaCheckPermission("pro:product:query")
    @GetMapping("/calculateExclusiveTaxPrice")
    public R<BigDecimal> calculateExclusiveTaxPrice(
        @RequestParam BigDecimal inclusiveTaxPrice,
        @RequestParam BigDecimal taxRate) {
        BigDecimal result = proProductService.calculateExclusiveTaxPrice(inclusiveTaxPrice, taxRate);
        return R.ok(result);
    }

    /**
     * 获取分类树列表
     */
    @SaCheckPermission("base:product:list")
    @GetMapping("/measureUnitTree")
    public R<List<Tree<Long>>> measureUnitTree(MeasureUnitBo bo) {
        return R.ok(baseMeasureUnitService.selectTreeList(bo));
    }
}
