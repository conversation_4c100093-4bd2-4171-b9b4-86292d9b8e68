package com.iotlaser.spms.pro.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.pro.enums.InstanceStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 产品实例对象 pro_instance
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pro_instance")
public class Instance extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品实例ID
     */
    @TableId(value = "instance_id")
    private Long instanceId;

    /**
     * 产品实例编码
     */
    private String instanceCode;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 销售订单ID
     */
    private Long saleOrderId;

    /**
     * 销售订单编码
     */
    private String saleOrderCode;

    /**
     * 生产订单ID
     */
    private Long productionOrderId;

    /**
     * 生产订单编码
     */
    private String productionOrderCode;

    /**
     * BOMID
     */
    private Long bomId;

    /**
     * BOM编码
     */
    private String bomCode;

    /**
     * BOM名称
     */
    private String bomName;

    /**
     * 生产时间
     */
    private LocalDateTime productionTime;

    /**
     * 实例状态
     */
    private InstanceStatus instanceStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}
