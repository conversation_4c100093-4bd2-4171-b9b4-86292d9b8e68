package com.iotlaser.spms.pro.domain.bo;

import com.iotlaser.spms.pro.domain.BomItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * BOM明细业务对象 pro_bom_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = BomItem.class, reverseConvertGenerate = false)
public class BomItemBo extends BaseEntity {

    /**
     * BOM明细ID
     */
    @NotNull(message = "BOM明细ID不能为空", groups = {EditGroup.class})
    private Long itemId;

    /**
     * BOMID
     */
    @NotNull(message = "BOMID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long bomId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品规格
     */
    private String productSpecs;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品分类ID
     */
    @NotNull(message = "产品分类ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long categoryId;

    /**
     * 产品分类编码
     */
    private String categoryCode;

    /**
     * 产品分类名称
     */
    private String categoryName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 待完成数量
     */
    @NotNull(message = "待完成数量不能为空", groups = {EditGroup.class})
    private BigDecimal quantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 排除产品ID
     */
    private String excludeProductIds;
}
