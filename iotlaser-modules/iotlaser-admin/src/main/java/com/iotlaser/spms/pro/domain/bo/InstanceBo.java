package com.iotlaser.spms.pro.domain.bo;

import com.iotlaser.spms.pro.domain.Instance;
import com.iotlaser.spms.pro.enums.InstanceStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 产品实例业务对象 pro_instance
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Instance.class, reverseConvertGenerate = false)
public class InstanceBo extends BaseEntity {

    /**
     * 产品实例ID
     */
    @NotNull(message = "产品实例ID不能为空", groups = {EditGroup.class})
    private Long instanceId;

    /**
     * 产品实例编码
     */
    private String instanceCode;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 销售订单ID
     */
    private Long saleOrderId;

    /**
     * 销售订单编码
     */
    private String saleOrderCode;

    /**
     * 生产订单ID
     */
    private Long productionOrderId;

    /**
     * 生产订单编码
     */
    private String productionOrderCode;

    /**
     * BOMID
     */
    @NotNull(message = "BOMID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long bomId;

    /**
     * BOM编码
     */
    private String bomCode;

    /**
     * BOM名称
     */
    private String bomName;

    /**
     * 生产时间
     */
    private LocalDateTime productionTime;

    /**
     * 实例状态
     */
    private InstanceStatus instanceStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

}
