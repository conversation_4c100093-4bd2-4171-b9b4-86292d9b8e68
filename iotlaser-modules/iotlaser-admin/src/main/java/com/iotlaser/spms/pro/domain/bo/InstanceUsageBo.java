package com.iotlaser.spms.pro.domain.bo;

import com.iotlaser.spms.pro.domain.InstanceUsage;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品实例组件使用业务对象 pro_instance_usage
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InstanceUsage.class, reverseConvertGenerate = false)
public class InstanceUsageBo extends BaseEntity {

    /**
     * 实例组件使用ID
     */
    @NotNull(message = "实例组件使用ID不能为空", groups = {EditGroup.class})
    private Long usageId;

    /**
     * 产品实例ID
     */
    @NotNull(message = "产品实例ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long instanceId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 待完成数量
     */
    @NotNull(message = "待完成数量不能为空", groups = {EditGroup.class})
    private BigDecimal quantity;

    /**
     * 使用时间
     */
    @NotNull(message = "使用时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private LocalDateTime usageTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

}
