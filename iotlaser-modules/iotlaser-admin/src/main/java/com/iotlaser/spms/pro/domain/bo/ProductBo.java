package com.iotlaser.spms.pro.domain.bo;

import com.iotlaser.spms.pro.domain.Product;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 产品信息业务对象 pro_product
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Product.class, reverseConvertGenerate = false)
public class ProductBo extends BaseEntity {

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    @NotBlank(message = "产品编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String productCode;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String productName;

    /**
     * 产品类型
     */
    @NotBlank(message = "产品类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String productType;

    /**
     * 产品规格
     */
    private String productSpecs;

    /**
     * 产品分类ID
     */
    private Long categoryId;

    /**
     * 产品分类编码
     */
    private String categoryCode;

    /**
     * 产品分类名称
     */
    private String categoryName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 编码规则ID
     */
    private Long codeRuleId;

    /**
     * 是否批次管理
     */
    private String batchFlag;

    /**
     * 批次管理策略
     */
    private String batchPolicy;

    /**
     * 保质天数
     */
    private Long shelfLifeDays;

    /**
     * 是否安全库存
     */
    private String safeStockFlag;

    /**
     * 是否高价值
     */
    private String highValueFlag;

    /**
     * 最低库存量
     */
    private BigDecimal minStock;

    /**
     * 最大库存量
     */
    private BigDecimal maxStock;

    /**
     * 参考采购价 (含税)
     */
    private BigDecimal purchasePrice;

    /**
     * 参考采购税率
     */
    private BigDecimal purchaseTaxRate;

    /**
     * 参考采购价 (不含税)
     */
    private BigDecimal purchasePriceExclusiveTax;

    /**
     * 标准生产成本
     */
    private BigDecimal standardCost;

    /**
     * 参考销售价 (含税)
     */
    private BigDecimal salePrice;

    /**
     * 参考销售税率
     */
    private BigDecimal saleTaxRate;

    /**
     * 参考销售价 (不含税)
     */
    private BigDecimal salePriceExclusiveTax;

    /**
     * 来料检验方案ID (关联qms_inspection_plan)
     */
    private Long iqcPlanId;

    /**
     * 完工检验方案ID (关联qms_inspection_plan)
     */
    private Long fqcPlanId;

    /**
     * 出货检验方案ID (关联qms_inspection_plan)
     */
    private Long oqcPlanId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 排除产品ID
     */
    private String excludeProductIds;
}
