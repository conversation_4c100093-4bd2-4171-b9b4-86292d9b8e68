package com.iotlaser.spms.pro.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.pro.domain.InstanceUsage;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品实例组件使用视图对象 pro_instance_usage
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InstanceUsage.class)
public class InstanceUsageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 实例组件使用ID
     */
    @ExcelProperty(value = "实例组件使用ID")
    private Long usageId;

    /**
     * 产品实例ID
     */
    @ExcelProperty(value = "产品实例ID")
    private Long instanceId;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 使用时间
     */
    @ExcelProperty(value = "使用时间")
    private LocalDateTime usageTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

}
