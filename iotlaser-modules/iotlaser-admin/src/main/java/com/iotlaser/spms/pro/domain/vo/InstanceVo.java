package com.iotlaser.spms.pro.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.pro.domain.Instance;
import com.iotlaser.spms.pro.enums.InstanceStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品实例视图对象 pro_instance
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Instance.class)
public class InstanceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品实例ID
     */
    @ExcelProperty(value = "产品实例ID")
    private Long instanceId;

    /**
     * 产品实例编码
     */
    @ExcelProperty(value = "产品实例编码")
    private String instanceCode;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 销售订单ID
     */
    @ExcelProperty(value = "销售订单ID")
    private Long saleOrderId;

    /**
     * 销售订单编码
     */
    @ExcelProperty(value = "销售订单编码")
    private String saleOrderCode;

    /**
     * 生产订单ID
     */
    @ExcelProperty(value = "生产订单ID")
    private Long productionOrderId;

    /**
     * 生产订单编码
     */
    @ExcelProperty(value = "生产订单编码")
    private String productionOrderCode;

    /**
     * BOMID
     */
    @ExcelProperty(value = "BOMID")
    private Long bomId;

    /**
     * BOM编码
     */
    @ExcelProperty(value = "BOM编码")
    private String bomCode;

    /**
     * BOM名称
     */
    @ExcelProperty(value = "BOM名称")
    private String bomName;

    /**
     * 生产时间
     */
    @ExcelProperty(value = "生产时间")
    private LocalDateTime productionTime;

    /**
     * 实例状态
     */
    @ExcelProperty(value = "实例状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wf_business_status")
    private InstanceStatus instanceStatus;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

}
