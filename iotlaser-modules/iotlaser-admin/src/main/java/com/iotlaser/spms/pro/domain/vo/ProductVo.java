package com.iotlaser.spms.pro.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.pro.domain.Product;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 产品信息视图对象 pro_product
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Product.class)
public class ProductVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品类型
     */
    @ExcelProperty(value = "产品类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_product_type")
    private String productType;

    /**
     * 产品规格
     */
    @ExcelProperty(value = "产品规格")
    private String productSpecs;

    /**
     * 产品分类ID
     */
    @ExcelProperty(value = "产品分类ID")
    private Long categoryId;

    /**
     * 产品分类编码
     */
    @ExcelProperty(value = "产品分类编码")
    private String categoryCode;

    /**
     * 产品分类名称
     */
    @ExcelProperty(value = "产品分类名称")
    private String categoryName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 编码规则ID
     */
    @ExcelProperty(value = "编码规则ID")
    private Long codeRuleId;

    /**
     * 是否批次管理
     */
    @ExcelProperty(value = "是否批次管理")
    private String batchFlag;

    /**
     * 批次管理策略
     */
    @ExcelProperty(value = "批次管理策略")
    private String batchPolicy;

    /**
     * 保质天数
     */
    @ExcelProperty(value = "保质天数")
    private Long shelfLifeDays;

    /**
     * 是否安全库存
     */
    @ExcelProperty(value = "是否安全库存", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String safeStockFlag;

    /**
     * 是否高价值
     */
    @ExcelProperty(value = "是否高价值", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String highValueFlag;

    /**
     * 最低库存量
     */
    @ExcelProperty(value = "最低库存量")
    private BigDecimal minStock;

    /**
     * 最大库存量
     */
    @ExcelProperty(value = "最大库存量")
    private BigDecimal maxStock;

    /**
     * 参考采购价 (含税)
     */
    @ExcelProperty(value = "参考采购价 (含税)")
    private BigDecimal purchasePrice;

    /**
     * 参考采购税率
     */
    @ExcelProperty(value = "参考采购税率")
    private BigDecimal purchaseTaxRate;

    /**
     * 参考采购价 (不含税)
     */
    @ExcelProperty(value = "参考采购价 (不含税)")
    private BigDecimal purchasePriceExclusiveTax;

    /**
     * 标准生产成本
     */
    @ExcelProperty(value = "标准生产成本")
    private BigDecimal standardCost;

    /**
     * 参考销售价 (含税)
     */
    @ExcelProperty(value = "参考销售价 (含税)")
    private BigDecimal salePrice;

    /**
     * 参考销售税率
     */
    @ExcelProperty(value = "参考销售税率")
    private BigDecimal saleTaxRate;

    /**
     * 参考销售价 (不含税)
     */
    @ExcelProperty(value = "参考销售价 (不含税)")
    private BigDecimal salePriceExclusiveTax;

    /**
     * 来料检验方案ID (关联qms_inspection_plan)
     */
    @ExcelProperty(value = "来料检验方案ID (关联qms_inspection_plan)")
    private Long iqcPlanId;

    /**
     * 完工检验方案ID (关联qms_inspection_plan)
     */
    @ExcelProperty(value = "完工检验方案ID (关联qms_inspection_plan)")
    private Long fqcPlanId;

    /**
     * 出货检验方案ID (关联qms_inspection_plan)
     */
    @ExcelProperty(value = "出货检验方案ID (关联qms_inspection_plan)")
    private Long oqcPlanId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
