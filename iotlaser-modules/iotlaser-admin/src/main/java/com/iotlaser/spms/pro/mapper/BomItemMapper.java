package com.iotlaser.spms.pro.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.pro.domain.BomItem;
import com.iotlaser.spms.pro.domain.vo.BomItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * BOM明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface BomItemMapper extends BaseMapperPlus<BomItem, BomItemVo> {

    List<Long> selectProductIdsByBomId(Long productId);


    /**
     * 查询BOM明细表及其关联信息
     */
    BomItem queryByIdWith(@Param("itemId") Long itemId);

    /**
     * 分页查询BOM明细表及其关联信息
     */
    List<BomItem> queryPageListWith(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<BomItem> wrapper);


}
