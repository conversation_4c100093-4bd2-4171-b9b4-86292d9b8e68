package com.iotlaser.spms.pro.service;

import com.iotlaser.spms.pro.domain.bo.BomItemBo;
import com.iotlaser.spms.pro.domain.vo.BomItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * BOM明细Service接口
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
public interface IBomItemService {

    /**
     * 查询BOM明细
     *
     * @param itemId 主键
     * @return BOM明细
     */
    BomItemVo queryById(Long itemId);

    /**
     * 分页查询BOM明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return BOM明细分页列表
     */
    TableDataInfo<BomItemVo> queryPageList(BomItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的BOM明细列表
     *
     * @param bo 查询条件
     * @return BOM明细列表
     */
    List<BomItemVo> queryList(BomItemBo bo);

    /**
     * 新增BOM明细
     *
     * @param bo BOM明细
     * @return 是否新增成功
     */
    Boolean insertByBo(BomItemBo bo);

    /**
     * 批量新增更新BOM明细
     *
     * @param bos BOM明细
     * @return 是否新增成功
     */
    Boolean batchSave(List<BomItemBo> bos);

    /**
     * 修改BOM明细
     *
     * @param bo BOM明细
     * @return 是否修改成功
     */
    Boolean updateByBo(BomItemBo bo);

    /**
     * 校验并批量删除BOM明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量选择关联产品
     *
     * @param bomId      BOMID
     * @param productIds BOM需要关联的产品ID
     * @return 结果
     */
    int allocatedSelect(Long bomId, Long[] productIds);

    /**
     * 根据条件分页查询未已分配的产品列表
     *
     * @param bo 条件信息
     * @return 产品信息集合信息
     */
    TableDataInfo<BomItemVo> unallocatedList(BomItemBo bo, PageQuery pageQuery);


    /**
     * 查询BOM明细表及其关联信息
     *
     * @param itemId 主键
     * @return BOM明细表
     */
    BomItemVo queryByIdWith(Long itemId);

    /**
     * 分页查询BOM明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return BOM明细表分页列表
     */
    TableDataInfo<BomItemVo> queryPageListWith(BomItemBo bo, PageQuery pageQuery);


}
