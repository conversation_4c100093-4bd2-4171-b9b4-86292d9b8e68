package com.iotlaser.spms.pro.service.impl;

import com.iotlaser.spms.pro.service.IBomInventoryCheckService;
import com.iotlaser.spms.pro.service.IBomItemService;
import com.iotlaser.spms.pro.service.IBomService;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * BOM物料库存检查Service实现
 * <p>
 * 实现BOM物料库存检查、缺口分析、补料建议等功能
 *
 * <AUTHOR> <PERSON>
 * @date 2025/06/16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BomInventoryCheckServiceImpl implements IBomInventoryCheckService {

    private final IBomService bomService;
    private final IBomItemService itemService;
    private final IProductService productService;
    private final IInventoryService inventoryService;

    @Override
    public Map<String, Object> checkBomInventory(Long bomId, BigDecimal requiredQuantity) {
        try {
            Map<String, Object> checkResult = new HashMap<>();

            // TODO: 集成BOM和库存模块，实现完整的物料库存检查
            // 需要实现：BOM查询 -> 物料清单获取 -> 库存数量查询 -> 缺口计算 -> 结果汇总

            // 获取BOM物料详情列表
            List<Map<String, Object>> materialDetails = getBomMaterialInventoryDetails(bomId, requiredQuantity);

            // 统计检查结果
            int totalMaterials = materialDetails.size();
            int sufficientMaterials = 0;
            int insufficientMaterials = 0;
            BigDecimal totalShortageValue = BigDecimal.ZERO;

            for (Map<String, Object> material : materialDetails) {
                Boolean isSufficient = (Boolean) material.get("isSufficient");
                if (isSufficient) {
                    sufficientMaterials++;
                } else {
                    insufficientMaterials++;
                    BigDecimal shortageValue = (BigDecimal) material.getOrDefault("shortageValue", BigDecimal.ZERO);
                    totalShortageValue = totalShortageValue.add(shortageValue);
                }
            }

            // 构建检查结果
            checkResult.put("bomId", bomId);
            checkResult.put("requiredQuantity", requiredQuantity);
            checkResult.put("totalMaterials", totalMaterials);
            checkResult.put("sufficientMaterials", sufficientMaterials);
            checkResult.put("insufficientMaterials", insufficientMaterials);
            checkResult.put("totalShortageValue", totalShortageValue);
            checkResult.put("canStartProduction", insufficientMaterials == 0);
            checkResult.put("materialDetails", materialDetails);

            // 生成检查摘要
            String summary;
            if (insufficientMaterials == 0) {
                summary = String.format("物料充足，共%d种物料全部满足生产需求，可以开工", totalMaterials);
            } else {
                summary = String.format("物料不足，共%d种物料中有%d种不足，缺料总价值%.2f元，需要补料后才能开工",
                    totalMaterials, insufficientMaterials, totalShortageValue);
            }
            checkResult.put("summary", summary);

            log.info("BOM物料库存检查完成：BOM【{}】生产数量【{}】结果【{}】", bomId, requiredQuantity, summary);
            return checkResult;
        } catch (Exception e) {
            log.error("BOM物料库存检查失败：{}", e.getMessage(), e);
            throw new ServiceException("BOM物料库存检查失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> checkProductBomInventory(Long productId, BigDecimal requiredQuantity) {
        try {
            Map<String, Object> checkResult = new HashMap<>();

            // TODO: 集成产品模块，获取产品基本信息
            // 需要实现：Product product = productService.getById(productId);
            // if (product == null) {
            //     throw new ServiceException("产品不存在：" + productId);
            // }

            // TODO: 集成BOM模块，获取产品对应的BOM
            // 需要实现：List<Bom> boms = bomService.getByProductId(productId);
            // if (boms.isEmpty()) {
            //     throw new ServiceException("产品【" + product.getProductName() + "】未配置BOM清单");
            // }

            // TODO: 支持多BOM版本选择，当前选择最新版本
            // Bom activeBom = boms.stream()
            //     .filter(bom -> "ACTIVE".equals(bom.getBomStatus()))
            //     .max(Comparator.comparing(Bom::getVersionNumber))
            //     .orElseThrow(() -> new ServiceException("产品【" + product.getProductName() + "】没有有效的BOM版本"));

            log.info("产品BOM物料库存检查：产品【{}】生产数量【{}】", productId, requiredQuantity);

            // 模拟获取BOM ID并进行检查
            Long bomId = 1000L + productId; // 模拟BOM ID
            Map<String, Object> bomCheckResult = checkBomInventory(bomId, requiredQuantity);

            // 添加产品相关信息
            checkResult.put("productId", productId);
            checkResult.put("bomId", bomId);
            checkResult.put("requiredQuantity", requiredQuantity);
            checkResult.putAll(bomCheckResult);

            // TODO: 后续需要完善的功能
            // 支持产品多BOM版本管理
            // 支持产品替代料管理
            // 支持产品工艺路线关联的物料检查
            // 支持产品成本分析集成

            return checkResult;
        } catch (Exception e) {
            log.error("产品BOM物料库存检查失败：{}", e.getMessage(), e);
            throw new ServiceException("产品BOM物料库存检查失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getBomMaterialInventoryDetails(Long bomId, BigDecimal requiredQuantity) {
        try {
            List<Map<String, Object>> materialDetails = new ArrayList<>();

            // TODO: 集成BOM详情模块，获取BOM物料清单
            // 需要实现：List<BomDetail> bomDetails = bomDetailService.getByBomId(bomId);
            // if (bomDetails.isEmpty()) {
            //     throw new ServiceException("BOM【" + bomId + "】未配置物料清单");
            // }

            // TODO: 集成库存模块，获取物料库存信息
            // 需要实现：for (BomDetail detail : bomDetails) {
            //     Material material = materialService.getById(detail.getMaterialId());
            //     InventoryInfo inventory = inventoryService.getByMaterialId(detail.getMaterialId());
            //     // 计算需求和缺口
            // }

            log.info("获取BOM物料库存详情：BOM【{}】生产数量【{}】", bomId, requiredQuantity);

            // 模拟BOM物料数据，实际需要从数据库查询
            List<Map<String, Object>> mockBomDetails = getMockBomDetails(bomId);

            for (Map<String, Object> bomDetail : mockBomDetails) {
                Long materialId = (Long) bomDetail.get("materialId");
                BigDecimal bomQuantity = (BigDecimal) bomDetail.get("bomQuantity");

                // TODO: 集成物料模块，获取物料基本信息
                // Material material = materialService.getById(materialId);

                // TODO: 集成库存模块，获取物料库存信息
                // InventoryInfo inventory = inventoryService.getByMaterialId(materialId);
                // BigDecimal currentStock = inventory.getCurrentStock();
                // BigDecimal availableStock = inventory.getAvailableStock();

                Map<String, Object> material = buildMaterialInventoryDetail(bomDetail, requiredQuantity);
                materialDetails.add(material);
            }

            // TODO: 后续需要完善的功能
            // 支持物料替代料检查
            // 支持多仓库库存汇总
            // 支持物料有效期检查
            // 支持物料质量状态检查
            // 支持物料预留数量扣除

            return materialDetails;
        } catch (Exception e) {
            log.error("获取BOM物料库存详情失败：{}", e.getMessage(), e);
            throw new ServiceException("获取BOM物料库存详情失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getMaterialShortageAnalysis(Long bomId, BigDecimal requiredQuantity) {
        try {
            Map<String, Object> analysis = new HashMap<>();

            // 获取物料详情
            List<Map<String, Object>> materialDetails = getBomMaterialInventoryDetails(bomId, requiredQuantity);

            // 分析缺料情况
            List<Map<String, Object>> shortageList = new ArrayList<>();
            BigDecimal totalShortageValue = BigDecimal.ZERO;
            int criticalShortages = 0; // 严重缺料数量
            int minorShortages = 0; // 轻微缺料数量

            for (Map<String, Object> material : materialDetails) {
                Boolean isSufficient = (Boolean) material.get("isSufficient");
                if (!isSufficient) {
                    shortageList.add(material);

                    BigDecimal shortageValue = (BigDecimal) material.get("shortageValue");
                    totalShortageValue = totalShortageValue.add(shortageValue);

                    String stockStatus = (String) material.get("stockStatus");
                    if ("严重不足".equals(stockStatus)) {
                        criticalShortages++;
                    } else {
                        minorShortages++;
                    }
                }
            }

            // 构建分析结果
            analysis.put("bomId", bomId);
            analysis.put("requiredQuantity", requiredQuantity);
            analysis.put("totalMaterials", materialDetails.size());
            analysis.put("shortageCount", shortageList.size());
            analysis.put("criticalShortages", criticalShortages);
            analysis.put("minorShortages", minorShortages);
            analysis.put("totalShortageValue", totalShortageValue);
            analysis.put("shortageList", shortageList);

            // 生成分析建议
            List<String> suggestions = new ArrayList<>();
            if (criticalShortages > 0) {
                suggestions.add("存在" + criticalShortages + "种严重缺料，建议立即采购补充");
            }
            if (minorShortages > 0) {
                suggestions.add("存在" + minorShortages + "种轻微缺料，建议适当补充库存");
            }
            if (shortageList.isEmpty()) {
                suggestions.add("所有物料库存充足，可以正常开工生产");
            }
            analysis.put("suggestions", suggestions);

            log.info("物料缺口分析完成：BOM【{}】缺料种类【{}】总价值【{}】", bomId, shortageList.size(), totalShortageValue);
            return analysis;
        } catch (Exception e) {
            log.error("物料缺口分析失败：{}", e.getMessage(), e);
            throw new ServiceException("物料缺口分析失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> generateMaterialReplenishmentSuggestions(Long bomId, BigDecimal requiredQuantity) {
        try {
            List<Map<String, Object>> suggestions = new ArrayList<>();

            // 获取缺料分析
            Map<String, Object> shortageAnalysis = getMaterialShortageAnalysis(bomId, requiredQuantity);
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> shortageList = (List<Map<String, Object>>) shortageAnalysis.get("shortageList");

            for (Map<String, Object> shortage : shortageList) {
                Map<String, Object> suggestion = new HashMap<>();

                // 基本信息
                suggestion.put("materialId", shortage.get("materialId"));
                suggestion.put("materialCode", shortage.get("materialCode"));
                suggestion.put("materialName", shortage.get("materialName"));
                suggestion.put("unit", shortage.get("unit"));

                // 缺料信息
                BigDecimal shortageQty = (BigDecimal) shortage.get("shortage");
                BigDecimal unitPrice = (BigDecimal) shortage.get("unitPrice");
                suggestion.put("shortageQuantity", shortageQty);
                suggestion.put("unitPrice", unitPrice);

                // 补料建议
                BigDecimal suggestedQty = shortageQty.multiply(new BigDecimal("1.2")); // 建议多采购20%作为安全库存
                BigDecimal suggestedValue = suggestedQty.multiply(unitPrice);
                suggestion.put("suggestedQuantity", suggestedQty.setScale(2, RoundingMode.HALF_UP));
                suggestion.put("suggestedValue", suggestedValue.setScale(2, RoundingMode.HALF_UP));

                // 优先级
                String stockStatus = (String) shortage.get("stockStatus");
                String priority = "严重不足".equals(stockStatus) ? "高" : "中";
                suggestion.put("priority", priority);

                // 建议采购时间
                String suggestedTime = "严重不足".equals(stockStatus) ? "立即采购" : "3天内采购";
                suggestion.put("suggestedTime", suggestedTime);

                suggestions.add(suggestion);
            }

            // 按优先级排序
            suggestions.sort((a, b) -> {
                String priorityA = (String) a.get("priority");
                String priorityB = (String) b.get("priority");
                return "高".equals(priorityA) ? -1 : ("高".equals(priorityB) ? 1 : 0);
            });

            log.info("生成补料建议完成：BOM【{}】建议补料【{}】种", bomId, suggestions.size());
            return suggestions;
        } catch (Exception e) {
            log.error("生成补料建议失败：{}", e.getMessage(), e);
            throw new ServiceException("生成补料建议失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> checkCanStartProduction(Long bomId, BigDecimal requiredQuantity) {
        try {
            Map<String, Object> checkResult = checkBomInventory(bomId, requiredQuantity);

            Map<String, Object> startCheck = new HashMap<>();
            startCheck.put("bomId", bomId);
            startCheck.put("requiredQuantity", requiredQuantity);

            Boolean canStart = (Boolean) checkResult.get("canStartProduction");
            startCheck.put("canStartProduction", canStart);

            if (canStart) {
                startCheck.put("result", "PASS");
                startCheck.put("message", "物料库存充足，可以开工生产");
                startCheck.put("nextAction", "可以创建生产订单并开始生产");
            } else {
                startCheck.put("result", "FAIL");
                Integer insufficientMaterials = (Integer) checkResult.get("insufficientMaterials");
                BigDecimal totalShortageValue = (BigDecimal) checkResult.get("totalShortageValue");
                startCheck.put("message", String.format("物料不足，有%d种物料缺料，总价值%.2f元", insufficientMaterials, totalShortageValue));
                startCheck.put("nextAction", "请先补充缺料后再开工");

                // 添加补料建议
                List<Map<String, Object>> suggestions = generateMaterialReplenishmentSuggestions(bomId, requiredQuantity);
                startCheck.put("replenishmentSuggestions", suggestions);
            }

            log.info("开工检查完成：BOM【{}】结果【{}】", bomId, startCheck.get("result"));
            return startCheck;
        } catch (Exception e) {
            log.error("开工检查失败：{}", e.getMessage(), e);
            throw new ServiceException("开工检查失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> batchCheckBomInventory(List<Map<String, Object>> bomCheckRequests) {
        try {
            List<Map<String, Object>> batchResults = new ArrayList<>();

            for (Map<String, Object> request : bomCheckRequests) {
                Long bomId = Long.valueOf(request.get("bomId").toString());
                BigDecimal requiredQuantity = new BigDecimal(request.get("requiredQuantity").toString());

                Map<String, Object> checkResult = checkBomInventory(bomId, requiredQuantity);
                batchResults.add(checkResult);
            }

            log.info("批量BOM库存检查完成：检查数量【{}】", bomCheckRequests.size());
            return batchResults;
        } catch (Exception e) {
            log.error("批量BOM库存检查失败：{}", e.getMessage(), e);
            throw new ServiceException("批量BOM库存检查失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getMaterialInventoryAlerts(Long bomId, BigDecimal requiredQuantity) {
        try {
            Map<String, Object> alerts = new HashMap<>();

            // 获取物料详情
            List<Map<String, Object>> materialDetails = getBomMaterialInventoryDetails(bomId, requiredQuantity);

            List<Map<String, Object>> alertList = new ArrayList<>();
            for (Map<String, Object> material : materialDetails) {
                BigDecimal currentStock = (BigDecimal) material.get("currentStock");
                BigDecimal totalRequired = (BigDecimal) material.get("totalRequired");

                // 计算库存周转天数（假设日消耗量）
                BigDecimal dailyConsumption = totalRequired.divide(new BigDecimal("30"), 2, RoundingMode.HALF_UP);
                BigDecimal stockDays = currentStock.divide(dailyConsumption, 0, RoundingMode.HALF_UP);

                Map<String, Object> alert = new HashMap<>();
                alert.put("materialId", material.get("materialId"));
                alert.put("materialCode", material.get("materialCode"));
                alert.put("materialName", material.get("materialName"));
                alert.put("currentStock", currentStock);
                alert.put("stockDays", stockDays);

                // 预警级别
                String alertLevel;
                String alertMessage;
                if (stockDays.compareTo(new BigDecimal("7")) <= 0) {
                    alertLevel = "紧急";
                    alertMessage = "库存仅够" + stockDays + "天，需要紧急补货";
                } else if (stockDays.compareTo(new BigDecimal("15")) <= 0) {
                    alertLevel = "警告";
                    alertMessage = "库存仅够" + stockDays + "天，建议及时补货";
                } else if (stockDays.compareTo(new BigDecimal("30")) <= 0) {
                    alertLevel = "提醒";
                    alertMessage = "库存够" + stockDays + "天，可考虑适当补货";
                } else {
                    alertLevel = "正常";
                    alertMessage = "库存充足";
                }

                alert.put("alertLevel", alertLevel);
                alert.put("alertMessage", alertMessage);

                if (!"正常".equals(alertLevel)) {
                    alertList.add(alert);
                }
            }

            alerts.put("bomId", bomId);
            alerts.put("alertCount", alertList.size());
            alerts.put("alertList", alertList);

            log.info("物料库存预警检查完成：BOM【{}】预警数量【{}】", bomId, alertList.size());
            return alerts;
        } catch (Exception e) {
            log.error("物料库存预警检查失败：{}", e.getMessage(), e);
            throw new ServiceException("物料库存预警检查失败：" + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 获取模拟BOM详情数据
     * TODO: 后续需要替换为真实的BOM详情查询
     */
    private List<Map<String, Object>> getMockBomDetails(Long bomId) {
        List<Map<String, Object>> mockDetails = new ArrayList<>();

        // 模拟3种物料的BOM配置
        for (int i = 1; i <= 3; i++) {
            Map<String, Object> detail = new HashMap<>();
            detail.put("materialId", 1000L + i);
            detail.put("materialCode", "MAT" + String.format("%03d", i));
            detail.put("materialName", "物料" + i);
            detail.put("materialSpec", "规格" + i);
            detail.put("unit", "个");
            detail.put("bomQuantity", new BigDecimal(i * 2)); // 每个产品需要的物料数量
            detail.put("unitPrice", new BigDecimal(10 + i * 5)); // 模拟单价
            mockDetails.add(detail);
        }

        return mockDetails;
    }

    /**
     * 构建物料库存详情
     * TODO: 后续需要集成真实的库存查询逻辑
     */
    private Map<String, Object> buildMaterialInventoryDetail(Map<String, Object> bomDetail, BigDecimal requiredQuantity) {
        Map<String, Object> material = new HashMap<>();

        // 基本信息
        material.put("materialId", bomDetail.get("materialId"));
        material.put("materialCode", bomDetail.get("materialCode"));
        material.put("materialName", bomDetail.get("materialName"));
        material.put("materialSpec", bomDetail.get("materialSpec"));
        material.put("unit", bomDetail.get("unit"));

        // BOM需求
        BigDecimal bomQuantity = (BigDecimal) bomDetail.get("bomQuantity");
        BigDecimal totalRequired = bomQuantity.multiply(requiredQuantity);
        material.put("bomQuantity", bomQuantity);
        material.put("totalRequired", totalRequired);

        // 模拟库存情况 - TODO: 替换为真实库存查询
        Long materialId = (Long) bomDetail.get("materialId");
        int mockIndex = materialId.intValue() - 1000;
        BigDecimal currentStock = new BigDecimal(mockIndex * 150); // 模拟当前库存
        BigDecimal availableStock = currentStock.multiply(new BigDecimal("0.9")); // 可用库存（扣除安全库存）
        material.put("currentStock", currentStock);
        material.put("availableStock", availableStock);

        // 缺口分析
        BigDecimal shortage = totalRequired.subtract(availableStock);
        boolean isSufficient = shortage.compareTo(BigDecimal.ZERO) <= 0;
        material.put("shortage", isSufficient ? BigDecimal.ZERO : shortage);
        material.put("isSufficient", isSufficient);

        // 价值计算
        BigDecimal unitPrice = (BigDecimal) bomDetail.get("unitPrice");
        BigDecimal shortageValue = isSufficient ? BigDecimal.ZERO : shortage.multiply(unitPrice);
        material.put("unitPrice", unitPrice);
        material.put("shortageValue", shortageValue);

        // 库存状态
        String stockStatus = determineStockStatus(isSufficient, shortage, totalRequired);
        material.put("stockStatus", stockStatus);

        return material;
    }

    /**
     * 判断库存状态
     */
    private String determineStockStatus(boolean isSufficient, BigDecimal shortage, BigDecimal totalRequired) {
        if (isSufficient) {
            return "充足";
        } else if (shortage.compareTo(totalRequired.multiply(new BigDecimal("0.5"))) <= 0) {
            return "轻微不足";
        } else {
            return "严重不足";
        }
    }
}
