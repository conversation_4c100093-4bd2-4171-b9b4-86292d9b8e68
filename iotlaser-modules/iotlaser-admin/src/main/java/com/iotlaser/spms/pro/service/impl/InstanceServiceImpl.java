package com.iotlaser.spms.pro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.mes.enums.ProductionOrderStatus;
import com.iotlaser.spms.mes.service.IProductionOrderService;
import com.iotlaser.spms.pro.domain.Instance;
import com.iotlaser.spms.pro.domain.bo.InstanceBo;
import com.iotlaser.spms.pro.domain.vo.InstanceVo;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.enums.InstanceStatus;
import com.iotlaser.spms.pro.mapper.InstanceMapper;
import com.iotlaser.spms.pro.service.IInstanceService;
import com.iotlaser.spms.pro.service.IProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static com.iotlaser.spms.base.enums.GenCodeType.PRO_INSTANCE_CODE;

/**
 * 产品实例Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InstanceServiceImpl implements IInstanceService {

    private final InstanceMapper baseMapper;
    private final Gen gen;
    private final IProductionOrderService productionOrderService;
    private final IProductService productService;

    /**
     * 查询产品实例
     *
     * @param instanceId 主键
     * @return 产品实例
     */
    @Override
    public InstanceVo queryById(Long instanceId) {
        return baseMapper.selectVoById(instanceId);
    }

    /**
     * 分页查询产品实例列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品实例分页列表
     */
    @Override
    public TableDataInfo<InstanceVo> queryPageList(InstanceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Instance> lqw = buildQueryWrapper(bo);
        Page<InstanceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品实例列表
     *
     * @param bo 查询条件
     * @return 产品实例列表
     */
    @Override
    public List<InstanceVo> queryList(InstanceBo bo) {
        LambdaQueryWrapper<Instance> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Instance> buildQueryWrapper(InstanceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Instance> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Instance::getInstanceId);
        lqw.eq(StringUtils.isNotBlank(bo.getInstanceCode()), Instance::getInstanceCode, bo.getInstanceCode());
        lqw.eq(bo.getProductId() != null, Instance::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), Instance::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), Instance::getProductName, bo.getProductName());
        lqw.eq(bo.getSaleOrderId() != null, Instance::getSaleOrderId, bo.getSaleOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getSaleOrderCode()), Instance::getSaleOrderCode, bo.getSaleOrderCode());
        lqw.eq(bo.getProductionOrderId() != null, Instance::getProductionOrderId, bo.getProductionOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductionOrderCode()), Instance::getProductionOrderCode, bo.getProductionOrderCode());
        lqw.eq(bo.getBomId() != null, Instance::getBomId, bo.getBomId());
        lqw.eq(StringUtils.isNotBlank(bo.getBomCode()), Instance::getBomCode, bo.getBomCode());
        lqw.like(StringUtils.isNotBlank(bo.getBomName()), Instance::getBomName, bo.getBomName());
        lqw.eq(bo.getProductionTime() != null, Instance::getProductionTime, bo.getProductionTime());
        lqw.eq(bo.getInstanceStatus() != null, Instance::getInstanceStatus, bo.getInstanceStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Instance::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品实例
     *
     * @param bo 产品实例
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(InstanceBo bo) {
        if (StringUtils.isEmpty(bo.getInstanceCode())) {
            bo.setInstanceCode(gen.code(PRO_INSTANCE_CODE));
        }
        bo.setInstanceStatus(InstanceStatus.DRAFT);
        Instance add = MapstructUtils.convert(bo, Instance.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setInstanceId(add.getInstanceId());
            log.info("新增产品实例成功，实例编码：{}", add.getInstanceCode());
        }
        return flag;
    }

    /**
     * 修改产品实例
     *
     * @param bo 产品实例
     * @return 是否修改成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateByBo(InstanceBo bo) {
        Instance update = MapstructUtils.convert(bo, Instance.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            log.info("修改产品实例成功，实例编码：{}", update.getInstanceCode());
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Instance entity) {
        // 校验实例编码唯一性
        if (StringUtils.isNotBlank(entity.getInstanceCode())) {
            LambdaQueryWrapper<Instance> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Instance::getInstanceCode, entity.getInstanceCode());
            if (entity.getInstanceId() != null) {
                wrapper.ne(Instance::getInstanceId, entity.getInstanceId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("实例编码已存在：" + entity.getInstanceCode());
            }
        }
    }

    /**
     * 校验并批量删除产品实例信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验实例状态，只有草稿状态的实例才能删除
            List<Instance> instances = baseMapper.selectByIds(ids);
            for (Instance instance : instances) {
                if (instance.getInstanceStatus() != InstanceStatus.DRAFT) {
                    throw new ServiceException("实例【" + instance.getInstanceCode() + "】状态为【" +
                        instance.getInstanceStatus() + "】，不允许删除");
                }
                log.info("删除产品实例，实例编码：{}", instance.getInstanceCode());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据实例编码查询产品实例
     * 基于README_FLOW.md第7.2节：生产报工流程
     *
     * @param instanceCode 实例编码
     * @return 产品实例
     */
    @Override
    public InstanceVo getByInstanceCode(String instanceCode) {
        try {
            LambdaQueryWrapper<Instance> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Instance::getInstanceCode, instanceCode);

            Instance instance = baseMapper.selectOne(wrapper);
            if (instance == null) {
                return null;
            }

            return MapstructUtils.convert(instance, InstanceVo.class);
        } catch (Exception e) {
            log.error("根据实例编码查询产品实例失败：{}", e.getMessage(), e);
            throw new ServiceException("查询产品实例失败：" + e.getMessage());
        }
    }

    /**
     * 创建产品实例
     * 基于README_FLOW.md第7.2节：开工 (首工序)
     *
     * @param orderId   生产订单ID
     * @param productId 产品ID
     * @param routingId 工艺路线ID (临时参数，当前版本可为null)
     * @return 产品实例编码
     */
    @Transactional(rollbackFor = Exception.class)
    public String createProductInstance(Long orderId, Long productId, Long routingId) {
        try {
            // 验证生产订单状态是否允许创建实例
            var productionOrder = productionOrderService.queryById(orderId);
            if (productionOrder == null) {
                throw new ServiceException("生产订单不存在：" + orderId);
            }

            // 验证订单状态：只有已下达(RELEASED)或生产中(IN_PROGRESS)的订单才能创建实例
            ProductionOrderStatus orderStatus = productionOrder.getOrderStatus();
            if (orderStatus != ProductionOrderStatus.RELEASED && orderStatus != ProductionOrderStatus.IN_PROGRESS) {
                throw new ServiceException("生产订单状态不正确，当前状态：" + orderStatus.getName() + "，无法创建产品实例");
            }

            // 验证产品信息
            ProductVo product = productService.queryById(productId);
            if (product == null) {
                throw new ServiceException("产品不存在：" + productId);
            }

            // 生成唯一的产品实例编码
            String instanceCode = gen.code(PRO_INSTANCE_CODE);

            // 创建产品实例记录
            Instance instance = new Instance();
            instance.setInstanceCode(instanceCode);
            instance.setProductId(productId);
            instance.setProductCode(product.getProductCode());
            instance.setProductName(product.getProductName());

            // 关联生产订单信息
            instance.setProductionOrderId(orderId);
            instance.setProductionOrderCode(productionOrder.getOrderCode());

            // 关联销售订单信息(如果存在) TODO 需完善
            if (productionOrder.getSourceId() != null) {
                instance.setSaleOrderId(productionOrder.getSourceId());
                instance.setSaleOrderCode(productionOrder.getSourceCode());
            }

            // 关联BOM信息(如果存在)
            if (productionOrder.getBomId() != null) {
                instance.setBomId(productionOrder.getBomId());
                instance.setBomCode(productionOrder.getBomCode());
                instance.setBomName(productionOrder.getBomName());
            }

            // 设置初始状态和时间
            instance.setInstanceStatus(InstanceStatus.ACTIVE);
            instance.setProductionTime(LocalDateTime.now());

            // 保存实例记录
            validEntityBeforeSave(instance);
            boolean flag = baseMapper.insert(instance) > 0;
            if (!flag) {
                throw new ServiceException("创建产品实例失败：数据库插入失败");
            }

            log.info("创建产品实例成功：订单【{}】产品【{}】实例编码【{}】",
                productionOrder.getOrderCode(), product.getProductCode(), instanceCode);

            return instanceCode;
        } catch (Exception e) {
            log.error("创建产品实例失败：{}", e.getMessage(), e);
            throw new ServiceException("创建产品实例失败：" + e.getMessage());
        }
    }

    /**
     * 更新产品实例状态
     * 基于README_FLOW.md第7.2节：完工 (当前工序)
     *
     * @param instanceCode 实例编码
     * @param newStatus    新状态
     * @param reason       状态变更原因
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateInstanceStatus(String instanceCode, InstanceStatus newStatus, String reason) {
        try {
            // 根据实例编码查找实例
            Instance instance = baseMapper.selectOne(
                Wrappers.lambdaQuery(Instance.class)
                    .eq(Instance::getInstanceCode, instanceCode)
            );
            if (instance == null) {
                throw new ServiceException("产品实例不存在：" + instanceCode);
            }

            // 获取当前状态
            InstanceStatus currentStatus = instance.getInstanceStatus();

            // 验证状态转换的合法性
            if (!isValidStatusTransition(currentStatus, newStatus)) {
                throw new ServiceException("无效的状态转换：" + currentStatus.getName() + " -> " + newStatus.getName());
            }

            // 更新实例状态
            InstanceStatus oldStatus = instance.getInstanceStatus();
            instance.setInstanceStatus(newStatus);

            // 根据新状态设置相关时间字段
            if (newStatus == InstanceStatus.ACTIVE) {
                // 激活状态：设置生产时间
                if (instance.getProductionTime() == null) {
                    instance.setProductionTime(LocalDateTime.now());
                }
            }

            // 更新数据库
            boolean flag = baseMapper.updateById(instance) > 0;
            if (!flag) {
                throw new ServiceException("更新产品实例状态失败：数据库更新失败");
            }

            log.info("更新产品实例状态成功：实例【{}】状态【{} -> {}】原因【{}】",
                instanceCode, oldStatus, newStatus.getValue(), reason);

            return true;
        } catch (Exception e) {
            log.error("更新产品实例状态失败：{}", e.getMessage(), e);
            throw new ServiceException("更新产品实例状态失败：" + e.getMessage());
        }
    }

    /**
     * 验证状态转换的合法性
     * 基于产品实例状态机规则
     *
     * @param fromStatus 原状态
     * @param toStatus   目标状态
     * @return 是否允许转换
     */
    private boolean isValidStatusTransition(InstanceStatus fromStatus, InstanceStatus toStatus) {
        // 定义状态转换规则
        Map<InstanceStatus, Set<InstanceStatus>> transitionRules = new HashMap<>();

        // DRAFT -> ACTIVE
        transitionRules.put(InstanceStatus.DRAFT, Set.of(InstanceStatus.ACTIVE, InstanceStatus.ARCHIVED));

        // ACTIVE -> IN_USE, MAINTENANCE, RETIRED, ARCHIVED
        transitionRules.put(InstanceStatus.ACTIVE, Set.of(
            InstanceStatus.IN_USE, InstanceStatus.MAINTENANCE, InstanceStatus.RETIRED, InstanceStatus.ARCHIVED));

        // IN_USE -> ACTIVE, MAINTENANCE, RETIRED, ARCHIVED
        transitionRules.put(InstanceStatus.IN_USE, Set.of(
            InstanceStatus.ACTIVE, InstanceStatus.MAINTENANCE, InstanceStatus.RETIRED, InstanceStatus.ARCHIVED));

        // MAINTENANCE -> ACTIVE, IN_USE, RETIRED, ARCHIVED
        transitionRules.put(InstanceStatus.MAINTENANCE, Set.of(
            InstanceStatus.ACTIVE, InstanceStatus.IN_USE, InstanceStatus.RETIRED, InstanceStatus.ARCHIVED));

        // RETIRED -> ARCHIVED (只能归档)
        transitionRules.put(InstanceStatus.RETIRED, Set.of(InstanceStatus.ARCHIVED));

        // ARCHIVED 是终态，不能转换到其他状态
        transitionRules.put(InstanceStatus.ARCHIVED, Set.of());

        Set<InstanceStatus> allowedTransitions = transitionRules.get(fromStatus);
        return allowedTransitions != null && allowedTransitions.contains(toStatus);
    }


    /**
     * 获取产品实例的生产进度
     * 基于README_FLOW.md第7.2节：工序流转与报工
     *
     * @param instanceCode 实例编码
     * @return 生产进度信息
     */
    @Override
    public Map<String, Object> getProductionProgress(String instanceCode) {
        try {
            Map<String, Object> progressInfo = new HashMap<>();

            // 获取产品实例基本信息
            InstanceVo instance = getByInstanceCode(instanceCode);
            if (instance == null) {
                throw new ServiceException("产品实例不存在：" + instanceCode);
            }

            progressInfo.put("instanceCode", instanceCode);
            progressInfo.put("instanceStatus", instance.getInstanceStatus());
            progressInfo.put("productName", instance.getProductName());
            progressInfo.put("productionOrderCode", instance.getProductionOrderCode());

            // 计算临时变量：生产进度百分比
            // @TableField(exist = false) - 临时变量
            BigDecimal progressPercent = calculateProgressPercent(instance);
            progressInfo.put("progressPercent", progressPercent);

            // 统计临时变量：报工记录总数
            // @TableField(exist = false) - 临时变量
            Integer totalReports = getTotalReportsCount(instance.getInstanceId());
            progressInfo.put("totalReports", totalReports);

            // 获取临时变量：当前工序名称
            // @TableField(exist = false) - 临时变量
            String currentStepName = getCurrentStepName(instance.getInstanceId());
            progressInfo.put("currentStepName", currentStepName);

            log.info("获取产品实例【{}】生产进度成功", instanceCode);
            return progressInfo;
        } catch (Exception e) {
            log.error("获取产品实例生产进度失败：{}", e.getMessage(), e);
            throw new ServiceException("获取生产进度失败：" + e.getMessage());
        }
    }

    /**
     * 检查产品实例是否可以进行指定操作
     * 基于README_FLOW.md第7.2节：业务规则校验
     *
     * @param instanceCode 实例编码
     * @param operation    操作类型 (START, COMPLETE, CONSUME等)
     * @return 是否可以操作
     */
    @Override
    public Boolean canPerformOperation(String instanceCode, String operation) {
        try {
            InstanceVo instance = getByInstanceCode(instanceCode);
            if (instance == null) {
                return false;
            }

            InstanceStatus currentStatus = instance.getInstanceStatus();

            // 根据操作类型和当前状态判断是否可以执行操作
            switch (operation.toUpperCase()) {
                case "START":
                    // 只有草稿状态可以开工
                    return currentStatus == InstanceStatus.DRAFT;
                case "COMPLETE":
                    // 激活或使用中状态可以完工
                    return currentStatus == InstanceStatus.ACTIVE || currentStatus == InstanceStatus.IN_USE;
                case "CONSUME":
                    // 激活或使用中状态可以消耗物料
                    return currentStatus == InstanceStatus.ACTIVE || currentStatus == InstanceStatus.IN_USE;
                case "MAINTENANCE":
                    // 激活或使用中状态可以进入维护
                    return currentStatus == InstanceStatus.ACTIVE || currentStatus == InstanceStatus.IN_USE;
                case "RETIRE":
                    // 除了已归档状态，其他状态都可以退役
                    return currentStatus != InstanceStatus.ARCHIVED;
                case "ARCHIVE":
                    // 任何状态都可以归档
                    return true;
                default:
                    log.warn("未知的操作类型：{}", operation);
                    return false;
            }
        } catch (Exception e) {
            log.error("检查产品实例操作权限失败：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 计算生产进度百分比 (临时变量)
     *
     * @TableField(exist = false)
     */
    private BigDecimal calculateProgressPercent(InstanceVo instance) {
        // 基于实例状态简单计算进度
        InstanceStatus status = instance.getInstanceStatus();
        switch (status) {
            case DRAFT:
                return BigDecimal.ZERO;
            case ACTIVE:
                return BigDecimal.valueOf(25);
            case IN_USE:
                return BigDecimal.valueOf(75);
            case MAINTENANCE:
                return BigDecimal.valueOf(50);
            case RETIRED:
                return BigDecimal.valueOf(100);
            case ARCHIVED:
                return BigDecimal.valueOf(100);
            default:
                return BigDecimal.ZERO;
        }
    }

    /**
     * 获取报工记录总数 (临时变量)
     *
     * @TableField(exist = false)
     */
    private Integer getTotalReportsCount(Long instanceId) {
        // TODO: 集成生产报工服务查询报工记录数量
        // 当前返回模拟数据，待集成后实现真实查询
        return 0;
    }

    /**
     * 获取当前工序名称 (临时变量)
     *
     * @TableField(exist = false)
     */
    private String getCurrentStepName(Long instanceId) {
        // TODO: 集成工艺路线服务查询当前工序名称
        // 当前返回模拟数据，待集成后实现真实查询
        return "待定工序";
    }
}
