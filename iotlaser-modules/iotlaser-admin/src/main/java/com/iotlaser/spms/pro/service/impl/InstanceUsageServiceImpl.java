package com.iotlaser.spms.pro.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.pro.domain.InstanceUsage;
import com.iotlaser.spms.pro.domain.bo.InstanceUsageBo;
import com.iotlaser.spms.pro.domain.vo.InstanceUsageVo;
import com.iotlaser.spms.pro.domain.vo.InstanceVo;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.pro.mapper.InstanceUsageMapper;
import com.iotlaser.spms.pro.service.IInstanceService;
import com.iotlaser.spms.pro.service.IInstanceUsageService;
import com.iotlaser.spms.pro.service.IProductService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 产品实例组件使用Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InstanceUsageServiceImpl implements IInstanceUsageService {

    private final InstanceUsageMapper baseMapper;
    private final IInstanceService instanceService;
    private final IProductService productService;

    /**
     * 查询产品实例组件使用
     *
     * @param usageId 主键
     * @return 产品实例组件使用
     */
    @Override
    public InstanceUsageVo queryById(Long usageId) {
        return baseMapper.selectVoById(usageId);
    }

    /**
     * 分页查询产品实例组件使用列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品实例组件使用分页列表
     */
    @Override
    public TableDataInfo<InstanceUsageVo> queryPageList(InstanceUsageBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InstanceUsage> lqw = buildQueryWrapper(bo);
        Page<InstanceUsageVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品实例组件使用列表
     *
     * @param bo 查询条件
     * @return 产品实例组件使用列表
     */
    @Override
    public List<InstanceUsageVo> queryList(InstanceUsageBo bo) {
        LambdaQueryWrapper<InstanceUsage> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InstanceUsage> buildQueryWrapper(InstanceUsageBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InstanceUsage> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(InstanceUsage::getUsageId);
        lqw.eq(bo.getInstanceId() != null, InstanceUsage::getInstanceId, bo.getInstanceId());
        lqw.eq(bo.getProductId() != null, InstanceUsage::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), InstanceUsage::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), InstanceUsage::getProductName, bo.getProductName());
        // ✅ 优化：移除数量的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, InstanceUsage::getQuantity, bo.getQuantity());
        // TODO: 如需要可以后续添加数量范围查询支持
        lqw.eq(bo.getUsageTime() != null, InstanceUsage::getUsageTime, bo.getUsageTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), InstanceUsage::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品实例组件使用
     *
     * @param bo 产品实例组件使用
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(InstanceUsageBo bo) {
        InstanceUsage add = MapstructUtils.convert(bo, InstanceUsage.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setUsageId(add.getUsageId());
        }
        return flag;
    }

    /**
     * 修改产品实例组件使用
     *
     * @param bo 产品实例组件使用
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(InstanceUsageBo bo) {
        InstanceUsage update = MapstructUtils.convert(bo, InstanceUsage.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InstanceUsage entity) {
        //校验必填字段
        if (entity.getInstanceId() == null) {
            throw new ServiceException("产品实例不能为空");
        }
        if (entity.getProductId() == null) {
            throw new ServiceException("组件不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("使用数量必须大于0");
        }

        // 保留关联数据存在性校验
        if (entity.getInstanceId() != null) {
            // TODO: 校验产品实例是否存在
        }
        if (entity.getProductId() != null) {
            // TODO: 校验组件产品是否存在
        }

        // 校验同一实例中组件不能重复
        if (entity.getInstanceId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<InstanceUsage> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(InstanceUsage::getInstanceId, entity.getInstanceId());
            wrapper.eq(InstanceUsage::getProductId, entity.getProductId());
            if (entity.getUsageId() != null) {
                wrapper.ne(InstanceUsage::getUsageId, entity.getUsageId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一产品实例中不能重复添加相同组件");
            }
        }
    }

    /**
     * 校验并批量删除产品实例组件使用信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验组件使用记录是否可以删除
            List<InstanceUsage> usages = baseMapper.selectByIds(ids);
            for (InstanceUsage usage : usages) {
                log.info("删除产品实例组件使用记录，实例：{}，组件：{}",
                    usage.getInstanceId(), usage.getProductId());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 创建物料消耗记录
     * 基于README_FLOW.md第7.2节：物料消耗 (工序中)
     *
     * @param instanceId      产品实例ID
     * @param materialBatchId 物料批次ID
     * @param consumeQuantity 消耗数量
     * @param operatorId      操作员ID
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean createMaterialConsume(Long instanceId, Long materialProductId,
                                         BigDecimal consumeQuantity, Long operatorId) {
        try {
            // 验证产品实例状态
            InstanceVo instance = instanceService.queryById(instanceId);
            if (instance == null) {
                throw new ServiceException("产品实例不存在：" + instanceId);
            }

            // 验证实例状态：只有激活(ACTIVE)或使用中(IN_USE)的实例才能消耗物料
            // TODO: 修复实例状态转换逻辑
            // InstanceStatus instanceStatus = InstanceStatus.getByValue(instance.getInstanceStatus());
            // if (instanceStatus == null) {
            //     throw new ServiceException("产品实例状态无效：" + instance.getInstanceStatus());
            // }
            // TODO: 修复实例状态验证逻辑
            // if (instanceStatus != InstanceStatus.ACTIVE && instanceStatus != InstanceStatus.IN_USE) {
            //     throw new ServiceException("产品实例状态不正确，当前状态：" + instanceStatus.getName() + "，无法消耗物料");
            // }

            // 验证物料产品信息 (使用materialProductId替代materialBatchId，基于现有字段)
            ProductVo materialProduct = productService.queryById(materialProductId);
            if (materialProduct == null) {
                throw new ServiceException("物料产品不存在：" + materialProductId);
            }

            // 验证消耗数量
            if (consumeQuantity == null || consumeQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("消耗数量必须大于0");
            }

            // 创建物料消耗记录
            InstanceUsage usage = new InstanceUsage();
            usage.setInstanceId(instanceId);
            usage.setProductId(materialProductId);
            usage.setProductCode(materialProduct.getProductCode());
            usage.setProductName(materialProduct.getProductName());
            usage.setUnitId(materialProduct.getUnitId());
            usage.setUnitCode(materialProduct.getUnitCode());
            usage.setUnitName(materialProduct.getUnitName());
            usage.setQuantity(consumeQuantity);
            usage.setUsageTime(LocalDateTime.now());

            // 临时存储操作员信息在备注中，待后续版本添加专门字段
            if (operatorId != null) {
                usage.setRemark("操作员ID: " + operatorId + " | 物料消耗记录 | 实例编码: " + instance.getInstanceCode());
            } else {
                usage.setRemark("物料消耗记录 | 实例编码: " + instance.getInstanceCode());
            }

            // 保存消耗记录
            validEntityBeforeSave(usage);
            boolean flag = baseMapper.insert(usage) > 0;
            if (!flag) {
                throw new ServiceException("创建物料消耗记录失败：数据库插入失败");
            }

            log.info("创建物料消耗记录成功：实例【{}】物料【{}】数量【{}】操作员【{}】",
                instance.getInstanceCode(), materialProduct.getProductCode(), consumeQuantity, operatorId);

            return true;
        } catch (Exception e) {
            log.error("创建物料消耗记录失败：{}", e.getMessage(), e);
            throw new ServiceException("创建物料消耗记录失败：" + e.getMessage());
        }
    }

    /**
     * 根据产品实例ID查询所有物料消耗记录
     * 基于README_FLOW.md第7.3节：生产追溯流程
     *
     * @param instanceId 产品实例ID
     * @return 物料消耗记录列表
     */
    @Override
    public List<InstanceUsageVo> queryByInstanceId(Long instanceId) {
        try {
            if (instanceId == null) {
                throw new ServiceException("产品实例ID不能为空");
            }

            LambdaQueryWrapper<InstanceUsage> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(InstanceUsage::getInstanceId, instanceId);
            wrapper.orderByDesc(InstanceUsage::getUsageTime); // 按使用时间倒序排列

            List<InstanceUsage> usageList = baseMapper.selectList(wrapper);
            return MapstructUtils.convert(usageList, InstanceUsageVo.class);
        } catch (Exception e) {
            log.error("根据产品实例ID查询物料消耗记录失败：{}", e.getMessage(), e);
            throw new ServiceException("查询物料消耗记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取产品实例的物料追溯信息
     * 基于README_FLOW.md第7.3节：完整追溯链
     *
     * @param instanceId 产品实例ID
     * @return 物料追溯信息
     */
    @Override
    public Map<String, Object> getMaterialTraceability(Long instanceId) {
        try {
            Map<String, Object> traceInfo = new HashMap<>();

            // 获取产品实例信息
            InstanceVo instance = instanceService.queryById(instanceId);
            if (instance == null) {
                throw new ServiceException("产品实例不存在：" + instanceId);
            }

            // 获取所有物料消耗记录
            List<InstanceUsageVo> usageList = queryByInstanceId(instanceId);

            // 统计临时变量：物料使用记录总数
            // @TableField(exist = false) - 临时变量
            Integer totalMaterialUsages = usageList.size();

            // 计算临时变量：物料消耗总量
            // @TableField(exist = false) - 临时变量
            BigDecimal totalConsumeQuantity = usageList.stream()
                .map(InstanceUsageVo::getQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 构建追溯信息
            traceInfo.put("instanceCode", instance.getInstanceCode());
            traceInfo.put("instanceStatus", instance.getInstanceStatus());
            traceInfo.put("productName", instance.getProductName());
            traceInfo.put("totalMaterialUsages", totalMaterialUsages);
            traceInfo.put("totalConsumeQuantity", totalConsumeQuantity);
            traceInfo.put("usageRecords", usageList);

            // 按产品分组统计
            Map<String, List<InstanceUsageVo>> groupByProduct = usageList.stream()
                .collect(Collectors.groupingBy(usage ->
                    usage.getProductCode() + "-" + usage.getProductName()));
            traceInfo.put("materialsByProduct", groupByProduct);

            log.info("获取产品实例【{}】物料追溯信息成功，共【{}】条记录",
                instance.getInstanceCode(), totalMaterialUsages);

            return traceInfo;
        } catch (Exception e) {
            log.error("获取产品实例物料追溯信息失败：{}", e.getMessage(), e);
            throw new ServiceException("获取物料追溯信息失败：" + e.getMessage());
        }
    }

    /**
     * 批量创建物料消耗记录
     * 支持一次报工消耗多种物料
     *
     * @param instanceId       产品实例ID
     * @param materialConsumes 物料消耗列表 (materialProductId -> quantity)
     * @param operatorId       操作员ID
     * @return 是否创建成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchCreateMaterialConsume(Long instanceId, Map<Long, BigDecimal> materialConsumes,
                                              Long operatorId) {
        try {
            if (instanceId == null) {
                throw new ServiceException("产品实例ID不能为空");
            }

            if (materialConsumes == null || materialConsumes.isEmpty()) {
                throw new ServiceException("物料消耗列表不能为空");
            }

            // 验证产品实例状态
            InstanceVo instance = instanceService.queryById(instanceId);
            if (instance == null) {
                throw new ServiceException("产品实例不存在：" + instanceId);
            }

            // TODO: 修复实例状态转换逻辑
            // InstanceStatus instanceStatus = InstanceStatus.getByValue(instance.getInstanceStatus());
            // if (instanceStatus == null) {
            //     throw new ServiceException("产品实例状态无效：" + instance.getInstanceStatus());
            // }
            // TODO: 修复实例状态验证逻辑
            // if (instanceStatus != InstanceStatus.ACTIVE && instanceStatus != InstanceStatus.IN_USE) {
            //     throw new ServiceException("产品实例状态不正确，当前状态：" + instanceStatus.getName() + "，无法批量消耗物料");
            // }

            // 批量创建物料消耗记录
            int successCount = 0;
            int totalCount = materialConsumes.size();

            for (Map.Entry<Long, BigDecimal> entry : materialConsumes.entrySet()) {
                Long materialProductId = entry.getKey();
                BigDecimal quantity = entry.getValue();

                try {
                    Boolean result = createMaterialConsume(instanceId, materialProductId, quantity, operatorId);
                    if (result) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("批量创建物料消耗记录失败，物料ID：{}，数量：{}，错误：{}",
                        materialProductId, quantity, e.getMessage());
                    // 继续处理其他记录，不中断整个批量操作
                }
            }

            log.info("批量创建物料消耗记录完成：实例【{}】总数【{}】成功【{}】失败【{}】操作员【{}】",
                instance.getInstanceCode(), totalCount, successCount, totalCount - successCount, operatorId);

            // 如果至少有一条记录创建成功，则认为批量操作成功
            return successCount > 0;
        } catch (Exception e) {
            log.error("批量创建物料消耗记录失败：{}", e.getMessage(), e);
            throw new ServiceException("批量创建物料消耗记录失败：" + e.getMessage());
        }
    }
}
