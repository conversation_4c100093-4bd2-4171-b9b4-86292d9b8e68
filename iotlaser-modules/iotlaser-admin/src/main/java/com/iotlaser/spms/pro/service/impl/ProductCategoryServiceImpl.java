package com.iotlaser.spms.pro.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.pro.domain.ProductCategory;
import com.iotlaser.spms.pro.domain.bo.ProductCategoryBo;
import com.iotlaser.spms.pro.domain.vo.ProductCategoryVo;
import com.iotlaser.spms.pro.mapper.ProductCategoryMapper;
import com.iotlaser.spms.pro.service.IProductCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.TreeBuildUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.PRO_PRODUCT_CATEGORY_CODE;

/**
 * 产品分类Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductCategoryServiceImpl implements IProductCategoryService {

    private final ProductCategoryMapper baseMapper;
    private final Gen gen;

    /**
     * 查询产品分类
     *
     * @param categoryId 主键
     * @return 产品分类
     */
    @Override
    public ProductCategoryVo queryById(Long categoryId) {
        return baseMapper.selectVoById(categoryId);
    }

    /**
     * 查询符合条件的产品分类列表
     *
     * @param bo 查询条件
     * @return 产品分类列表
     */
    @Override
    public List<ProductCategoryVo> queryList(ProductCategoryBo bo) {
        LambdaQueryWrapper<ProductCategory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductCategory> buildQueryWrapper(ProductCategoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductCategory> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProductCategory::getOrderNum);
        lqw.notIn(StringUtils.isNotBlank(bo.getExcludeCategoryIds()), ProductCategory::getCategoryId, StringUtils.splitTo(bo.getExcludeCategoryIds(), Convert::toLong));
        lqw.eq(StringUtils.isNotBlank(bo.getCategoryCode()), ProductCategory::getCategoryCode, bo.getCategoryCode());
        lqw.like(StringUtils.isNotBlank(bo.getCategoryName()), ProductCategory::getCategoryName, bo.getCategoryName());
        lqw.eq(bo.getParentId() != null, ProductCategory::getParentId, bo.getParentId());
        lqw.eq(bo.getOrderNum() != null, ProductCategory::getOrderNum, bo.getOrderNum());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ProductCategory::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品分类
     *
     * @param bo 产品分类
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProductCategoryBo bo) {
        if (StringUtils.isEmpty(bo.getCategoryCode())) {
            bo.setCategoryCode(gen.code(PRO_PRODUCT_CATEGORY_CODE));
        }
        ProductCategory add = MapstructUtils.convert(bo, ProductCategory.class);
        validEntityBeforeSave(add);
        redundancyEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCategoryId(add.getCategoryId());
        }
        return flag;
    }

    /**
     * 修改产品分类
     *
     * @param bo 产品分类
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProductCategoryBo bo) {
        ProductCategory update = MapstructUtils.convert(bo, ProductCategory.class);
        validEntityBeforeSave(update);
        redundancyEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductCategory entity) {
        // 校验分类编码唯一性
        if (StringUtils.isNotBlank(entity.getCategoryCode())) {
            LambdaQueryWrapper<ProductCategory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(ProductCategory::getCategoryCode, entity.getCategoryCode());
            if (entity.getCategoryId() != null) {
                wrapper.ne(ProductCategory::getCategoryId, entity.getCategoryId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("产品分类编码已存在：" + entity.getCategoryCode());
            }
        }

        // 校验分类名称唯一性（同级别下）
        LambdaQueryWrapper<ProductCategory> nameWrapper = Wrappers.lambdaQuery();
        nameWrapper.eq(ProductCategory::getCategoryName, entity.getCategoryName());
        nameWrapper.eq(ProductCategory::getParentId, entity.getParentId() != null ? entity.getParentId() : 0L);
        if (entity.getCategoryId() != null) {
            nameWrapper.ne(ProductCategory::getCategoryId, entity.getCategoryId());
        }
        if (baseMapper.exists(nameWrapper)) {
            throw new ServiceException("同级别下产品分类名称已存在：" + entity.getCategoryName());
        }

        // 校验父分类是否存在
        if (entity.getParentId() != null && entity.getParentId() > 0) {
            ProductCategory parent = baseMapper.selectById(entity.getParentId());
            if (parent == null) {
                throw new ServiceException("父分类不存在");
            }
            // 防止循环引用
            if (entity.getCategoryId() != null && entity.getParentId().equals(entity.getCategoryId())) {
                throw new ServiceException("不能将自己设置为父分类");
            }
        }
    }

    /**
     * 保存前的数据填充
     */
    private void redundancyEntityBeforeSave(ProductCategory entity) {
        //TODO
    }

    /**
     * 校验并批量删除产品分类信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验分类是否可以删除
            List<ProductCategory> categories = baseMapper.selectByIds(ids);
            for (ProductCategory category : categories) {
                // 检查是否有子分类
                LambdaQueryWrapper<ProductCategory> childWrapper = Wrappers.lambdaQuery();
                childWrapper.eq(ProductCategory::getParentId, category.getCategoryId());
                if (baseMapper.exists(childWrapper)) {
                    throw new ServiceException("分类【" + category.getCategoryName() + "】存在子分类，不允许删除");
                }

                // 检查是否有关联的产品
                checkProductCategoryProductRelation(category.getCategoryId());
                // 如果有产品模块，可以在这里检查关联关系

                log.info("删除产品分类校验：{}", category.getCategoryName());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public boolean checkProductCategoryCodeUnique(ProductCategoryBo bo) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<ProductCategory>()
            .eq(ProductCategory::getCategoryCode, bo.getCategoryCode())
            .eq(ProductCategory::getParentId, bo.getParentId())
            .ne(ObjectUtil.isNotNull(bo.getCategoryId()), ProductCategory::getCategoryId, bo.getCategoryId()));
        return !exist;
    }

    @Override
    public boolean checkProductCategoryNameUnique(ProductCategoryBo bo) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<ProductCategory>()
            .eq(ProductCategory::getCategoryName, bo.getCategoryName())
            .eq(ProductCategory::getParentId, bo.getParentId())
            .ne(ObjectUtil.isNotNull(bo.getCategoryId()), ProductCategory::getCategoryId, bo.getCategoryId()));
        return !exist;
    }

    @Override
    public List<Tree<Long>> selectTreeList(ProductCategoryBo bo) {
        // 构建查询条件
        LambdaQueryWrapper<ProductCategory> lqw = buildQueryWrapper(bo);
        // 执行查询并获取商品类型列表
        List<ProductCategoryVo> results = baseMapper.selectVoList(lqw);
        // 构建并返回商品类型树形列表
        return buildTreeSelect(results);
    }

    public List<Tree<Long>> buildTreeSelect(List<ProductCategoryVo> results) {
        // 如果类型列表为空，则直接返回一个空的新列表
        if (CollUtil.isEmpty(results)) {
            return CollUtil.newArrayList();
        }
        // 获取当前列表中每一个节点的parentId，然后在列表中查找是否有id与其parentId对应，若无对应，则表明此时节点列表中，该节点在当前列表中属于顶级节点
        List<Tree<Long>> treeList = CollUtil.newArrayList();
        for (ProductCategoryVo productCategoryVo : results) {
            Long parentId = productCategoryVo.getParentId();
            // 寻找当前类型列表中是否有与当前节点的parentId匹配的节点，如果没有找到，则认为当前节点是顶级节点
            ProductCategoryVo first = StreamUtils.findFirst(results, it -> it.getCategoryId().longValue() == parentId);
            // 如果没有找到父节点，说明当前节点是顶级节点，构建树结构并添加到树列表中
            if (ObjectUtil.isNull(first)) {
                // 使用TreeBuildUtils工具类构建树结构
                List<Tree<Long>> trees = TreeBuildUtils.build(results, parentId, (vo, tree) ->
                    tree.setId(vo.getCategoryId())
                        .setParentId(vo.getParentId())
                        .setName(vo.getCategoryName())
                        .setWeight(vo.getOrderNum())
                        .putExtra("disabled", SystemConstants.DISABLE.equals(vo.getStatus())));
                // 找到当前节点在树结构中的位置，并将其添加到树列表中
                Tree<Long> tree = StreamUtils.findFirst(trees, it -> it.getId().longValue() == productCategoryVo.getCategoryId());
                treeList.add(tree);
            }
        }
        // 返回构建好的树结构列表
        return treeList;
    }

    /**
     * 检查产品分类与产品的关联关系
     *
     * @param categoryId 产品分类ID
     */
    private void checkProductCategoryProductRelation(Long categoryId) {
        // TODO: 集成产品模块，检查产品分类的使用情况和关联关系
        // 需要实现：
        // 检查产品表中是否有使用该分类的记录
        // 检查BOM表中是否有使用该分类产品的记录
        // 检查生产订单中是否有使用该分类产品的记录
        // 检查库存中是否有该分类产品的库存记录
        // 提供分类迁移功能（将产品迁移到其他分类）

        // 实现示例：
        // LambdaQueryWrapper<Product> wrapper = Wrappers.lambdaQuery();
        // wrapper.eq(Product::getCategoryId, categoryId);
        // if (productService.exists(wrapper)) {
        //     throw new ServiceException("该产品分类正在被产品使用，不能删除");
        // }

        // TODO: 后续需要完善的功能
        // 产品分类使用情况统计和分析
        // 产品分类层级调整和重组功能
        // 产品分类权限管理和访问控制

        log.debug("检查产品分类【{}】与产品的关联关系", categoryId);
    }
}
