package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.InboundBo;
import com.iotlaser.spms.wms.domain.bo.InboundItemBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import com.iotlaser.spms.wms.domain.vo.InboundVo;
import com.iotlaser.spms.wms.service.IInboundService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品入库
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/inbound")
public class InboundController extends BaseController {

    private final IInboundService inboundService;

    /**
     * 查询产品入库列表
     */
    @SaCheckPermission("wms:inbound:list")
    @GetMapping("/list")
    public TableDataInfo<InboundVo> list(InboundBo bo, PageQuery pageQuery) {
        return inboundService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品入库列表
     */
    @SaCheckPermission("wms:inbound:export")
    @Log(title = "产品入库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InboundBo bo, HttpServletResponse response) {
        List<InboundVo> list = inboundService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品入库", InboundVo.class, response);
    }

    /**
     * 获取产品入库详细信息
     *
     * @param inboundId 主键
     */
    @SaCheckPermission("wms:inbound:query")
    @GetMapping("/{inboundId}")
    public R<InboundVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long inboundId) {
        return R.ok(inboundService.queryById(inboundId));
    }

    /**
     * 新增产品入库
     */
    @SaCheckPermission("wms:inbound:add")
    @Log(title = "产品入库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<InboundVo> add(@Validated(AddGroup.class) @RequestBody InboundBo bo) {
        return R.ok(inboundService.insertByBo(bo));
    }

    /**
     * 修改产品入库
     */
    @SaCheckPermission("wms:inbound:edit")
    @Log(title = "产品入库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<InboundVo> edit(@Validated(EditGroup.class) @RequestBody InboundBo bo) {
        return R.ok(inboundService.updateByBo(bo));
    }

    /**
     * 删除产品入库
     *
     * @param inboundIds 主键串
     */
    @SaCheckPermission("wms:inbound:remove")
    @Log(title = "产品入库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inboundIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] inboundIds) {
        return toAjax(inboundService.deleteWithValidByIds(List.of(inboundIds), true));
    }

    /**
     * 获取产品入库明细表以及关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wms:inbound:query")
    @GetMapping("item/{id}")
    public R<InboundItemVo> queryByIdWith(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(inboundService.queryItemById(id));
    }

    /**
     * 查询产品入库明细表列表以及关联详细信息
     */
    @SaCheckPermission("wms:inbound:list")
    @GetMapping("item/list")
    public TableDataInfo<InboundItemVo> queryPageListWith(InboundItemBo bo, PageQuery pageQuery) {
        return inboundService.queryItemPageList(bo, pageQuery);
    }

    /**
     * 取消入库单
     *
     * @param inboundId 入库ID
     * @param reason    取消原因
     */
    @SaCheckPermission("wms:inbound:edit")
    @Log(title = "入库单取消", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{inboundId}")
    public R<Void> cancel(@NotNull(message = "入库ID不能为空") @PathVariable Long inboundId, @RequestParam(required = false) String reason) {
        return toAjax(inboundService.cancelInbound(inboundId, reason));
    }

    /**
     * 完成入库单
     *
     * @param inboundId 入库ID
     */
    @SaCheckPermission("wms:inbound:edit")
    @Log(title = "入库单取消", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{inboundId}")
    public R<Void> complete(@NotNull(message = "入库ID不能为空") @PathVariable Long inboundId) {
        return toAjax(inboundService.completeInbound(inboundId));
    }


}
