package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.InboundItemBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import com.iotlaser.spms.wms.service.IInboundItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品入库明细
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/inboundItem")
public class InboundItemController extends BaseController {

    private final IInboundItemService inboundItemService;

    /**
     * 查询产品入库明细列表
     */
    @SaCheckPermission("wms:inboundItem:list")
    @GetMapping("/list")
    public TableDataInfo<InboundItemVo> list(InboundItemBo bo, PageQuery pageQuery) {
        return inboundItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品入库明细列表
     */
    @SaCheckPermission("wms:inboundItem:export")
    @Log(title = "产品入库明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InboundItemBo bo, HttpServletResponse response) {
        List<InboundItemVo> list = inboundItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品入库明细", InboundItemVo.class, response);
    }

    /**
     * 获取产品入库明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("wms:inboundItem:query")
    @GetMapping("/{itemId}")
    public R<InboundItemVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long itemId) {
        return R.ok(inboundItemService.queryById(itemId));
    }

    /**
     * 新增产品入库明细
     */
    @SaCheckPermission("wms:inboundItem:add")
    @Log(title = "产品入库明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("insertOrUpdateBatch")
    public R<Void> insertOrUpdateBatch(@Validated(AddGroup.class) @RequestBody List<InboundItemBo> bos) {
        return toAjax(inboundItemService.insertOrUpdateBatch(bos));
    }

    /**
     * 新增产品入库明细
     */
    @SaCheckPermission("wms:inboundItem:add")
    @Log(title = "产品入库明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InboundItemBo bo) {
        return toAjax(inboundItemService.insertByBo(bo));
    }

    /**
     * 修改产品入库明细
     */
    @SaCheckPermission("wms:inboundItem:edit")
    @Log(title = "产品入库明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InboundItemBo bo) {
        return toAjax(inboundItemService.updateByBo(bo));
    }

    /**
     * 删除产品入库明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("wms:inboundItem:remove")
    @Log(title = "产品入库明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(inboundItemService.deleteWithValidByIds(List.of(itemIds), true));
    }
}
