package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.service.IInventoryService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * WMS 库存控制器
 * 管理仓库库存的查询、调整、冻结、释放等核心功能，支持批次管理和FIFO算法
 * 提供库存统计、预警、追溯等高级库存管理功能
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/inventory")
public class InventoryController extends BaseController {

    private final IInventoryService inventoryService;

    /**
     * 查询产品库存列表
     */
    @SaCheckPermission("wms:inventory:list")
    @GetMapping("/list")
    public TableDataInfo<InventoryVo> list(InventoryBo bo, PageQuery pageQuery) {
        return inventoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品库存列表
     */
    @SaCheckPermission("wms:inventory:export")
    @Log(title = "产品库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InventoryBo bo, HttpServletResponse response) {
        List<InventoryVo> list = inventoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品库存", InventoryVo.class, response);
    }

    /**
     * 获取产品库存详细信息
     *
     * @param inventoryId 主键
     */
    @SaCheckPermission("wms:inventory:query")
    @GetMapping("/{inventoryId}")
    public R<InventoryVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long inventoryId) {
        return R.ok(inventoryService.queryById(inventoryId));
    }

    /**
     * 新增产品库存
     */
    @SaCheckPermission("wms:inventory:add")
    @Log(title = "产品库存", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InventoryBo bo) {
        return toAjax(inventoryService.insertByBo(bo));
    }

    /**
     * 修改产品库存
     */
    @SaCheckPermission("wms:inventory:edit")
    @Log(title = "产品库存", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InventoryBo bo) {
        return toAjax(inventoryService.updateByBo(bo));
    }

    @SaCheckPermission("wms:inventory:remove")
    @Log(title = "产品库存", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inventoryIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] inventoryIds) {
        return toAjax(inventoryService.deleteWithValidByIds(List.of(inventoryIds), true));
    }

    // TODO: [库存调整接口] - 优先级: HIGH - 参考文档: docs/design/README_OVERVIEW.md
    // 需要添加库存调整相关接口：
    // 1. 库存调整: POST /adjust - 手工调整库存数量，支持增加和减少
    // 2. 批次调整: POST /adjustBatch - 按批次调整库存
    // 3. 库存冻结: POST /freeze/{inventoryId} - 冻结指定库存
    // 4. 库存释放: POST /unfreeze/{inventoryId} - 释放冻结的库存
    // 实现思路：调用 InventoryService 的对应方法，记录调整日志

    // TODO: [库存查询接口] - 优先级: MEDIUM - 参考文档: docs/design/README_OVERVIEW.md
    // 需要添加高级查询接口：
    // 1. 可用库存查询: GET /available - 查询可用库存（排除冻结、预留等）
    // 2. 批次库存查询: GET /batches - 按批次查询库存，支持FIFO排序
    // 3. 库存汇总: GET /summary - 按产品、库位等维度汇总库存
    // 4. 库存预警: GET /alerts - 查询库存预警信息（低库存、过期等）
    // 实现思路：使用聚合查询，返回统计数据和预警信息

    // TODO: [库存追溯接口] - 优先级: LOW - 参考文档: docs/design/README_OVERVIEW.md
    // 需要添加库存追溯接口：
    // 1. 库存历史: GET /history/{inventoryId} - 查询库存变动历史
    // 2. 批次追溯: GET /batchTrace/{batchNumber} - 追溯批次的完整生命周期
    // 3. 库存来源: GET /source/{inventoryId} - 查询库存的来源单据
    // 4. 库存去向: GET /destination/{inventoryId} - 查询库存的去向单据
    // 实现思路：基于库存日志表进行追溯查询，提供完整的数据链路

}
