package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.service.IInventoryService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品库存
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/inventory")
public class InventoryController extends BaseController {

    private final IInventoryService inventoryService;

    /**
     * 查询产品库存列表
     */
    @SaCheckPermission("wms:inventory:list")
    @GetMapping("/list")
    public TableDataInfo<InventoryVo> list(InventoryBo bo, PageQuery pageQuery) {
        return inventoryService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品库存列表
     */
    @SaCheckPermission("wms:inventory:export")
    @Log(title = "产品库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(InventoryBo bo, HttpServletResponse response) {
        List<InventoryVo> list = inventoryService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品库存", InventoryVo.class, response);
    }

    /**
     * 获取产品库存详细信息
     *
     * @param inventoryId 主键
     */
    @SaCheckPermission("wms:inventory:query")
    @GetMapping("/{inventoryId}")
    public R<InventoryVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long inventoryId) {
        return R.ok(inventoryService.queryById(inventoryId));
    }

    /**
     * 新增产品库存
     */
    @SaCheckPermission("wms:inventory:add")
    @Log(title = "产品库存", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody InventoryBo bo) {
        return toAjax(inventoryService.insertByBo(bo));
    }

    /**
     * 修改产品库存
     */
    @SaCheckPermission("wms:inventory:edit")
    @Log(title = "产品库存", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody InventoryBo bo) {
        return toAjax(inventoryService.updateByBo(bo));
    }

    /**
     * 删除产品库存
     *
     * @param inventoryIds 主键串
     */
    @SaCheckPermission("wms:inventory:remove")
    @Log(title = "产品库存", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inventoryIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] inventoryIds) {
        return toAjax(inventoryService.deleteWithValidByIds(List.of(inventoryIds), true));
    }
}
