package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.OutboundBo;
import com.iotlaser.spms.wms.domain.bo.OutboundItemBo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import com.iotlaser.spms.wms.domain.vo.OutboundVo;
import com.iotlaser.spms.wms.service.IOutboundService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * WMS 出库控制器
 * 管理仓库出库单的完整生命周期，包括出库单创建、拣货、发货、完成以及与ERP系统的集成
 * 支持多种出库类型：销售出库、生产领料、调拨出库、盘亏出库等
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/outbound")
public class OutboundController extends BaseController {

    private final IOutboundService outboundService;

    /**
     * 查询产品出库列表
     */
    @SaCheckPermission("wms:outbound:list")
    @GetMapping("/list")
    public TableDataInfo<OutboundVo> list(OutboundBo bo, PageQuery pageQuery) {
        return outboundService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品出库列表
     */
    @SaCheckPermission("wms:outbound:export")
    @Log(title = "产品出库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OutboundBo bo, HttpServletResponse response) {
        List<OutboundVo> list = outboundService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品出库", OutboundVo.class, response);
    }

    /**
     * 获取产品出库详细信息
     *
     * @param outboundId 主键
     */
    @SaCheckPermission("wms:outbound:query")
    @GetMapping("/{outboundId}")
    public R<OutboundVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long outboundId) {
        return R.ok(outboundService.queryById(outboundId));
    }

    /**
     * 新增产品出库
     */
    @SaCheckPermission("wms:outbound:add")
    @Log(title = "产品出库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<OutboundVo> add(@Validated(AddGroup.class) @RequestBody OutboundBo bo) {
        return R.ok(outboundService.insertByBo(bo));
    }

    /**
     * 修改产品出库
     */
    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "产品出库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<OutboundVo> edit(@Validated(EditGroup.class) @RequestBody OutboundBo bo) {
        return R.ok(outboundService.updateByBo(bo));
    }

    /**
     * 删除产品出库
     *
     * @param outboundIds 主键串
     */
    @SaCheckPermission("wms:outbound:remove")
    @Log(title = "产品出库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{outboundIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] outboundIds) {
        return toAjax(outboundService.deleteWithValidByIds(List.of(outboundIds), true));
    }


    /**
     * 获取产品出库明细表以及关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wms:outboundItem:query")
    @GetMapping("item/{id}")
    public R<OutboundItemVo> queryItemById(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(outboundService.queryItemById(id));
    }

    /**
     * 查询产品出库明细表列表以及关联详细信息
     */
    @SaCheckPermission("wms:outboundItem:list")
    @GetMapping("item/list")
    public TableDataInfo<OutboundItemVo> queryItemPageList(OutboundItemBo bo, PageQuery pageQuery) {
        return outboundService.queryItemPageList(bo, pageQuery);
    }


    /**
     * 确认出库单
     */
    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "确认出库单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("confirm/{outboundId}")
    public R<Void> confirm(@NotNull(message = "主键不能为空") @PathVariable Long outboundId) {
        return toAjax(outboundService.confirmOutbound(outboundId));
    }

    /**
     * 批量确认出库单
     *
     * @param outboundIds 出库ID数组
     */
    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "出库单批量确认", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "出库单ID不能为空") @RequestBody Long[] outboundIds) {
        return toAjax(outboundService.batchConfirmOutbounds(List.of(outboundIds)));
    }

    /**
     * 取消出库单
     *
     * @param outboundId 出库ID
     * @param reason     取消原因
     */
    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "出库单取消", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{outboundId}")
    public R<Void> cancel(@NotNull(message = "出库ID不能为空") @PathVariable Long outboundId, @RequestParam(required = false) String reason) {
        return toAjax(outboundService.cancelOutbound(outboundId, reason));
    }

    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "完成出库单", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{outboundId}")
    public R<Void> complete(@NotNull(message = "出库ID不能为空") @PathVariable Long outboundId) {
        return toAjax(outboundService.completeOutbound(outboundId));
    }

    // TODO: [WMS出库状态流转接口] - 优先级: HIGH - 参考文档: docs/design/README_STATE.md
    // 需要添加以下状态流转接口：
    // 1. 开始拣货: POST /startPicking/{outboundId} - 将状态从 PENDING_PICKING 转为 PICKING_IN_PROGRESS
    // 2. 暂停拣货: POST /pausePicking/{outboundId} - 暂停拣货操作，记录暂停原因
    // 3. 恢复拣货: POST /resumePicking/{outboundId} - 恢复拣货操作
    // 4. 拣货完成: POST /finishPicking/{outboundId} - 将状态转为 PICKED
    // 5. 开始发货: POST /startShipping/{outboundId} - 将状态转为 SHIPPED
    // 实现思路：每个接口包含状态校验、业务逻辑和日志记录

    // TODO: [WMS出库批量操作接口] - 优先级: MEDIUM - 参考文档: docs/design/README_FLOW.md
    // 需要添加批量操作接口：
    // 1. 批量开始拣货: POST /batchStartPicking - 批量开始多个出库单的拣货
    // 2. 批量完成出库: POST /batchComplete - 批量完成多个出库单
    // 3. 批量取消出库: POST /batchCancel - 批量取消多个出库单
    // 实现思路：接收出库单ID列表，循环调用单个操作方法，支持部分成功的结果返回

    // TODO: [WMS出库波次管理接口] - 优先级: LOW - 参考文档: docs/design/README_OVERVIEW.md
    // 需要添加波次管理接口：
    // 1. 创建拣货波次: POST /createPickingWave - 将多个出库单组合成拣货波次
    // 2. 波次拣货: POST /wavePickingComplete/{waveId} - 完成整个波次的拣货
    // 3. 波次统计: GET /waveStatistics/{waveId} - 获取波次的拣货统计信息
    // 实现思路：优化拣货效率，减少拣货人员的行走路径

}
