package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.OutboundBo;
import com.iotlaser.spms.wms.domain.bo.OutboundItemBo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import com.iotlaser.spms.wms.domain.vo.OutboundVo;
import com.iotlaser.spms.wms.service.IOutboundService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品出库
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/outbound")
public class OutboundController extends BaseController {

    private final IOutboundService outboundService;

    /**
     * 查询产品出库列表
     */
    @SaCheckPermission("wms:outbound:list")
    @GetMapping("/list")
    public TableDataInfo<OutboundVo> list(OutboundBo bo, PageQuery pageQuery) {
        return outboundService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品出库列表
     */
    @SaCheckPermission("wms:outbound:export")
    @Log(title = "产品出库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OutboundBo bo, HttpServletResponse response) {
        List<OutboundVo> list = outboundService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品出库", OutboundVo.class, response);
    }

    /**
     * 获取产品出库详细信息
     *
     * @param outboundId 主键
     */
    @SaCheckPermission("wms:outbound:query")
    @GetMapping("/{outboundId}")
    public R<OutboundVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long outboundId) {
        return R.ok(outboundService.queryById(outboundId));
    }

    /**
     * 新增产品出库
     */
    @SaCheckPermission("wms:outbound:add")
    @Log(title = "产品出库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<OutboundVo> add(@Validated(AddGroup.class) @RequestBody OutboundBo bo) {
        return R.ok(outboundService.insertByBo(bo));
    }

    /**
     * 修改产品出库
     */
    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "产品出库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<OutboundVo> edit(@Validated(EditGroup.class) @RequestBody OutboundBo bo) {
        return R.ok(outboundService.updateByBo(bo));
    }

    /**
     * 删除产品出库
     *
     * @param outboundIds 主键串
     */
    @SaCheckPermission("wms:outbound:remove")
    @Log(title = "产品出库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{outboundIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] outboundIds) {
        return toAjax(outboundService.deleteWithValidByIds(List.of(outboundIds), true));
    }


    /**
     * 获取产品出库明细表以及关联详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("wms:outboundItem:query")
    @GetMapping("item/{id}")
    public R<OutboundItemVo> queryItemById(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(outboundService.queryItemById(id));
    }

    /**
     * 查询产品出库明细表列表以及关联详细信息
     */
    @SaCheckPermission("wms:outboundItem:list")
    @GetMapping("item/list")
    public TableDataInfo<OutboundItemVo> queryItemPageList(OutboundItemBo bo, PageQuery pageQuery) {
        return outboundService.queryItemPageList(bo, pageQuery);
    }


    /**
     * 确认出库单
     */
    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "确认出库单", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("confirm/{outboundId}")
    public R<Void> confirm(@NotNull(message = "主键不能为空") @PathVariable Long outboundId) {
        return toAjax(outboundService.confirmOutbound(outboundId));
    }

    /**
     * 批量确认出库单
     *
     * @param outboundIds 出库ID数组
     */
    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "出库单批量确认", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public R<Void> batchConfirm(@NotEmpty(message = "出库单ID不能为空") @RequestBody Long[] outboundIds) {
        return toAjax(outboundService.batchConfirmOutbounds(List.of(outboundIds)));
    }

    /**
     * 取消出库单
     *
     * @param outboundId 出库ID
     * @param reason     取消原因
     */
    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "出库单取消", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{outboundId}")
    public R<Void> cancel(@NotNull(message = "出库ID不能为空") @PathVariable Long outboundId, @RequestParam(required = false) String reason) {
        return toAjax(outboundService.cancelOutbound(outboundId, reason));
    }

    /**
     * 完成出库单
     *
     * @param outboundId 出库ID
     */
    @SaCheckPermission("wms:outbound:edit")
    @Log(title = "出库单取消", businessType = BusinessType.UPDATE)
    @PostMapping("/complete/{outboundId}")
    public R<Void> complete(@NotNull(message = "出库ID不能为空") @PathVariable Long outboundId) {
        return toAjax(outboundService.completeOutbound(outboundId));
    }


}
