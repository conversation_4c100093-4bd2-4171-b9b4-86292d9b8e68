package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.OutboundItemBo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import com.iotlaser.spms.wms.service.IOutboundItemService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品出库明细
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/outboundItem")
public class OutboundItemController extends BaseController {

    private final IOutboundItemService outboundItemService;

    /**
     * 查询产品出库明细列表
     */
    @SaCheckPermission("wms:outboundItem:list")
    @GetMapping("/list")
    public TableDataInfo<OutboundItemVo> list(OutboundItemBo bo, PageQuery pageQuery) {
        return outboundItemService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品出库明细列表
     */
    @SaCheckPermission("wms:outboundItem:export")
    @Log(title = "产品出库明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OutboundItemBo bo, HttpServletResponse response) {
        List<OutboundItemVo> list = outboundItemService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品出库明细", OutboundItemVo.class, response);
    }

    /**
     * 获取产品出库明细详细信息
     *
     * @param itemId 主键
     */
    @SaCheckPermission("wms:outboundItem:query")
    @GetMapping("/{itemId}")
    public R<OutboundItemVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long itemId) {
        return R.ok(outboundItemService.queryById(itemId));
    }

    /**
     * 新增产品出库明细
     */
    @SaCheckPermission("wms:outboundItem:add")
    @Log(title = "产品出库明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("insertOrUpdateBatch")
    public R<Void> insertOrUpdateBatch(@Validated(AddGroup.class) @RequestBody List<OutboundItemBo> bos) {
        return toAjax(outboundItemService.insertOrUpdateBatch(bos));
    }

    /**
     * 新增产品出库明细
     */
    @SaCheckPermission("wms:outboundItem:add")
    @Log(title = "产品出库明细", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OutboundItemBo bo) {
        return toAjax(outboundItemService.insertByBo(bo));
    }

    /**
     * 修改产品出库明细
     */
    @SaCheckPermission("wms:outboundItem:edit")
    @Log(title = "产品出库明细", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OutboundItemBo bo) {
        return toAjax(outboundItemService.updateByBo(bo));
    }

    /**
     * 删除产品出库明细
     *
     * @param itemIds 主键串
     */
    @SaCheckPermission("wms:outboundItem:remove")
    @Log(title = "产品出库明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{itemIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] itemIds) {
        return toAjax(outboundItemService.deleteWithValidByIds(List.of(itemIds), true));
    }

}
