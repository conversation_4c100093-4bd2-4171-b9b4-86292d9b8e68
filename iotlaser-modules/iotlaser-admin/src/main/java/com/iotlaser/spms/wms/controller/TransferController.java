package com.iotlaser.spms.wms.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.iotlaser.spms.wms.domain.bo.TransferBo;
import com.iotlaser.spms.wms.domain.vo.TransferVo;
import com.iotlaser.spms.wms.service.ITransferService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品移库
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/spms/wms/transfer")
public class TransferController extends BaseController {

    private final ITransferService transferService;

    /**
     * 查询产品移库列表
     */
    @SaCheckPermission("wms:transfer:list")
    @GetMapping("/list")
    public TableDataInfo<TransferVo> list(TransferBo bo, PageQuery pageQuery) {
        return transferService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出产品移库列表
     */
    @SaCheckPermission("wms:transfer:export")
    @Log(title = "产品移库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TransferBo bo, HttpServletResponse response) {
        List<TransferVo> list = transferService.queryList(bo);
        ExcelUtil.exportExcel(list, "产品移库", TransferVo.class, response);
    }

    /**
     * 获取产品移库详细信息
     *
     * @param transferId 主键
     */
    @SaCheckPermission("wms:transfer:query")
    @GetMapping("/{transferId}")
    public R<TransferVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long transferId) {
        return R.ok(transferService.queryById(transferId));
    }

    /**
     * 新增产品移库
     */
    @SaCheckPermission("wms:transfer:add")
    @Log(title = "产品移库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<TransferVo> add(@Validated(AddGroup.class) @RequestBody TransferBo bo) {
        return R.ok(transferService.insertByBo(bo));
    }

    /**
     * 修改产品移库
     */
    @SaCheckPermission("wms:transfer:edit")
    @Log(title = "产品移库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<TransferVo> edit(@Validated(EditGroup.class) @RequestBody TransferBo bo) {
        return R.ok(transferService.updateByBo(bo));
    }

    /**
     * 删除产品移库
     *
     * @param transferIds 主键串
     */
    @SaCheckPermission("wms:transfer:remove")
    @Log(title = "产品移库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{transferIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] transferIds) {
        return toAjax(transferService.deleteWithValidByIds(List.of(transferIds), true));
    }
}
