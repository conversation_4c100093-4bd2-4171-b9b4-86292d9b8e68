package com.iotlaser.spms.wms.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.InboundStatus;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 产品入库对象 wms_inbound
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_inbound")
public class Inbound extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 入库单ID
     */
    @TableId(value = "inbound_id")
    private Long inboundId;

    /**
     * 入库单编号
     */
    private String inboundCode;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编码
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 入库时间
     */
    private LocalDateTime inboundTime;

    /**
     * 入库状态
     */
    private InboundStatus inboundStatus;

    /**
     * 仓库操作员ID
     */
    private Long operatorId;

    /**
     * 仓库操作员
     */
    private String operatorName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
