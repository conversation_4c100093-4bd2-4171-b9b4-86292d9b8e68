package com.iotlaser.spms.wms.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.InventoryManagementType;
import com.iotlaser.spms.wms.enums.InventoryStatus;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品库存对象 wms_inventory
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_inventory")
public class Inventory extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 库存ID
     */
    @TableId(value = "inventory_id")
    private Long inventoryId;

    /**
     * 管理方式
     */
    private InventoryManagementType managementType;

    /**
     * 内部批次号
     */
    private String internalBatchNumber;

    /**
     * 供应商批次编号
     */
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    private String serialNumber;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编号
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 上游批次ID
     */
    private Long directSourceBatchId;

    /**
     * 上游明细ID
     */
    private Long directSourceItemId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 不含税成本单价
     */
    private BigDecimal costPrice;

    /**
     * 库存时间
     */
    private LocalDateTime inventoryTime;

    /**
     * 失效时间
     */
    private LocalDateTime expiryTime;

    /**
     * 库存状态
     */
    private InventoryStatus inventoryStatus;

    /**
     * 最后一次记录ID
     */
    private Long lastLogId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
