package com.iotlaser.spms.wms.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.OutboundStatus;
import com.iotlaser.spms.wms.enums.SourceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 产品出库对象 wms_outbound
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_outbound")
public class Outbound extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 出库单ID
     */
    @TableId(value = "outbound_id")
    private Long outboundId;

    /**
     * 出库单编号
     */
    private String outboundCode;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编码
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 出库时间
     */
    private LocalDateTime outboundTime;

    /**
     * 出库状态
     */
    private OutboundStatus outboundStatus;

    /**
     * 拣货员ID
     */
    private Long pickerId;

    /**
     * 拣货员
     */
    private String pickerName;

    /**
     * 打包/复核员ID
     */
    private Long packerId;

    /**
     * 打包/复核员
     */
    private String packerName;

    /**
     * 发运员ID
     */
    private Long shipperId;

    /**
     * 发运员
     */
    private String shipperName;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
