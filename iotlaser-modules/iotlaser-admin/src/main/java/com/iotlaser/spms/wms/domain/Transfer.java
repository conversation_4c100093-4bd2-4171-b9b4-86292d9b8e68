package com.iotlaser.spms.wms.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.wms.enums.TransferStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 产品移库对象 wms_transfer
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_transfer")
public class Transfer extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 移库单ID
     */
    @TableId(value = "transfer_id")
    private Long transferId;

    /**
     * 移库单编号
     */
    private String transferCode;

    /**
     * 移库单名称
     */
    private String transferName;

    /**
     * 移库单类型
     */
    private String transferType;

    /**
     * 移库时间
     */
    private LocalDateTime transferTime;

    /**
     * 移库状态
     */
    private TransferStatus transferStatus;

    /**
     * 移库操作员ID
     */
    private Long operatorId;

    /**
     * 移库操作员
     */
    private String operatorName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;


}
