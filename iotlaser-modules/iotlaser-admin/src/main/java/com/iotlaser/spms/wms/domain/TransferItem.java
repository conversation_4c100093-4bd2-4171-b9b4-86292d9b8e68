package com.iotlaser.spms.wms.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.iotlaser.spms.pro.domain.Product;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;

/**
 * 产品移库明细对象 wms_transfer_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_transfer_item")
public class TransferItem extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @TableId(value = "item_id")
    private Long itemId;

    /**
     * 移库单ID
     */
    private Long transferId;

    /**
     * 库存ID
     */
    private Long inventoryId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 移出位置库位ID
     */
    private Long fromLocationId;

    /**
     * 移出位置库位编码
     */
    private String fromLocationCode;

    /**
     * 移出位置库位名称
     */
    private String fromLocationName;

    /**
     * 移入位置库位ID
     */
    private Long toLocationId;

    /**
     * 移入位置库位编码
     */
    private String toLocationCode;

    /**
     * 移入位置库位名称
     */
    private String toLocationName;

    /**
     * 计划移库数量
     */
    private BigDecimal quantity;

    /**
     * 实际移库数量
     */
    private BigDecimal finishQuantity;

    /**
     * 成本单价
     */
    private BigDecimal costPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 产品信息
     */
    @TableField(exist = false)
    private Product product;

    /**
     * 批次信息
     */
    @TableField(exist = false)
    private List<TransferItemBatch> batches;
}
