package com.iotlaser.spms.wms.domain;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品移库批次明细对象 wms_transfer_item_batch
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wms_transfer_item_batch")
public class TransferItemBatch extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品移库单批次ID
     */
    @TableId(value = "batch_id")
    private Long batchId;

    /**
     * 产品移库单明细ID
     */
    private Long itemId;

    /**
     * 产品移库单ID
     */
    private Long transferId;

    /**
     * 库存ID
     */
    private Long inventoryId;

    /**
     * 内部批次号/成品序列号
     */
    private String internalBatchNumber;

    /**
     * 供应商批次号
     */
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    private String serialNumber;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 移出位置库位ID
     */
    private Long fromLocationId;

    /**
     * 移出位置库位编码
     */
    private String fromLocationCode;

    /**
     * 移出位置库位名称
     */
    private String fromLocationName;

    /**
     * 移入位置库位ID
     */
    private Long toLocationId;

    /**
     * 移入位置库位编码
     */
    private String toLocationCode;

    /**
     * 移入位置库位名称
     */
    private String toLocationName;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 成本单价
     */
    private BigDecimal costPrice;

    /**
     * 生产时间
     */
    private LocalDateTime productionTime;

    /**
     * 失效时间
     */
    private LocalDateTime expiryTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;
}
