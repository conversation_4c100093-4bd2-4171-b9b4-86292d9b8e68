package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.Inbound;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.InboundStatus;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 产品入库业务对象 wms_inbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Inbound.class, reverseConvertGenerate = false)
public class InboundBo extends BaseEntity {

    /**
     * 入库单ID
     */
    private Long inboundId;

    /**
     * 入库单编号
     */
    @NotBlank(message = "入库单编号不能为空", groups = {EditGroup.class})
    private String inboundCode;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编码
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotNull(message = "源头类型不能为空", groups = {EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotNull(message = "上游类型不能为空", groups = {EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 入库时间
     */
    @NotNull(message = "入库时间不能为空", groups = {EditGroup.class})
    private LocalDateTime inboundTime;

    /**
     * 入库状态
     */
    @NotNull(message = "入库状态不能为空", groups = {EditGroup.class})
    private InboundStatus inboundStatus;

    /**
     * 仓库操作员ID
     */
    private Long operatorId;

    /**
     * 仓库操作员
     */
    private String operatorName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
