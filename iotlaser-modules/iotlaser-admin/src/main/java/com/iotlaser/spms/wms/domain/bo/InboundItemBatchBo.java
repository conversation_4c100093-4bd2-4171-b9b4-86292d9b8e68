package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.InboundItemBatch;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品入库批次明细业务对象 wms_inbound_item_batch
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InboundItemBatch.class, reverseConvertGenerate = false)
public class InboundItemBatchBo extends BaseEntity {

    /**
     * 产品入库单批次ID
     */
    @NotNull(message = "产品入库单批次ID不能为空", groups = {EditGroup.class})
    private Long batchId;

    /**
     * 产品入库单明细ID
     */
    @NotNull(message = "产品入库单明细ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long itemId;

    /**
     * 产品入库单ID
     */
    @NotNull(message = "产品入库单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long inboundId;

    /**
     * 库存ID
     */
    private Long inventoryId;

    /**
     * 内部批次号/成品序列号
     */
    @NotBlank(message = "内部批次号不能为空", groups = {EditGroup.class})
    private String internalBatchNumber;

    /**
     * 供应商批次号
     */
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    private String serialNumber;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    @NotNull(message = "位置库位不能为空", groups = {EditGroup.class})
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空", groups = {AddGroup.class, EditGroup.class})
    @DecimalMin(value = "0.00", message = "数量不能小于0")
    @DecimalMax(value = "*********.99", message = "数量不能大于*********.99")
    private BigDecimal quantity;

    /**
     * 单价(含税)
     */
    @NotNull(message = "单价(含税)不能为空", groups = {EditGroup.class})
    @DecimalMin(value = "0.00", message = "单价(含税)不能小于0")
    @DecimalMax(value = "*********.99", message = "单价(含税)不能大于*********.99")
    private BigDecimal price;

    /**
     * 单价(不含税)
     */
    @NotNull(message = "单价(不含税)不能为空", groups = {EditGroup.class})
    @DecimalMin(value = "0.00", message = "单价(不含税)不能小于0")
    @DecimalMax(value = "*********.99", message = "单价(不含税)不能大于*********.99")
    private BigDecimal priceExclusiveTax;

    /**
     * 金额(不含税)
     */
    @NotNull(message = "金额(含税)不能为空", groups = {EditGroup.class})
    @DecimalMin(value = "0.00", message = "金额(含税)不能小于0")
    @DecimalMax(value = "*********.99", message = "金额(含税)不能大于*********.99")
    private BigDecimal amount;

    /**
     * 金额(不含税)
     */
    @NotNull(message = "金额(不含税)不能为空", groups = {EditGroup.class})
    @DecimalMin(value = "0.00", message = "金额(不含税)不能小于0")
    @DecimalMax(value = "*********.99", message = "金额(不含税)不能大于*********.99")
    private BigDecimal amountExclusiveTax;

    /**
     * 税率
     */
    @NotNull(message = "税率不能为空", groups = {EditGroup.class})
    @DecimalMin(value = "0.00", message = "税率不能小于0")
    @DecimalMax(value = "100.00", message = "税率不能大于100")
    private BigDecimal taxRate;

    /**
     * 税额
     */
    @NotNull(message = "税额不能为空", groups = {EditGroup.class})
    @DecimalMin(value = "0.00", message = "税额不能小于0")
    @DecimalMax(value = "*********.99", message = "税额不能大于*********.99")
    private BigDecimal taxAmount;

    /**
     * 生产时间
     */
    private LocalDateTime productionTime;

    /**
     * 失效时间
     */
    private LocalDateTime expiryTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

}
