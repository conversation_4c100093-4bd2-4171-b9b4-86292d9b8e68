package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.Inventory;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.InventoryManagementType;
import com.iotlaser.spms.wms.enums.InventoryStatus;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品库存业务对象 wms_inventory
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Inventory.class, reverseConvertGenerate = false)
public class InventoryBo extends BaseEntity {

    /**
     * 库存ID
     */
    private Long inventoryId;

    /**
     * 管理方式
     */
    @NotNull(message = "管理方式不能为空", groups = {AddGroup.class, EditGroup.class})
    private InventoryManagementType managementType;

    /**
     * 内部批次号
     */
    private String internalBatchNumber;

    /**
     * 供应商批次编号
     */
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    private String serialNumber;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编号
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotNull(message = "源头类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotNull(message = "上游类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 上游批次ID
     */
    @NotNull(message = "上游批次ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceBatchId;

    /**
     * 上游明细ID
     */
    @NotNull(message = "上游明细ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceItemId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal quantity;

    /**
     * 不含税成本单价
     */
    private BigDecimal costPrice;

    /**
     * 库存时间
     */
    @NotNull(message = "库存时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private LocalDateTime inventoryTime;

    /**
     * 失效时间
     */
    private LocalDateTime expiryTime;

    /**
     * 库存状态
     */
    @NotNull(message = "库存状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private InventoryStatus inventoryStatus;

    /**
     * 最后一次记录ID
     */
    private Long lastLogId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
