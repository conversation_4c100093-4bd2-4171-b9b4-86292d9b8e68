package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.InventoryLog;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.InventoryDirection;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品库存日志业务对象 wms_inventory_log
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = InventoryLog.class, reverseConvertGenerate = false)
public class InventoryLogBo extends BaseEntity {

    /**
     * 日志ID
     */
    private Long logId;

    /**
     * 实例ID
     */
    private Long productInstanceId;

    /**
     * 库存ID
     */
    @NotNull(message = "库存ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long inventoryId;

    /**
     * 源头ID
     */
    @NotNull(message = "源头ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long sourceId;

    /**
     * 源头编号
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotBlank(message = "源头类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    @NotBlank(message = "上游编码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 上游明细ID
     */
    @NotNull(message = "上游明细ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceItemId;

    /**
     * 上游批次ID
     */
    @NotNull(message = "上游批次ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceBatchId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 方向
     */
    @NotNull(message = "方向不能为空", groups = {AddGroup.class, EditGroup.class})
    private InventoryDirection direction;

    /**
     * 之前数量
     */
    @NotNull(message = "之前数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal beforeQuantity;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal quantity;

    /**
     * 之后数量
     */
    @NotNull(message = "之后数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal afterQuantity;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 记录时间
     */
    @NotNull(message = "记录时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private LocalDateTime recordTime;

    /**
     * 原因代码
     */
    private String reasonCode;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
