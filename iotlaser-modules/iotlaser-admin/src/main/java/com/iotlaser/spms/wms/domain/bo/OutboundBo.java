package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.Outbound;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.OutboundStatus;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 产品出库业务对象 wms_outbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Outbound.class, reverseConvertGenerate = false)
public class OutboundBo extends BaseEntity {

    /**
     * 出库单ID
     */
    private Long outboundId;

    /**
     * 出库单编号
     */
    @NotBlank(message = "出库单编号不能为空", groups = {EditGroup.class})
    private String outboundCode;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编码
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    @NotNull(message = "源头类型不能为空", groups = {EditGroup.class})
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    @NotNull(message = "上游类型不能为空", groups = {EditGroup.class})
    private DirectSourceType directSourceType;

    /**
     * 出库时间
     */
    private LocalDateTime outboundTime;

    /**
     * 出库状态
     */
    @NotNull(message = "出库状态不能为空", groups = {EditGroup.class})
    private OutboundStatus outboundStatus;

    /**
     * 拣货员ID
     */
    private Long pickerId;

    /**
     * 拣货员
     */
    private String pickerName;

    /**
     * 打包/复核员ID
     */
    private Long packerId;

    /**
     * 打包/复核员
     */
    private String packerName;

    /**
     * 发运员ID
     */
    private Long shipperId;

    /**
     * 发运员
     */
    private String shipperName;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
