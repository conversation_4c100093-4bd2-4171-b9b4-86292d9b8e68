package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.OutboundItem;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 产品出库明细业务对象 wms_outbound_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OutboundItem.class, reverseConvertGenerate = false)
public class OutboundItemBo extends BaseEntity {

    /**
     * 明细ID
     */
    private Long itemId;

    /**
     * 出库单ID
     */
    @NotNull(message = "出库单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long outboundId;

    /**
     * 库存ID
     */
    @NotNull(message = "库存ID不能为空", groups = { EditGroup.class})
    private Long inventoryId;

    /**
     * 源头ID
     */
    private Long sourceId;

    /**
     * 源头编码
     */
    private String sourceCode;

    /**
     * 源头类型
     */
    private SourceType sourceType;

    /**
     * 上游ID
     */
    private Long directSourceId;

    /**
     * 上游编码
     */
    private String directSourceCode;

    /**
     * 上游类型
     */
    private DirectSourceType directSourceType;

    /**
     * 上游明细ID
     */
    @NotNull(message = "上游明细ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long directSourceItemId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 位置库位ID
     */
    private Long locationId;

    /**
     * 位置库位编码
     */
    private String locationCode;

    /**
     * 位置库位名称
     */
    private String locationName;

    /**
     * 通知出库数量
     */
    @NotNull(message = "通知出库数量不能为空", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal quantity;

    /**
     * 已拣货数量
     */
    private BigDecimal finishQuantity;

    /**
     * 成本单价
     */
    private BigDecimal costPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
