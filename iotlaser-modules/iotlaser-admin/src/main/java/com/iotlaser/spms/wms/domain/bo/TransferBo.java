package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.Transfer;
import com.iotlaser.spms.wms.enums.TransferStatus;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 产品移库业务对象 wms_transfer
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Transfer.class, reverseConvertGenerate = false)
public class TransferBo extends BaseEntity {

    /**
     * 移库单ID
     */
    private Long transferId;

    /**
     * 移库单编号
     */
    @NotBlank(message = "移库单编号不能为空", groups = {EditGroup.class})
    private String transferCode;

    /**
     * 移库单名称
     */
    @NotBlank(message = "移库单名称不能为空", groups = {EditGroup.class})
    private String transferName;

    /**
     * 移库单类型
     */
    @NotNull(message = "移库单类型不能为空", groups = {EditGroup.class})
    private String transferType;

    /**
     * 移库时间
     */
    private LocalDateTime transferTime;

    /**
     * 移库状态
     */
    @NotNull(message = "移库状态不能为空", groups = {EditGroup.class})
    private TransferStatus transferStatus;

    /**
     * 移库操作员ID
     */
    private Long operatorId;

    /**
     * 移库操作员
     */
    private String operatorName;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
