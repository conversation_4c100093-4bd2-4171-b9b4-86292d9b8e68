package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.TransferItemBatch;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品移库批次明细业务对象 wms_transfer_item_batch
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TransferItemBatch.class, reverseConvertGenerate = false)
public class TransferItemBatchBo extends BaseEntity {

    /**
     * 产品移库单批次ID
     */
    @NotNull(message = "产品移库单批次ID不能为空", groups = {EditGroup.class})
    private Long batchId;

    /**
     * 产品移库单明细ID
     */
    @NotNull(message = "产品移库单明细ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long itemId;

    /**
     * 产品移库单ID
     */
    @NotNull(message = "产品移库单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long transferId;

    /**
     * 库存ID
     */
    @NotNull(message = "库存ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long inventoryId;

    /**
     * 内部批次号/成品序列号
     */
    private String internalBatchNumber;

    /**
     * 供应商批次号
     */
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    private String serialNumber;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 移出位置库位ID
     */
    @NotNull(message = "移出位置库位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long fromLocationId;

    /**
     * 移出位置库位编码
     */
    private String fromLocationCode;

    /**
     * 移出位置库位名称
     */
    private String fromLocationName;

    /**
     * 移入位置库位ID
     */
    @NotNull(message = "移入位置库位ID不能为空", groups = {EditGroup.class})
    private Long toLocationId;

    /**
     * 移入位置库位编码
     */
    private String toLocationCode;

    /**
     * 移入位置库位名称
     */
    private String toLocationName;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 成本单价
     */
    private BigDecimal costPrice;

    /**
     * 生产时间
     */
    private LocalDateTime productionTime;

    /**
     * 失效时间
     */
    private LocalDateTime expiryTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;

}
