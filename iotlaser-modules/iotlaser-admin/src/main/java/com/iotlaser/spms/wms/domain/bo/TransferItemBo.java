package com.iotlaser.spms.wms.domain.bo;

import com.iotlaser.spms.wms.domain.TransferItem;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 产品移库明细业务对象 wms_transfer_item
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = TransferItem.class, reverseConvertGenerate = false)
public class TransferItemBo extends BaseEntity {

    /**
     * 明细ID
     */
    private Long itemId;

    /**
     * 移库单ID
     */
    @NotNull(message = "移库单ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long transferId;

    /**
     * 库存ID
     */
    @NotNull(message = "库存ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long inventoryId;

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long productId;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 计量单位ID
     */
    @NotNull(message = "计量单位ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long unitId;

    /**
     * 计量单位编码
     */
    private String unitCode;

    /**
     * 计量单位名称
     */
    private String unitName;

    /**
     * 移出位置库位ID
     */
    private Long fromLocationId;

    /**
     * 移出位置库位编码
     */
    private String fromLocationCode;

    /**
     * 移出位置库位名称
     */
    private String fromLocationName;

    /**
     * 移入位置库位ID
     */
    private Long toLocationId;

    /**
     * 移入位置库位编码
     */
    private String toLocationCode;

    /**
     * 移入位置库位名称
     */
    private String toLocationName;

    /**
     * 计划移库数量
     */
    @NotNull(message = "计划移库数量不能为空", groups = {EditGroup.class})
    private BigDecimal quantity;

    /**
     * 实际移库数量
     */
    private BigDecimal finishQuantity;

    /**
     * 成本单价
     */
    private BigDecimal costPrice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效状态
     */
    private String status;


}
