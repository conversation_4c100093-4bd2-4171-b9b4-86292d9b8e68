package com.iotlaser.spms.wms.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.wms.domain.InventoryLog;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.InventoryDirection;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 产品库存日志视图对象 wms_inventory_log
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = InventoryLog.class)
public class InventoryLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    @ExcelProperty(value = "日志ID")
    private Long logId;

    /**
     * 实例ID
     */
    @ExcelProperty(value = "实例ID")
    private Long productInstanceId;

    /**
     * 库存ID
     */
    @ExcelProperty(value = "库存ID")
    private Long inventoryId;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编号
     */
    @ExcelProperty(value = "源头编号")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_inventory_batch_order_type")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_inventory_log_direct_order_type")
    private DirectSourceType directSourceType;

    /**
     * 上游明细ID
     */
    @ExcelProperty(value = "上游明细ID")
    private Long directSourceItemId;

    /**
     * 上游批次ID
     */
    @ExcelProperty(value = "上游批次ID")
    private Long directSourceBatchId;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 位置库位ID
     */
    @ExcelProperty(value = "位置库位ID")
    private Long locationId;

    /**
     * 位置库位编码
     */
    @ExcelProperty(value = "位置库位编码")
    private String locationCode;

    /**
     * 位置库位名称
     */
    @ExcelProperty(value = "位置库位名称")
    private String locationName;

    /**
     * 方向
     */
    @ExcelProperty(value = "方向")
    private InventoryDirection direction;

    /**
     * 之前数量
     */
    @ExcelProperty(value = "之前数量")
    private BigDecimal beforeQuantity;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 之后数量
     */
    @ExcelProperty(value = "之后数量")
    private BigDecimal afterQuantity;

    /**
     * 价格
     */
    @ExcelProperty(value = "价格")
    private BigDecimal price;

    /**
     * 记录时间
     */
    @ExcelProperty(value = "记录时间")
    private LocalDateTime recordTime;

    /**
     * 原因代码
     */
    @ExcelProperty(value = "原因代码")
    private String reasonCode;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
