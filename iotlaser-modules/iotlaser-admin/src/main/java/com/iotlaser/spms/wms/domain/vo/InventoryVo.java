package com.iotlaser.spms.wms.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.wms.domain.Inventory;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.InventoryManagementType;
import com.iotlaser.spms.wms.enums.InventoryStatus;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 产品库存视图对象 wms_inventory
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Inventory.class)
public class InventoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 库存ID
     */
    @ExcelProperty(value = "库存ID")
    private Long inventoryId;

    /**
     * 管理方式
     */
    @ExcelProperty(value = "管理方式", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_inventory_batch_management_type")
    private InventoryManagementType managementType;

    /**
     * 内部批次号
     */
    @ExcelProperty(value = "内部批次号")
    private String internalBatchNumber;

    /**
     * 供应商批次编号
     */
    @ExcelProperty(value = "供应商批次编号")
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    @ExcelProperty(value = "单品序列号")
    private String serialNumber;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编号
     */
    @ExcelProperty(value = "源头编号")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_inventory_batch_order_type")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_inventory_batch_direct_order_type")
    private DirectSourceType directSourceType;

    /**
     * 上游批次ID
     */
    @ExcelProperty(value = "上游批次ID")
    private Long directSourceBatchId;

    /**
     * 上游明细ID
     */
    @ExcelProperty(value = "上游明细ID")
    private Long directSourceItemId;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 位置库位ID
     */
    @ExcelProperty(value = "位置库位ID")
    private Long locationId;

    /**
     * 位置库位编码
     */
    @ExcelProperty(value = "位置库位编码")
    private String locationCode;

    /**
     * 位置库位名称
     */
    @ExcelProperty(value = "位置库位名称")
    private String locationName;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 不含税成本单价
     */
    @ExcelProperty(value = "不含税成本单价")
    private BigDecimal costPrice;

    /**
     * 库存时间
     */
    @ExcelProperty(value = "库存时间")
    private LocalDateTime inventoryTime;

    /**
     * 失效时间
     */
    @ExcelProperty(value = "失效时间")
    private LocalDateTime expiryTime;

    /**
     * 库存状态
     */
    @ExcelProperty(value = "库存状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_inventory_batch_status")
    private InventoryStatus inventoryStatus;

    /**
     * 最后一次记录ID
     */
    @ExcelProperty(value = "最后一次记录ID")
    private Long lastLogId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


}
