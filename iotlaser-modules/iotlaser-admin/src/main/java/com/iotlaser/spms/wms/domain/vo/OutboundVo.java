package com.iotlaser.spms.wms.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.wms.domain.Outbound;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.OutboundStatus;
import com.iotlaser.spms.wms.enums.SourceType;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 产品出库视图对象 wms_outbound
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Outbound.class)
public class OutboundVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 出库单ID
     */
    @ExcelProperty(value = "出库单ID")
    private Long outboundId;

    /**
     * 出库单编号
     */
    @ExcelProperty(value = "出库单编号")
    private String outboundCode;

    /**
     * 源头ID
     */
    @ExcelProperty(value = "源头ID")
    private Long sourceId;

    /**
     * 源头编码
     */
    @ExcelProperty(value = "源头编码")
    private String sourceCode;

    /**
     * 源头类型
     */
    @ExcelProperty(value = "源头类型")
    private SourceType sourceType;

    /**
     * 上游ID
     */
    @ExcelProperty(value = "上游ID")
    private Long directSourceId;

    /**
     * 上游编码
     */
    @ExcelProperty(value = "上游编码")
    private String directSourceCode;

    /**
     * 上游类型
     */
    @ExcelProperty(value = "上游类型")
    private DirectSourceType directSourceType;

    /**
     * 出库时间
     */
    @ExcelProperty(value = "出库时间")
    private LocalDateTime outboundTime;

    /**
     * 出库状态
     */
    @ExcelProperty(value = "出库状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_outbound_status")
    private OutboundStatus outboundStatus;

    /**
     * 拣货员ID
     */
    @ExcelProperty(value = "拣货员ID")
    private Long pickerId;

    /**
     * 拣货员
     */
    @ExcelProperty(value = "拣货员")
    private String pickerName;

    /**
     * 打包/复核员ID
     */
    @ExcelProperty(value = "打包/复核员ID")
    private Long packerId;

    /**
     * 打包/复核员
     */
    @ExcelProperty(value = "打包/复核员")
    private String packerName;

    /**
     * 发运员ID
     */
    @ExcelProperty(value = "发运员ID")
    private Long shipperId;

    /**
     * 发运员
     */
    @ExcelProperty(value = "发运员")
    private String shipperName;

    /**
     * 完成时间
     */
    @ExcelProperty(value = "完成时间")
    private LocalDateTime completeTime;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


    private List<OutboundItemVo> items;
}
