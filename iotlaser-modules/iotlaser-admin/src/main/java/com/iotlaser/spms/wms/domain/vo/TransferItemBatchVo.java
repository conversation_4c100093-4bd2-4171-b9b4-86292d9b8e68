package com.iotlaser.spms.wms.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.wms.domain.TransferItemBatch;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品移库批次明细视图对象 wms_transfer_item_batch
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TransferItemBatch.class)
public class TransferItemBatchVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 产品移库单批次ID
     */
    @ExcelProperty(value = "产品移库单批次ID")
    private Long batchId;

    /**
     * 产品移库单明细ID
     */
    @ExcelProperty(value = "产品移库单明细ID")
    private Long itemId;

    /**
     * 产品移库单ID
     */
    @ExcelProperty(value = "产品移库单ID")
    private Long transferId;

    /**
     * 库存ID
     */
    @ExcelProperty(value = "库存ID")
    private Long inventoryId;

    /**
     * 内部批次号/成品序列号
     */
    @ExcelProperty(value = "内部批次号/成品序列号")
    private String internalBatchNumber;

    /**
     * 供应商批次号
     */
    @ExcelProperty(value = "供应商批次号")
    private String supplierBatchNumber;

    /**
     * 单品序列号
     */
    @ExcelProperty(value = "单品序列号")
    private String serialNumber;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 移出位置库位ID
     */
    @ExcelProperty(value = "移出位置库位ID")
    private Long fromLocationId;

    /**
     * 移出位置库位编码
     */
    @ExcelProperty(value = "移出位置库位编码")
    private String fromLocationCode;

    /**
     * 移出位置库位名称
     */
    @ExcelProperty(value = "移出位置库位名称")
    private String fromLocationName;

    /**
     * 移入位置库位ID
     */
    @ExcelProperty(value = "移入位置库位ID")
    private Long toLocationId;

    /**
     * 移入位置库位编码
     */
    @ExcelProperty(value = "移入位置库位编码")
    private String toLocationCode;

    /**
     * 移入位置库位名称
     */
    @ExcelProperty(value = "移入位置库位名称")
    private String toLocationName;

    /**
     * 数量
     */
    @ExcelProperty(value = "数量")
    private BigDecimal quantity;

    /**
     * 成本单价
     */
    @ExcelProperty(value = "成本单价")
    private BigDecimal costPrice;

    /**
     * 生产时间
     */
    @ExcelProperty(value = "生产时间")
    private LocalDateTime productionTime;

    /**
     * 失效时间
     */
    @ExcelProperty(value = "失效时间")
    private LocalDateTime expiryTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

}
