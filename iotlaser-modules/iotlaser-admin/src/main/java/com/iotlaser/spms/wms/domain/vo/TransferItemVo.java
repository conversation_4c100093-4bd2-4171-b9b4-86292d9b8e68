package com.iotlaser.spms.wms.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.pro.domain.vo.ProductVo;
import com.iotlaser.spms.wms.domain.TransferItem;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


/**
 * 产品移库明细视图对象 wms_transfer_item
 *
 * <AUTHOR> <PERSON>
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TransferItem.class)
public class TransferItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 明细ID
     */
    @ExcelProperty(value = "明细ID")
    private Long itemId;

    /**
     * 移库单ID
     */
    @ExcelProperty(value = "移库单ID")
    private Long transferId;

    /**
     * 库存ID
     */
    @ExcelProperty(value = "库存ID")
    private Long inventoryId;

    /**
     * 产品ID
     */
    @ExcelProperty(value = "产品ID")
    private Long productId;

    /**
     * 产品编码
     */
    @ExcelProperty(value = "产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位ID")
    private Long unitId;

    /**
     * 计量单位编码
     */
    @ExcelProperty(value = "计量单位编码")
    private String unitCode;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称")
    private String unitName;

    /**
     * 移出位置库位ID
     */
    @ExcelProperty(value = "移出位置库位ID")
    private Long fromLocationId;

    /**
     * 移出位置库位编码
     */
    @ExcelProperty(value = "移出位置库位编码")
    private String fromLocationCode;

    /**
     * 移出位置库位名称
     */
    @ExcelProperty(value = "移出位置库位名称")
    private String fromLocationName;

    /**
     * 移入位置库位ID
     */
    @ExcelProperty(value = "移入位置库位ID")
    private Long toLocationId;

    /**
     * 移入位置库位编码
     */
    @ExcelProperty(value = "移入位置库位编码")
    private String toLocationCode;

    /**
     * 移入位置库位名称
     */
    @ExcelProperty(value = "移入位置库位名称")
    private String toLocationName;

    /**
     * 计划移库数量
     */
    @ExcelProperty(value = "计划移库数量")
    private BigDecimal quantity;

    /**
     * 实际移库数量
     */
    @ExcelProperty(value = "实际移库数量")
    private BigDecimal finishQuantity;

    /**
     * 成本单价
     */
    @ExcelProperty(value = "成本单价")
    private BigDecimal costPrice;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;


    /**
     * 产品信息表 (ONE_TO_ONE)
     */
    private ProductVo product;


    /**
     * 产品移库批次明细表 (ONE_TO_MANY)
     */
    private List<TransferItemBatchVo> batches;


}
