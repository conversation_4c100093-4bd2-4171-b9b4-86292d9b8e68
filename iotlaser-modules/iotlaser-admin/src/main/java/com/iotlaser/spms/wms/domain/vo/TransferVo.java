package com.iotlaser.spms.wms.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.iotlaser.spms.wms.domain.Transfer;
import com.iotlaser.spms.wms.enums.TransferStatus;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 产品移库视图对象 wms_transfer
 *
 * <AUTHOR> Kai
 * @date 2025-07-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Transfer.class)
public class TransferVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 移库单ID
     */
    @ExcelProperty(value = "移库单ID")
    private Long transferId;

    /**
     * 移库单编号
     */
    @ExcelProperty(value = "移库单编号")
    private String transferCode;

    /**
     * 移库单名称
     */
    @ExcelProperty(value = "移库单名称")
    private String transferName;

    /**
     * 移库单类型
     */
    @ExcelProperty(value = "移库单类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_transfer_type")
    private String transferType;

    /**
     * 移库时间
     */
    @ExcelProperty(value = "移库时间")
    private LocalDateTime transferTime;

    /**
     * 移库状态
     */
    @ExcelProperty(value = "移库状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "wms_transfer_status")
    private TransferStatus transferStatus;

    /**
     * 移库操作员ID
     */
    @ExcelProperty(value = "移库操作员ID")
    private Long operatorId;

    /**
     * 移库操作员
     */
    @ExcelProperty(value = "移库操作员")
    private String operatorName;

    /**
     * 摘要
     */
    @ExcelProperty(value = "摘要")
    private String summary;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 有效状态
     */
    @ExcelProperty(value = "有效状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_data_status")
    private String status;

    /**
     * 明细
     */
    private List<TransferItemVo> items;

}
