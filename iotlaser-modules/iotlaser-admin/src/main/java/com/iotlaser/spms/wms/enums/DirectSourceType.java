package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存记录单上游类型枚举
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Getter
@AllArgsConstructor
public enum DirectSourceType implements IDictEnum<String> {

    SALE_ORDER("sale_order", "销售订单", "销售订单"),
    SALE_OUTBOUND("sale_outbound", "销售出库单", "销售订单出库单"),
    SALE_RETURN("sale_return", "销售退货单", "销售退货入库单"),

    PURCHASE_ORDER("purchase_order", "采购订单", "采购订单"),
    PURCHASE_INBOUND("purchase_inbound", "采购入库单", "采购入库单入库单"),
    PURCHASE_RETURN("purchase_return", "采购退货单", "采购退货出库单"),

    PRODUCTION_ORDER("production_order", "生产订单", "生产订单"),
    PRODUCTION_INBOUND("production_inbound", "生产入库单", "生产完工入库"),
    PRODUCTION_ISSUE("production_issue", "生产领料单", "生产领料出库单"),
    PRODUCTION_RETURN("production_return", "生产退料单", "生产退料入库单"),

    TRANSFER("transfer", "移库单", "移库单"),
    OUTBOUND("outbound", "出库单", "出库单"),
    INBOUND("inbound", "入库单", "入库单"),
    INVENTORY_INIT("inventory_init", "库存初始化", "库存初始化"),
    INVENTORY_TAKING("inventory_taking", "库存盘点", "库存盘点"),
    INVENTORY_ADJUSTMENT("inventory_adjustment", "库存调整", "库存调整"),
    ;

    public final static String DICT_CODE = "wms_direct_source_type";
    public final static String DICT_NAME = "直接来源类型";
    public final static String DICT_DESC = "定义库存变动的直接来源类型，包括采购入库、销售出库、生产领料等具体业务类型";
    /**
     * 类型值
     */
    @EnumValue
    private final String value;
    /**
     * 类型名称
     */
    private final String name;
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 类型值
     * @return 直接来源类型枚举
     */
    public static DirectSourceType getByValue(String value) {
        for (DirectSourceType sourceType : values()) {
            if (sourceType.getValue().equals(value)) {
                return sourceType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
