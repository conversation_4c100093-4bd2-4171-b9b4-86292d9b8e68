package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 入库状态枚举
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
@Getter
@AllArgsConstructor
public enum InboundStatus implements IDictEnum<String> {

    PENDING_RECEIPT("pending_receipt", "待收货", "等待收货"),
    PARTIALLY_RECEIVED("partially_received", "部分收货", "部分收货"),
    COMPLETED("completed", "已完成", "已完成"),
    CANCELLED("cancelled", "已取消", "已取消");

    public final static String DICT_CODE = "wms_inbound_status";
    public final static String DICT_NAME = "入库状态";
    public final static String DICT_DESC = "管理仓库入库单的流程状态，从草稿创建到入库完成的完整生命周期";

    /**
     * 状态值
     */
    @EnumValue
    private final String value;
    /**
     * 状态名称
     */
    private final String name;
    /**
     * 状态描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static InboundStatus getByValue(String value) {
        for (InboundStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
