package com.iotlaser.spms.wms.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.iotlaser.spms.core.dict.enums.IDictEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 库存管理方式枚举
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
@Getter
@AllArgsConstructor
public enum InventoryManagementType implements IDictEnum<String> {
    NONE("none", "正常管理", "按正常流程进行库存管理"),
    BATCH("batch", "批次管理", "按批次进行库存管理"),
    SERIALIZED_BATCH("serialized_batch", "序列号管理", "按序列号批次进行库存管理"),
    SERIALIZED_UNIQUE("serialized_unique", "唯一序列号管理", "按唯一序列号进行库存管理");

    public final static String DICT_CODE = "wms_inventory_management_type";
    public final static String DICT_NAME = "库存管理方式";
    public final static String DICT_DESC = "定义产品的库存管理方式，包括批次管理、序列号管理等不同的库存跟踪方式";
    /**
     * 管理类型值
     */
    @EnumValue
    private final String value;
    /**
     * 管理类型名称
     */
    private final String name;
    /**
     * 管理类型描述
     */
    private final String desc;

    /**
     * 根据值获取枚举
     *
     * @param value 管理类型值
     * @return 库存管理方式枚举
     */
    public static InventoryManagementType getByValue(String value) {
        for (InventoryManagementType managementType : values()) {
            if (managementType.getValue().equals(value)) {
                return managementType;
            }
        }
        return null;
    }

    @Override
    public String getDictCode() {
        return DICT_CODE;
    }

    @Override
    public String getDictName() {
        return DICT_NAME;
    }

    @Override
    public String getDictDesc() {
        return DICT_DESC;
    }
}
