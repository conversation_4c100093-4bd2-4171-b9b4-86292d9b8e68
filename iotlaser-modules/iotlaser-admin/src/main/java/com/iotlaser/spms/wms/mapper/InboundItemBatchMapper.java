package com.iotlaser.spms.wms.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.wms.domain.InboundItemBatch;
import com.iotlaser.spms.wms.domain.vo.InboundItemBatchVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.Collection;

/**
 * 产品入库批次明细Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
public interface InboundItemBatchMapper extends BaseMapperPlus<InboundItemBatch, InboundItemBatchVo> {

    /**
     * 查询采购入库明细批次数量
     */
    BigDecimal selectQuantitySumByItemId(@Param("itemId") Long itemId, @Param("batchId") Long batchId);

    /**
     * 删除采购入库明细表
     */
    default int deleteByInboundId(Long inboundId) {
        return delete(new LambdaQueryWrapper<InboundItemBatch>().eq(InboundItemBatch::getInboundId, inboundId));
    }

    /**
     * 删除采购入库明细表
     */
    default int deleteByItemIds(Collection<Long> itemIds) {
        return delete(new LambdaQueryWrapper<InboundItemBatch>().in(InboundItemBatch::getItemId, itemIds));
    }
}
