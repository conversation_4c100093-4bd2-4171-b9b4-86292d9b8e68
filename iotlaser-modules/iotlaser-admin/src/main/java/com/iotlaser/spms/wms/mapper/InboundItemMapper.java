package com.iotlaser.spms.wms.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.wms.domain.InboundItem;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 产品入库明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface InboundItemMapper extends BaseMapperPlus<InboundItem, InboundItemVo> {

    /**
     * 查询产品入库明细表及其关联信息
     */
    InboundItem queryById(@Param("itemId") Long itemId);

    /**
     * 分页查询产品入库明细表及其关联信息
     */
    List<InboundItem> queryPageList(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<InboundItem> wrapper);

}
