package com.iotlaser.spms.wms.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iotlaser.spms.wms.domain.InventoryLog;
import com.iotlaser.spms.wms.domain.vo.InventoryLogVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
 * 产品库存日志Mapper接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
public interface InventoryLogMapper extends BaseMapperPlus<InventoryLog, InventoryLogVo> {

    default Boolean existsBySourceId(Long sourceId) {
        return exists(new LambdaQueryWrapper<InventoryLog>().eq(InventoryLog::getSourceId, sourceId));
    }

    default Boolean existsByDirectSourceId(Long directSourceId) {
        return exists(new LambdaQueryWrapper<InventoryLog>().eq(InventoryLog::getDirectSourceId, directSourceId));
    }

    default Boolean existsByInventoryId(Long inventoryId) {
        return exists(new LambdaQueryWrapper<InventoryLog>().eq(InventoryLog::getInventoryId, inventoryId));
    }
}
