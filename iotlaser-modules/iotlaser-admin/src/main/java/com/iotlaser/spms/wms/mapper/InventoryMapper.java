package com.iotlaser.spms.wms.mapper;

import com.iotlaser.spms.wms.domain.Inventory;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.math.BigDecimal;
import java.util.List;

/**
 * 产品库存Mapper接口
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
public interface InventoryMapper extends BaseMapperPlus<Inventory, InventoryVo> {

    /**
     * 使用SELECT FOR UPDATE锁定可用批次（FIFO排序）
     * 用于并发控制，防止库存超卖
     *
     * @param productId  产品ID
     * @param locationId 库位ID
     * @return 锁定的可用批次列表
     */
    @Select("SELECT * FROM wms_inventory_batch " +
        "WHERE product_id = #{productId} " +
        "AND location_id = #{locationId} " +
        "AND inventory_status = 'AVAILABLE' " +
        "AND quantity > 0 " +
        "ORDER BY create_time ASC " +
        "FOR UPDATE")
    List<Inventory> selectAvailableBatchesForUpdate(@Param("productId") Long productId,
                                                    @Param("locationId") Long locationId);

    /**
     * 使用SELECT FOR UPDATE锁定指定批次
     * 用于并发控制，防止同时修改同一批次
     *
     * @param batchId 批次ID
     * @return 锁定的批次
     */
    @Select("SELECT * FROM wms_inventory_batch " +
        "WHERE batch_id = #{batchId} " +
        "FOR UPDATE")
    Inventory selectByIdForUpdate(@Param("batchId") Long batchId);

    /**
     * 增加库存原子操作
     * 用于并发控制，防止同时修改同一批次
     *
     * @param batchId 批次ID
     * @return 锁定的批次
     */
    @Update("UPDATE wms_inventory_batch SET quantity = quantity + #{amount} WHERE batch_id = #{batchId} ")
    int increaseQuantity(@Param("batchId") Long batchId, @Param("amount") BigDecimal amount);

    /**
     * 扣除库存原子操作
     * 用于并发控制，防止同时修改同一批次
     *
     * @param batchId 批次ID
     * @return 锁定的批次
     */
    @Update("UPDATE wms_inventory_batch SET quantity = quantity - #{amount} WHERE batch_id = #{batchId} ")
    int deductQuantity(@Param("batchId") Long batchId, @Param("amount") BigDecimal amount);

}
