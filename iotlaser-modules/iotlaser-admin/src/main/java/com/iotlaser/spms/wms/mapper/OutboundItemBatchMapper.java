package com.iotlaser.spms.wms.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.iotlaser.spms.wms.domain.OutboundItemBatch;
import com.iotlaser.spms.wms.domain.vo.OutboundItemBatchVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Collection;

/**
 * 产品出库批次明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025/05/10
 */
public interface OutboundItemBatchMapper extends BaseMapperPlus<OutboundItemBatch, OutboundItemBatchVo> {

    default int deleteByOutboundIds(Collection<Long> outboundIds) {
        return delete(new LambdaUpdateWrapper<OutboundItemBatch>().in(OutboundItemBatch::getOutboundId, outboundIds));
    }
}
