package com.iotlaser.spms.wms.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.wms.domain.OutboundItem;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Collection;
import java.util.List;

/**
 * 产品出库明细Mapper接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface OutboundItemMapper extends BaseMapperPlus<OutboundItem, OutboundItemVo> {


    /**
     * 查询产品出库明细表及其关联信息
     */
    OutboundItem queryById(@Param("itemId") Long itemId);

    /**
     * 分页查询产品出库明细表及其关联信息
     */
    List<OutboundItem> queryPageList(@Param("page") Page<Object> page, @Param(Constants.WRAPPER) QueryWrapper<OutboundItem> wrapper);


    default int deleteByOutboundIds(Collection<Long> outboundIds) {
        return delete(new QueryWrapper<OutboundItem>().lambda().in(OutboundItem::getOutboundId, outboundIds));
    }
}
