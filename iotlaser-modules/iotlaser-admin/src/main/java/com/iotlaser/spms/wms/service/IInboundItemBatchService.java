package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.bo.InboundItemBatchBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemBatchVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 产品入库批次明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
public interface IInboundItemBatchService {

    /**
     * 查询产品入库批次明细
     *
     * @param batchId 主键
     * @return 产品入库批次明细
     */
    InboundItemBatchVo queryById(Long batchId);

    /**
     * 分页查询产品入库批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库批次明细分页列表
     */
    TableDataInfo<InboundItemBatchVo> queryPageList(InboundItemBatchBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品入库批次明细列表
     *
     * @param bo 查询条件
     * @return 产品入库批次明细列表
     */
    List<InboundItemBatchVo> queryList(InboundItemBatchBo bo);

    /**
     * 新增产品入库批次明细
     *
     * @param bo 产品入库批次明细
     * @return 是否新增成功
     */
    Boolean insertByBo(InboundItemBatchBo bo);

    /**
     * 修改产品入库批次明细
     *
     * @param bo 产品入库批次明细
     * @return 是否修改成功
     */
    Boolean updateByBo(InboundItemBatchBo bo);

    /**
     * 校验并批量删除产品入库批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量新增产品入库批次明细
     * ✅ 修正：使用BO而非直接暴露Entity
     *
     * @param batches 批次BO列表
     * @return 是否成功
     */
    Boolean insertOrUpdateBatch(List<InboundItemBatchBo> batches);

    /**
     * 根据明细ID查询
     *
     * @param itemId 明细ID
     * @return 库存记录
     */
    List<InboundItemBatchVo> queryByItemId(Long itemId);
}
