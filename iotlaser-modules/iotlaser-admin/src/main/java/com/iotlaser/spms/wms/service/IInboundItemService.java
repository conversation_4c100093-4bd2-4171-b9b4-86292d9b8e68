package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.bo.InboundItemBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * 产品入库明细Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface IInboundItemService {

    /**
     * 查询产品入库明细
     *
     * @param itemId 主键
     * @return 产品入库明细
     */
    InboundItemVo queryById(Long itemId);

    /**
     * 分页查询产品入库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库明细分页列表
     */
    TableDataInfo<InboundItemVo> queryPageList(InboundItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品入库明细列表
     *
     * @param bo 查询条件
     * @return 产品入库明细列表
     */
    List<InboundItemVo> queryList(InboundItemBo bo);

    /**
     * 新增产品入库明细
     *
     * @param bo 产品入库明细
     * @return 是否新增成功
     */
    Boolean insertByBo(InboundItemBo bo);

    /**
     * 修改产品入库明细
     *
     * @param bo 产品入库明细
     * @return 是否修改成功
     */
    Boolean updateByBo(InboundItemBo bo);

    /**
     * 批量完成收货数量汇总
     *
     * @param itemId   明细 ID
     * @param batchId  批次 ID
     * @param quantity 数量
     * @return Boolean 返回结果
     */
    Boolean updateFinishQuantityBeforeSave(Long itemId, Long batchId, BigDecimal quantity);

    /**
     * 校验并批量删除产品入库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入产品入库明细表
     *
     * @param items 明细BO列表
     * @return 是否成功
     */
    Boolean insertOrUpdateBatch(List<InboundItemBo> items);

}
