package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.erp.domain.vo.PurchaseInboundVo;
import com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo;
import com.iotlaser.spms.wms.domain.bo.InboundBo;
import com.iotlaser.spms.wms.domain.bo.InboundItemBo;
import com.iotlaser.spms.wms.domain.vo.InboundItemVo;
import com.iotlaser.spms.wms.domain.vo.InboundVo;
import com.iotlaser.spms.wms.domain.vo.OutboundVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 产品入库Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface IInboundService {

    /**
     * 查询产品入库
     *
     * @param inboundId 主键
     * @return 产品入库
     */
    InboundVo queryById(Long inboundId);

    /**
     * 分页查询产品入库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库分页列表
     */
    TableDataInfo<InboundVo> queryPageList(InboundBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品入库列表
     *
     * @param bo 查询条件
     * @return 产品入库列表
     */
    List<InboundVo> queryList(InboundBo bo);

    /**
     * 新增产品入库
     *
     * @param bo 产品入库
     * @return 是否新增成功
     */
    InboundVo insertByBo(InboundBo bo);

    /**
     * 修改产品入库
     *
     * @param bo 产品入库
     * @return 是否修改成功
     */
    InboundVo updateByBo(InboundBo bo);

    /**
     * 校验并批量删除产品入库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据采购订单创建入库单
     *
     * @param orderVo 采购订单
     * @return 创建的入库单
     */
    Boolean createFromPurchaseOrder(PurchaseOrderVo orderVo);

    /**
     * 根据采购入库单创建入库单
     *
     * @param inboundVo 采购订单
     * @return 创建的入库单
     */
    Boolean createFromPurchaseInbound(PurchaseInboundVo inboundVo);

    /**
     * 根据出库单创建入库单
     *
     * @param outboundVo 出库单
     * @return 是否创建成功
     */
    Boolean createFromOutbound(OutboundVo outboundVo);

    /**
     * 取消入库单
     *
     * @param inboundId    入库单ID
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    Boolean cancelInbound(Long inboundId, String cancelReason);

    /**
     * 完成入库
     *
     * @param inboundId 入库单ID
     * @return 是否完成成功
     */
    Boolean completeInbound(Long inboundId);

    /**
     * 根据来源ID和来源类型查询仓库入库单列表
     *
     * @param sourceId   来源ID
     * @param sourceType 来源类型
     * @return 仓库入库单列表
     */
    List<InboundVo> queryBySourceId(Long sourceId, SourceType sourceType);


    /**
     * 查询产品入库明细表及其关联信息
     *
     * @param itemId 主键
     * @return 产品入库明细表
     */
    InboundItemVo queryItemById(Long itemId);

    /**
     * 查询产品入库明细表列表及其关联信息
     *
     * @param directSourceId   上游ID
     * @param directSourceType 上游类型
     * @return 产品入库明细表列表
     */
    List<InboundItemVo> queryItemByDirectSourceId(Long directSourceId, DirectSourceType directSourceType);

    /**
     * 查询产品入库明细表列表及其关联信息
     *
     * @param bo 查询条件
     * @return 产品入库明细表列表
     */
    List<InboundItemVo> queryItemList(InboundItemBo bo);

    /**
     * 分页查询产品入库明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品入库明细表分页列表
     */
    TableDataInfo<InboundItemVo> queryItemPageList(InboundItemBo bo, PageQuery pageQuery);

}
