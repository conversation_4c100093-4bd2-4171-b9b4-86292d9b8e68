package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.bo.InventoryLogBo;
import com.iotlaser.spms.wms.domain.vo.InventoryLogVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 产品库存日志Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
public interface IInventoryLogService {

    /**
     * 查询产品库存日志
     *
     * @param logId 主键
     * @return 产品库存日志
     */
    InventoryLogVo queryById(Long logId);

    /**
     * 分页查询产品库存日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品库存日志分页列表
     */
    TableDataInfo<InventoryLogVo> queryPageList(InventoryLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品库存日志列表
     *
     * @param bo 查询条件
     * @return 产品库存日志列表
     */
    List<InventoryLogVo> queryList(InventoryLogBo bo);

    /**
     * 新增产品库存日志
     *
     * @param bo 产品库存日志
     * @return 是否新增成功
     */
    Boolean insertByBo(InventoryLogBo bo);

    /**
     * 修改产品库存日志
     *
     * @param bo 产品库存日志
     * @return 是否修改成功
     */
    Boolean updateByBo(InventoryLogBo bo);

    /**
     * 校验并批量删除产品库存日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入或更新产品库存日志
     * ✅ 统一使用insertOrUpdateBatch方法，避免重复的批量插入方法
     *
     * @param logs 日志BO列表
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<InventoryLogBo> logs);

    /**
     * 判断指定来源ID的库存日志是否存在
     *
     * @param sourceId 来源ID
     * @return 是否存在
     */
    Boolean exsitsBySourceId(Long sourceId);

    /**
     * 判断指定直接来源ID的库存日志是否存在
     *
     * @param directSourceId 直接来源ID
     * @return 是否存在
     */
    Boolean exsitsByDirectSourceId(Long directSourceId);

    /**
     * 判断指定库存ID的库存日志是否存在
     *
     * @param inventoryId 库存ID
     * @return 是否存在
     */
    Boolean exsitsByInventoryId(Long inventoryId);
}
