package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.enums.InventoryStatus;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 产品库存Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/05/10
 */
public interface IInventoryService {

    /**
     * 查询产品库存
     *
     * @param inventoryId 主键
     * @return 产品库存
     */
    InventoryVo queryById(Long inventoryId);

    /**
     * 分页查询产品库存列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品库存分页列表
     */
    TableDataInfo<InventoryVo> queryPageList(InventoryBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品库存列表
     *
     * @param bo 查询条件
     * @return 产品库存列表
     */
    List<InventoryVo> queryList(InventoryBo bo);

    /**
     * 查询符合条件的产品库存是否存在
     *
     * @param bo 查询条件
     * @return 产品库存是否存在
     */
    Boolean exists(InventoryBo bo);

    /**
     * 新增产品库存
     *
     * @param bo 产品库存
     * @return 是否新增成功
     */
    Boolean insertByBo(InventoryBo bo);

    /**
     * 修改产品库存
     *
     * @param bo 产品库存
     * @return 是否修改成功
     */
    Boolean updateByBo(InventoryBo bo);

    /**
     * 校验并批量删除产品库存信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量插入或更新产品库存
     * ✅ 统一使用insertOrUpdateBatch方法，避免重复的批量插入方法
     *
     * @param batches 批次BO列表
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<InventoryBo> batches);

    /**
     * 批次有效期检查和状态更新
     *
     * @param batchId 批次ID
     * @return 是否更新成功
     */
    Boolean checkAndUpdateBatchExpiry(Long batchId);

    /**
     * 批量检查批次有效期
     *
     * @param productId  产品ID（可选，为null时检查所有产品）
     * @param locationId 库位ID（可选，为null时检查所有库位）
     * @return 检查结果统计
     */
    Map<String, Object> batchCheckExpiry(Long productId, Long locationId);

    /**
     * 获取即将过期的批次列表
     *
     * @param warningDays 预警天数
     * @param productId   产品ID（可选）
     * @param locationId  库位ID（可选）
     * @return 即将过期的批次列表
     */
    List<InventoryVo> getExpiringBatches(int warningDays, Long productId, Long locationId);

    /**
     * 获取已过期的批次列表
     *
     * @param productId  产品ID（可选）
     * @param locationId 库位ID（可选）
     * @return 已过期的批次列表
     */
    List<InventoryVo> getExpiredBatches(Long productId, Long locationId);

    /**
     * 冻结批次
     *
     * @param batchId      批次ID
     * @param freezeReason 冻结原因
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否冻结成功
     */
    Boolean freezeBatch(Long batchId, String freezeReason, Long operatorId, String operatorName);

    /**
     * 解冻批次
     *
     * @param batchId        批次ID
     * @param unfreezeReason 解冻原因
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否解冻成功
     */
    Boolean unfreezeBatch(Long batchId, String unfreezeReason, Long operatorId, String operatorName);

    /**
     * 批次状态转换
     *
     * @param batchId      批次ID
     * @param newStatus    新状态
     * @param reason       转换原因
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否转换成功
     */
    Boolean changeBatchStatus(Long batchId, InventoryStatus newStatus, String reason, Long operatorId, String operatorName);

    /**
     * 批次库存调整
     *
     * @param batchId  批次ID
     * @param quantity 调整数量
     * @return 是否调整成功
     */
    Boolean adjustItem(Long batchId, BigDecimal quantity);

    /**
     * 批次库存调整
     *
     * @param productId    产品ID
     * @param locationId   库位ID
     * @param adjustQty    调整数量（正数增加，负数减少）
     * @param adjustReason 调整原因
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否调整成功
     */
    Boolean adjustBatch(Long productId, Long locationId, BigDecimal adjustQty,
                        String adjustReason, Long operatorId, String operatorName);

    /**
     * 带并发控制的库存扣减（FIFO）
     * 使用SELECT FOR UPDATE防止并发超卖
     *
     * @param productId    产品ID
     * @param locationId   库位ID
     * @param deductQty    扣减数量
     * @param reason       扣减原因
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否扣减成功
     */
    Boolean deductInventoryWithLock(Long productId, Long locationId, BigDecimal deductQty,
                                    String reason, Long operatorId, String operatorName);

    /**
     * 按产品汇总数量
     *
     * @param productId 产品ID
     * @return 总数量
     */
    BigDecimal sumQuantityByProductId(Long productId);

    /**
     * 按产品汇总可用数量
     *
     * @param productId 产品ID
     * @return 可用数量
     */
    BigDecimal sumAvailableQuantityByProductId(Long productId);

    /**
     * 按产品和库位汇总可用数量
     *
     * @param productId  产品ID
     * @param locationId 库位ID（可选）
     * @return 可用数量
     */
    BigDecimal sumAvailableQuantityByProduct(Long productId, Long locationId);

    /**
     * 冻结指定库位的批次
     *
     * @param productId    产品ID
     * @param locationId   库位ID
     * @param freezeQty    冻结数量
     * @param freezeReason 冻结原因
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否冻结成功
     */
    Boolean freezeBatchesByLocation(Long productId, Long locationId, BigDecimal freezeQty,
                                    String freezeReason, Long operatorId, String operatorName);

    /**
     * 解冻指定库位的批次
     *
     * @param productId      产品ID
     * @param locationId     库位ID
     * @param unfreezeQty    解冻数量
     * @param unfreezeReason 解冻原因
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否解冻成功
     */
    Boolean unfreezeBatchesByLocation(Long productId, Long locationId, BigDecimal unfreezeQty,
                                      String unfreezeReason, Long operatorId, String operatorName);

    /**
     * 检查库存可用性
     *
     * @param productId  产品ID
     * @param locationId 库位ID
     * @param requireQty 需求数量
     * @return 是否有足够可用库存
     */
    Boolean checkInventoryAvailability(Long productId, Long locationId, BigDecimal requireQty);

    /**
     * 获取产品可用库存数量
     *
     * @param productId  产品ID
     * @param locationId 库位ID（可选，为null时查询所有库位）
     * @return 可用库存数量
     */
    BigDecimal getAvailableQuantity(Long productId, Long locationId);
}
