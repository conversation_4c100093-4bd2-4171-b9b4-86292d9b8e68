package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.bo.OutboundItemBatchBo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemBatchVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 产品出库批次明细Service接口
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
public interface IOutboundItemBatchService {

    /**
     * 查询产品出库批次明细
     *
     * @param batchId 主键
     * @return 产品出库批次明细
     */
    OutboundItemBatchVo queryById(Long batchId);

    /**
     * 分页查询产品出库批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品出库批次明细分页列表
     */
    TableDataInfo<OutboundItemBatchVo> queryPageList(OutboundItemBatchBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品出库批次明细列表
     *
     * @param bo 查询条件
     * @return 产品出库批次明细列表
     */
    List<OutboundItemBatchVo> queryList(OutboundItemBatchBo bo);

    /**
     * 新增产品出库批次明细
     *
     * @param bo 产品出库批次明细
     * @return 是否新增成功
     */
    Boolean insertByBo(OutboundItemBatchBo bo);

    /**
     * 修改产品出库批次明细
     *
     * @param bo 产品出库批次明细
     * @return 是否修改成功
     */
    Boolean updateByBo(OutboundItemBatchBo bo);

    /**
     * 校验并批量删除产品出库批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量新增或更新产品出库批次明细
     * ✅ 统一使用insertOrUpdateBatch方法，避免重复的批量插入方法
     *
     * @param batches 批次明细BO列表
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<OutboundItemBatchBo> batches);

    /**
     * 根据明细ID查询
     *
     * @param itemId 明细ID
     * @return 库存记录
     */
    List<OutboundItemBatchVo> queryByItemId(Long itemId);
}
