package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.bo.OutboundItemBo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 产品出库明细Service接口
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
public interface IOutboundItemService {

    /**
     * 查询产品出库明细
     *
     * @param itemId 主键
     * @return 产品出库明细
     */
    OutboundItemVo queryById(Long itemId);

    /**
     * 分页查询产品出库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品出库明细分页列表
     */
    TableDataInfo<OutboundItemVo> queryPageList(OutboundItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品出库明细列表
     *
     * @param bo 查询条件
     * @return 产品出库明细列表
     */
    List<OutboundItemVo> queryList(OutboundItemBo bo);

    /**
     * 新增产品出库明细
     *
     * @param bo 产品出库明细
     * @return 是否新增成功
     */
    Boolean insertByBo(OutboundItemBo bo);

    /**
     * 修改产品出库明细
     *
     * @param bo 产品出库明细
     * @return 是否修改成功
     */
    Boolean updateByBo(OutboundItemBo bo);

    /**
     * 校验并批量删除产品出库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量新增或更新产品出库明细
     *
     * @param items 明细BO列表
     * @return 是否操作成功
     */
    Boolean insertOrUpdateBatch(List<OutboundItemBo> items);


}
