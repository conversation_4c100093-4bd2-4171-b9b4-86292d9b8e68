package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import com.iotlaser.spms.wms.domain.bo.OutboundBo;
import com.iotlaser.spms.wms.domain.bo.OutboundItemBo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemVo;
import com.iotlaser.spms.wms.domain.vo.OutboundVo;
import com.iotlaser.spms.wms.domain.vo.TransferVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.SourceType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 产品出库Service接口
 *
 * <AUTHOR>
 * @date 2025/04/23
 */
public interface IOutboundService {

    /**
     * 查询产品出库
     *
     * @param outboundId 主键
     * @return 产品出库
     */
    OutboundVo queryById(Long outboundId);

    /**
     * 分页查询产品出库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品出库分页列表
     */
    TableDataInfo<OutboundVo> queryPageList(OutboundBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品出库列表
     *
     * @param bo 查询条件
     * @return 产品出库列表
     */
    List<OutboundVo> queryList(OutboundBo bo);

    /**
     * 新增产品出库
     *
     * @param bo 产品出库
     * @return 是否新增成功
     */
    OutboundVo insertByBo(OutboundBo bo);

    /**
     * 修改产品出库
     *
     * @param bo 产品出库
     * @return 是否修改成功
     */
    OutboundVo updateByBo(OutboundBo bo);

    /**
     * 校验并批量删除产品出库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 查询产品出库明细表及其关联信息
     *
     * @param itemId 主键
     * @return 产品出库明细表
     */
    OutboundItemVo queryItemById(Long itemId);

    /**
     * 查询产品出库明细表列表及其关联信息
     *
     * @param directSourceId   上游ID
     * @param directSourceType 上游类型
     * @return 产品出库明细表列表
     */
    List<OutboundItemVo> queryItemByDirectSourceId(Long directSourceId, DirectSourceType directSourceType);

    /**
     * 查询产品出库明细表列表及其关联信息
     *
     * @param bo 查询条件
     * @return 产品出库明细表列表
     */
    List<OutboundItemVo> queryItemList(OutboundItemBo bo);

    /**
     * 分页查询产品出库明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品出库明细表分页列表
     */
    TableDataInfo<OutboundItemVo> queryItemPageList(OutboundItemBo bo, PageQuery pageQuery);

    /**
     * 根据来源ID和来源类型查询仓库出库单列表
     *
     * @param sourceId   来源ID
     * @param sourceType 来源类型
     * @return 仓库出库单列表
     */
    List<OutboundVo> queryBySourceId(Long sourceId, SourceType sourceType);

    /**
     * 根据销售出库单创建仓库出库单
     *
     * @param outboundVo 销售出库单
     * @return 是否创建成功
     */
    Boolean createFromSaleOutbound(SaleOutboundVo outboundVo);

    /**
     * 基于仓库移库单创建仓库出库单
     *
     * @param transferVo 仓库移库单
     * @return 是否创建成功
     */
    Boolean createFromTransfer(TransferVo transferVo);

    /**
     * 确认仓库出库单
     *
     * @param outboundId 仓库出库单ID
     * @return 是否确认成功
     */
    Boolean confirmOutbound(Long outboundId);

    /**
     * 批量确认仓库出库单
     *
     * @param outboundIds 仓库出库单ID集合
     * @return 是否确认成功
     */
    Boolean batchConfirmOutbounds(Collection<Long> outboundIds);

    /**
     * 完成仓库出库
     *
     * @param outboundId 仓库出库单ID
     * @return 是否完成成功
     */
    Boolean completeOutbound(Long outboundId);

    /**
     * 取消仓库出库单
     *
     * @param outboundId   仓库出库单ID
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    Boolean cancelOutbound(Long outboundId, String cancelReason);
}
