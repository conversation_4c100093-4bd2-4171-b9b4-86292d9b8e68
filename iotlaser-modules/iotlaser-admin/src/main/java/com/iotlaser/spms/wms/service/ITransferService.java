package com.iotlaser.spms.wms.service;

import com.iotlaser.spms.wms.domain.bo.TransferBo;
import com.iotlaser.spms.wms.domain.vo.TransferVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 产品移库Service接口
 *
 * <AUTHOR> <PERSON>
 * @date 2025/04/23
 */
public interface ITransferService {

    /**
     * 查询产品移库
     *
     * @param transferId 主键
     * @return 产品移库
     */
    TransferVo queryById(Long transferId);

    /**
     * 分页查询产品移库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品移库分页列表
     */
    TableDataInfo<TransferVo> queryPageList(TransferBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的产品移库列表
     *
     * @param bo 查询条件
     * @return 产品移库列表
     */
    List<TransferVo> queryList(TransferBo bo);

    /**
     * 新增产品移库
     *
     * @param bo 产品移库
     * @return 是否新增成功
     */
    TransferVo insertByBo(TransferBo bo);

    /**
     * 修改产品移库
     *
     * @param bo 产品移库
     * @return 是否修改成功
     */
    TransferVo updateByBo(TransferBo bo);

    /**
     * 校验并批量删除产品移库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 更新产品移库为已完成
     *
     * @param transferId 移库ID
     */
    void finish(Long transferId);
}
