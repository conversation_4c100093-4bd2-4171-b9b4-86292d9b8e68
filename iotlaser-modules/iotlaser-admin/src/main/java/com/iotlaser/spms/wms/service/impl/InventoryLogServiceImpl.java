package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.wms.domain.InventoryLog;
import com.iotlaser.spms.wms.domain.bo.InventoryLogBo;
import com.iotlaser.spms.wms.domain.vo.InventoryLogVo;
import com.iotlaser.spms.wms.mapper.InventoryLogMapper;
import com.iotlaser.spms.wms.service.IInventoryLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品库存日志Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InventoryLogServiceImpl implements IInventoryLogService {

    private final InventoryLogMapper baseMapper;

    /**
     * 查询产品库存日志
     *
     * @param logId 主键
     * @return 产品库存日志
     */
    @Override
    public InventoryLogVo queryById(Long logId) {
        return baseMapper.selectVoById(logId);
    }

    /**
     * 分页查询产品库存日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品库存日志分页列表
     */
    @Override
    public TableDataInfo<InventoryLogVo> queryPageList(InventoryLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<InventoryLog> lqw = buildQueryWrapper(bo);
        Page<InventoryLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品库存日志列表
     *
     * @param bo 查询条件
     * @return 产品库存日志列表
     */
    @Override
    public List<InventoryLogVo> queryList(InventoryLogBo bo) {
        LambdaQueryWrapper<InventoryLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<InventoryLog> buildQueryWrapper(InventoryLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<InventoryLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(InventoryLog::getLogId);
        lqw.eq(bo.getProductInstanceId() != null, InventoryLog::getProductInstanceId, bo.getProductInstanceId());
        lqw.eq(bo.getInventoryId() != null, InventoryLog::getInventoryId, bo.getInventoryId());
        lqw.eq(bo.getSourceId() != null, InventoryLog::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), InventoryLog::getSourceCode, bo.getSourceCode());
        lqw.eq(bo.getSourceType() != null, InventoryLog::getSourceType, bo.getSourceType());
        lqw.eq(bo.getDirectSourceItemId() != null, InventoryLog::getDirectSourceItemId, bo.getDirectSourceItemId());
        lqw.eq(bo.getDirectSourceBatchId() != null, InventoryLog::getDirectSourceBatchId, bo.getDirectSourceBatchId());
        lqw.eq(bo.getProductId() != null, InventoryLog::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), InventoryLog::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), InventoryLog::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, InventoryLog::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), InventoryLog::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), InventoryLog::getUnitName, bo.getUnitName());
        lqw.eq(bo.getLocationId() != null, InventoryLog::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), InventoryLog::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), InventoryLog::getLocationName, bo.getLocationName());
        lqw.eq(bo.getDirection() != null, InventoryLog::getDirection, bo.getDirection());
        lqw.eq(bo.getBeforeQuantity() != null, InventoryLog::getBeforeQuantity, bo.getBeforeQuantity());
        lqw.eq(bo.getQuantity() != null, InventoryLog::getQuantity, bo.getQuantity());
        lqw.eq(bo.getAfterQuantity() != null, InventoryLog::getAfterQuantity, bo.getAfterQuantity());
        lqw.eq(bo.getPrice() != null, InventoryLog::getPrice, bo.getPrice());
        lqw.eq(bo.getRecordTime() != null, InventoryLog::getRecordTime, bo.getRecordTime());
        lqw.eq(StringUtils.isNotBlank(bo.getReasonCode()), InventoryLog::getReasonCode, bo.getReasonCode());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), InventoryLog::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品库存日志
     *
     * @param bo 产品库存日志
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(InventoryLogBo bo) {
        try {
            InventoryLog add = MapstructUtils.convert(bo, InventoryLog.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增产品库存日志失败");
            }

            bo.setLogId(add.getLogId());

            log.info("新增产品库存日志成功：产品【{}】方向【{}】数量【{}】",
                add.getProductName(), add.getDirection(), add.getQuantity());
            return true;
        } catch (Exception e) {
            log.error("新增产品库存日志失败：{}", e.getMessage(), e);
            throw new ServiceException("新增产品库存日志失败：" + e.getMessage());
        }
    }

    /**
     * 修改产品库存日志
     *
     * @param bo 产品库存日志
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(InventoryLogBo bo) {
        try {
            InventoryLog update = MapstructUtils.convert(bo, InventoryLog.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改产品库存日志失败：日志不存在或数据未变更");
            }

            log.info("修改产品库存日志成功：产品【{}】方向【{}】数量【{}】",
                update.getProductName(), update.getDirection(), update.getQuantity());
            return true;
        } catch (Exception e) {
            log.error("修改产品库存日志失败：{}", e.getMessage(), e);
            throw new ServiceException("修改产品库存日志失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(InventoryLog entity) {

    }

    /**
     * 校验并批量删除产品库存日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 库存日志一般不允许删除，只能查询
            List<InventoryLog> inventoryLogs = baseMapper.selectByIds(ids);
            for (InventoryLog inventoryLog : inventoryLogs) {
                // 检查是否允许删除库存日志
                log.info("删除库存日志校验：产品【{}】方向【{}】数量【{}】",
                    inventoryLog.getProductName(), inventoryLog.getDirection(), inventoryLog.getQuantity());

                // 一般情况下，库存日志作为审计记录不应该被删除
                // 如果业务需要删除，需要特殊权限
                throw new ServiceException("库存日志作为审计记录，不允许删除");
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 批量插入或更新产品库存日志
     * ✅ 统一使用insertOrUpdateBatch方法，避免重复的批量插入方法
     *
     * @param logs 日志BO列表
     * @return 是否操作成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertOrUpdateBatch(List<InventoryLogBo> logs) {
        List<InventoryLog> entities = logs.stream()
            .map(bo -> MapstructUtils.convert(bo, InventoryLog.class))
            .collect(Collectors.toList());
        return baseMapper.insertBatch(entities);
    }

    /**
     * 判断指定来源ID的库存日志是否存在
     *
     * @param sourceId 来源ID
     * @return 是否存在
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean exsitsBySourceId(Long sourceId) {
        return baseMapper.existsBySourceId(sourceId);
    }

    /**
     * 判断指定直接来源ID的库存日志是否存在
     *
     * @param directSourceId 直接来源ID
     * @return 是否存在
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean exsitsByDirectSourceId(Long directSourceId) {
        return baseMapper.existsByDirectSourceId(directSourceId);
    }

    /**
     * 判断指定库存ID的库存日志是否存在
     *
     * @param inventoryId 库存ID
     * @return 是否存在
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean exsitsByInventoryId(Long inventoryId) {
        return baseMapper.existsByInventoryId(inventoryId);
    }
}
