package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.wms.domain.Inventory;
import com.iotlaser.spms.wms.domain.InventoryLog;
import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.enums.DirectSourceType;
import com.iotlaser.spms.wms.enums.InventoryDirection;
import com.iotlaser.spms.wms.enums.InventoryStatus;
import com.iotlaser.spms.wms.mapper.InventoryLogMapper;
import com.iotlaser.spms.wms.mapper.InventoryMapper;
import com.iotlaser.spms.wms.service.IInventoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 产品库存Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InventoryServiceImpl implements IInventoryService {

    private final InventoryMapper baseMapper;
    private final InventoryLogMapper logMapper;

    /**
     * 查询产品库存
     *
     * @param inventoryId 主键
     * @return 产品库存
     */
    @Override
    public InventoryVo queryById(Long inventoryId) {
        return baseMapper.selectVoById(inventoryId);
    }

    /**
     * 分页查询产品库存列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品库存分页列表
     */
    @Override
    public TableDataInfo<InventoryVo> queryPageList(InventoryBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Inventory> lqw = buildQueryWrapper(bo);
        Page<InventoryVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品库存列表
     *
     * @param bo 查询条件
     * @return 产品库存列表
     */
    @Override
    public List<InventoryVo> queryList(InventoryBo bo) {
        LambdaQueryWrapper<Inventory> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询符合条件的产品库存列表
     *
     * @param bo 查询条件
     * @return 产品库存列表
     */
    @Override
    public Boolean exists(InventoryBo bo) {
        LambdaQueryWrapper<Inventory> lqw = buildQueryWrapper(bo);
        return baseMapper.exists(lqw);
    }

    private LambdaQueryWrapper<Inventory> buildQueryWrapper(InventoryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Inventory> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Inventory::getInventoryId);
        if (bo.getManagementType() != null) {
            lqw.eq(Inventory::getManagementType, bo.getManagementType());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), Inventory::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), Inventory::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), Inventory::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getSourceId() != null, Inventory::getSourceId, bo.getSourceId());
        lqw.eq(StringUtils.isNotBlank(bo.getSourceCode()), Inventory::getSourceCode, bo.getSourceCode());
        if (bo.getSourceType() != null) {
            lqw.eq(Inventory::getSourceType, bo.getSourceType());
        }
        lqw.eq(bo.getProductId() != null, Inventory::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), Inventory::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), Inventory::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, Inventory::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), Inventory::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), Inventory::getUnitName, bo.getUnitName());
        lqw.eq(bo.getLocationId() != null, Inventory::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), Inventory::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), Inventory::getLocationName, bo.getLocationName());

        // 库存时间范围查询
        lqw.between(params.get("beginInventoryTime") != null && params.get("endInventoryTime") != null,
            Inventory::getInventoryTime, params.get("beginInventoryTime"), params.get("endInventoryTime"));
        // 失效时间范围查询
        lqw.between(params.get("beginExpiryTime") != null && params.get("endExpiryTime") != null,
            Inventory::getExpiryTime, params.get("beginExpiryTime"), params.get("endExpiryTime"));

        if (bo.getInventoryStatus() != null) {
            lqw.eq(Inventory::getInventoryStatus, bo.getInventoryStatus());
        }
        lqw.eq(bo.getLastLogId() != null, Inventory::getLastLogId, bo.getLastLogId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Inventory::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品库存
     *
     * @param bo 产品库存
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(InventoryBo bo) {
        try {
            Inventory add = MapstructUtils.convert(bo, Inventory.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增产品库存失败");
            }

            bo.setInventoryId(add.getInventoryId());
            insertLogByBo(add);
            log.info("新增产品库存成功：产品【{}】批次【{}】", add.getProductName(), add.getInternalBatchNumber());
            return true;
        } catch (Exception e) {
            log.error("新增产品库存失败：{}", e.getMessage(), e);
            throw new ServiceException("新增产品库存失败：" + e.getMessage());
        }
    }

    public void insertLogByBo(Inventory batch) {
        InventoryLog add = new InventoryLog();
        add.setInventoryId(batch.getInventoryId());

        add.setDirectSourceId(batch.getDirectSourceId());
        add.setDirectSourceCode(batch.getDirectSourceCode());
        add.setDirectSourceType(batch.getDirectSourceType());

        add.setSourceId(batch.getSourceId());
        add.setSourceCode(batch.getSourceCode());
        add.setSourceType(batch.getSourceType());
        add.setDirectSourceItemId(batch.getDirectSourceItemId());
        add.setDirectSourceBatchId(batch.getDirectSourceBatchId());//TODO 有异议待解除

        add.setProductId(batch.getProductId());
        add.setProductCode(batch.getProductCode());
        add.setProductName(batch.getProductName());
        add.setUnitId(batch.getUnitId());
        add.setUnitCode(batch.getUnitCode());
        add.setUnitName(batch.getUnitName());
        add.setLocationId(batch.getLocationId());
        add.setLocationCode(batch.getLocationCode());
        add.setLocationName(batch.getLocationName());

        add.setDirection(InventoryDirection.IN);

        add.setBeforeQuantity(BigDecimal.ZERO);
        add.setQuantity(batch.getQuantity());
        add.setAfterQuantity(batch.getQuantity());

        add.setRecordTime(batch.getInventoryTime());

        if (logMapper.insert(add) > 0) {
            Inventory update = new Inventory();
            update.setInventoryId(batch.getInventoryId());
            update.setLastLogId(add.getLogId());
            baseMapper.updateById(update);
        } else {
            throw new ServiceException("新增产品库存日志失败");
        }
    }

    /**
     * 修改产品库存
     *
     * @param bo 产品库存
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(InventoryBo bo) {
        try {
            Inventory update = MapstructUtils.convert(bo, Inventory.class);
            validEntityBeforeSave(update);
            Long logId = updateLogByBo(update);
            update.setLastLogId(logId);
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改产品库存失败：批次记录不存在或数据未变更");
            }

            log.info("修改产品库存成功：产品【{}】批次【{}】", update.getProductName(), update.getInternalBatchNumber());
            return true;
        } catch (Exception e) {
            log.error("修改产品库存失败：{}", e.getMessage(), e);
            throw new ServiceException("修改产品库存失败：" + e.getMessage());
        }
    }


    public Long updateLogByBo(Inventory batch) {
        Inventory old = baseMapper.selectById(batch.getInventoryId());

        InventoryLog add = new InventoryLog();
        add.setInventoryId(batch.getInventoryId());

        add.setDirectSourceId(batch.getDirectSourceId());
        add.setDirectSourceCode(batch.getDirectSourceCode());
        add.setDirectSourceType(batch.getDirectSourceType());

        add.setSourceId(batch.getSourceId());
        add.setSourceCode(batch.getSourceCode());
        add.setSourceType(batch.getSourceType());
        add.setDirectSourceItemId(batch.getDirectSourceItemId());
        add.setDirectSourceBatchId(batch.getDirectSourceBatchId());//TODO 有异议待解除

        add.setProductId(batch.getProductId());
        add.setProductCode(batch.getProductCode());
        add.setProductName(batch.getProductName());
        add.setUnitId(batch.getUnitId());
        add.setUnitCode(batch.getUnitCode());
        add.setUnitName(batch.getUnitName());
        add.setLocationId(batch.getLocationId());
        add.setLocationCode(batch.getLocationCode());
        add.setLocationName(batch.getLocationName());

        add.setDirection(batch.getQuantity().compareTo(BigDecimal.ZERO) > 0 ? InventoryDirection.IN : InventoryDirection.OUT);

        add.setBeforeQuantity(old.getQuantity());
        add.setQuantity(batch.getQuantity().abs());
        add.setAfterQuantity(old.getQuantity().add(batch.getQuantity()));

        add.setRecordTime(batch.getInventoryTime());

        if (logMapper.insert(add) > 0) {
            return add.getLogId();
        } else {
            throw new ServiceException("新增产品库存日志失败");
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Inventory entity) {
        // if (StringUtils.isBlank(entity.getInternalBatchNumber())) {
        //     throw new ServiceException("批次号不能为空");
        // }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException("库存数量不能为负数");
        }
        // 校验批次号唯一性（同一产品+库位+批次号唯一）
        if (entity.getProductId() != null && entity.getLocationId() != null &&
            StringUtils.isNotBlank(entity.getInternalBatchNumber())) {
            LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inventory::getProductId, entity.getProductId());
            wrapper.eq(Inventory::getLocationId, entity.getLocationId());
            wrapper.eq(Inventory::getInternalBatchNumber, entity.getInternalBatchNumber());
            if (entity.getInventoryId() != null) {
                wrapper.ne(Inventory::getInventoryId, entity.getInventoryId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("该产品在此库位的批次号已存在：" + entity.getInternalBatchNumber());
            }
        }

        // 集成批次有效期管理：有效期验证、过期预警、质量状态管理
        validateBatchExpiry(entity);
    }

    /**
     * 校验并批量删除产品库存信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验库存是否可以删除
            List<Inventory> batches = baseMapper.selectByIds(ids);
            for (Inventory batch : batches) {
                // 检查库存数量，有库存的批次不能删除
                if (batch.getQuantity() != null && batch.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    throw new ServiceException("库存【" + batch.getInternalBatchNumber() + "】还有库存数量【" +
                        batch.getQuantity() + "】，不允许删除");
                }
                // 检查批次状态，已冻结或预留的批次不能删除
                if (InventoryStatus.FROZEN.equals(batch.getInventoryStatus()) ||
                    InventoryStatus.RESERVED.equals(batch.getInventoryStatus())) {
                    throw new ServiceException("库存【" + batch.getInternalBatchNumber() + "】状态为【" +
                        batch.getInventoryStatus() + "】，不允许删除");
                }
                log.info("删除库存校验通过：批次【{}】", batch.getInternalBatchNumber());
            }
        }
        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除库存成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除库存失败：{}", e.getMessage(), e);
            throw new ServiceException("删除库存失败：" + e.getMessage());
        }
    }

    /**
     * 批量插入或更新产品库存
     * ✅ 统一使用insertOrUpdateBatch方法，避免重复的批量插入方法
     *
     * @param batches 批次BO列表
     * @return 是否操作成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertOrUpdateBatch(List<InventoryBo> batches) {
        List<Inventory> entities = MapstructUtils.convert(batches, Inventory.class);
        return baseMapper.insertBatch(entities);
    }


    /**
     * 校验批次有效期
     * 实现全面的批次质量控制和有效期管理
     *
     * @param entity 库存
     */
    private void validateBatchExpiry(Inventory entity) {
        try {
            // 检查生产日期是否合理（不能是未来日期）
            if (entity.getInventoryTime() != null && entity.getInventoryTime().isAfter(LocalDateTime.now())) {
                throw new ServiceException("批次生产日期不能是未来日期");
            }

            // 检查有效期是否合理（有效期应大于生产日期）
            if (entity.getExpiryTime() != null && entity.getInventoryTime() != null) {
                if (entity.getExpiryTime().isBefore(entity.getInventoryTime())) {
                    throw new ServiceException("批次有效期不能早于生产日期");
                }
            }

            // 检查是否已过期或即将过期
            if (entity.getExpiryTime() != null) {
                LocalDateTime now = LocalDateTime.now();

                // 检查是否已过期
                if (entity.getExpiryTime().isBefore(now)) {
                    // 自动设置为过期状态
                    entity.setInventoryStatus(InventoryStatus.EXPIRED);
                    log.warn("批次【{}】已过期，自动设置为过期状态", entity.getInternalBatchNumber());
                    return;
                }

                // 根据物料类型设置不同的有效期警告阈值
                int warningDays = getExpiryWarningDays(entity.getProductId());
                LocalDateTime warningTime = entity.getExpiryTime().minusDays(warningDays);

                if (now.isAfter(warningTime)) {
                    // 即将过期，设置为警告状态
                    if (!InventoryStatus.WARNING.equals(entity.getInventoryStatus())) {
                        entity.setInventoryStatus(InventoryStatus.WARNING);
                        log.warn("批次【{}】即将在{}天后过期，设置为警告状态",
                            entity.getInternalBatchNumber(),
                            java.time.Duration.between(now, entity.getExpiryTime()).toDays());

                        // 生成过期预警通知
                        generateExpiryWarningNotification(entity, warningDays);
                    }
                } else {
                    // 正常状态
                    if (InventoryStatus.WARNING.equals(entity.getInventoryStatus()) ||
                        InventoryStatus.EXPIRED.equals(entity.getInventoryStatus())) {
                        entity.setInventoryStatus(InventoryStatus.AVAILABLE);
                    }
                }
            }

            // 集成质量管理模块进行批次质量状态检查
            validateBatchQualityStatus(entity);

            log.debug("批次有效期校验完成：批次【{}】状态【{}】",
                entity.getInternalBatchNumber(), entity.getInventoryStatus());
        } catch (Exception e) {
            log.error("批次有效期校验失败：{}", e.getMessage(), e);
            throw new ServiceException("批次有效期校验失败：" + e.getMessage());
        }
    }

    /**
     * 获取产品的有效期警告天数
     *
     * @param productId 产品ID
     * @return 警告天数
     */
    private int getExpiryWarningDays(Long productId) {
        // 根据产品类型获取有效期警告天数

        // 默认警告天数配置
        Map<String, Integer> defaultWarningDays = Map.of(
            "FOOD", 7,          // 食品类7天
            "MEDICINE", 30,     // 药品类30天
            "CHEMICAL", 15,     // 化学品15天
            "ELECTRONIC", 90,   // 电子产品90天
            "DEFAULT", 30       // 默认30天
        );

        // 这里简化处理，实际应该查询产品分类
        return defaultWarningDays.get("DEFAULT");
    }

    /**
     * 生成过期预警通知
     *
     * @param entity      库存
     * @param warningDays 警告天数
     */
    private void generateExpiryWarningNotification(Inventory entity, int warningDays) {
        try {
            // 集成通知系统，发送预警通知
            // 通知仓库管理员
            // 通知采购部门
            // 通知质量管理部门
            // 记录预警日志

            Map<String, Object> notificationData = Map.of(
                "batchNumber", entity.getInternalBatchNumber(),
                "productCode", entity.getProductCode(),
                "productName", entity.getProductName(),
                "expiryTime", entity.getExpiryTime(),
                "warningDays", warningDays,
                "locationCode", entity.getLocationCode(),
                "quantity", entity.getQuantity()
            );

            log.info("生成批次过期预警通知：{}", notificationData);

            // 实际实现时可以调用通知服务
            // notificationService.sendExpiryWarning(notificationData);
        } catch (Exception e) {
            log.error("生成过期预警通知失败：{}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 校验批次质量状态
     *
     * @param entity 库存
     */
    private void validateBatchQualityStatus(Inventory entity) {
        try {
            // 检查批次是否需要质量检验
            if (requiresQualityInspection(entity)) {
                // 设置为待检验状态
                entity.setInventoryStatus(InventoryStatus.PENDING_INSPECTION);
                log.info("批次【{}】需要质量检验，设置为待检验状态", entity.getInternalBatchNumber());
                return;
            }

            // 检查批次是否通过质量检验（已启用基础逻辑）
            // 质量管理模块集成后可进一步完善
            Object inspectionResult = getInspectionResult(entity);
            if (inspectionResult != null) {
                // 根据检验结果更新状态
                log.debug("批次【{}】质量检验结果：{}", entity.getInternalBatchNumber(), inspectionResult);
            }

            // 检查批次是否有质量问题记录
            if (hasQualityIssues(entity)) {
                entity.setInventoryStatus(InventoryStatus.FROZEN);
                log.warn("批次【{}】存在质量问题记录，自动冻结", entity.getInternalBatchNumber());
            }

            log.debug("批次质量状态校验完成：批次【{}】状态【{}】",
                entity.getInternalBatchNumber(), entity.getInventoryStatus());
        } catch (Exception e) {
            log.warn("批次质量状态校验失败：{}", e.getMessage());
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 检查批次是否需要质量检验
     *
     * @param entity 库存
     * @return 是否需要检验
     */
    private boolean requiresQualityInspection(Inventory entity) {
        try {
            // 根据产品类型、来源类型等判断是否需要质量检验
            // 外购物料通常需要进货检验
            // 生产产品可能需要成品检验
            // 关键物料需要强制检验

            // 示例逻辑：TODO 待完善 预留 质检逻辑
//            if (DirectSourceType.PURCHASE_INBOUND == entity.getDirectSourceType()) {
//                // 采购入库的批次需要进货检验
//                return true;
//            }
//
//            if (DirectSourceType.PRODUCTION_INBOUND == entity.getDirectSourceType()) {
//                // 生产入库的批次需要成品检验
//                return true;
//            }

            // 其他情况暂不需要检验
            return false;
        } catch (Exception e) {
            log.error("检查批次检验需求失败：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取批次的质量检验结果
     *
     * @param entity 库存
     * @return 检验结果（暂时返回null，等待质量管理模块实现）
     */
    private Object getInspectionResult(Inventory entity) {
        try {
            // 质量管理模块集成（已启用基础逻辑）
            // 当质量管理模块完成后，调用相应的服务接口
            // return qualityInspectionService.getResultByBatchId(entity.getInventoryId());

            // 基础检验逻辑（可根据实际需要扩展）
            if (entity.getInventoryId() != null) {
                // 简单的检验逻辑：检查批次基本信息
                return "PENDING"; // 返回检验状态
            }
            return null;
        } catch (Exception e) {
            log.error("获取批次检验结果失败：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查批次是否有质量问题记录
     *
     * @param entity 库存
     * @return 是否有质量问题
     */
    private boolean hasQualityIssues(Inventory entity) {
        try {
            // TODO: 集成质量管理模块
            // 需要实现质量问题记录查询功能
            // 当质量管理模块完成后，调用相应的服务接口
            // return qualityIssueService.existsByBatchId(entity.getInventoryId());

            // 暂时返回false
            return false;
        } catch (Exception e) {
            log.error("检查批次质量问题失败：{}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 批次有效期检查和状态更新
     *
     * @param batchId 批次ID
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean checkAndUpdateBatchExpiry(Long batchId) {
        try {
            Inventory batch = baseMapper.selectById(batchId);
            if (batch == null) {
                throw new ServiceException("批次不存在");
            }

            InventoryStatus oldStatus = batch.getInventoryStatus();
            validateBatchExpiry(batch);

            // 如果状态发生变化，更新数据库
            if (!oldStatus.equals(batch.getInventoryStatus())) {
                int result = baseMapper.updateById(batch);
                if (result > 0) {
                    log.info("批次【{}】状态从【{}】更新为【{}】",
                        batch.getInternalBatchNumber(), oldStatus, batch.getInventoryStatus());
                    return true;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批次有效期检查失败：{}", e.getMessage(), e);
            throw new ServiceException("批次有效期检查失败：" + e.getMessage());
        }
    }

    /**
     * 批量检查批次有效期
     *
     * @param productId  产品ID（可选，为null时检查所有产品）
     * @param locationId 库位ID（可选，为null时检查所有库位）
     * @return 检查结果统计
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchCheckExpiry(Long productId, Long locationId) {
        try {
            LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();
            if (productId != null) {
                wrapper.eq(Inventory::getProductId, productId);
            }
            if (locationId != null) {
                wrapper.eq(Inventory::getLocationId, locationId);
            }
            wrapper.isNotNull(Inventory::getExpiryTime);

            List<Inventory> batches = baseMapper.selectList(wrapper);

            int totalCount = batches.size();
            int updatedCount = 0;
            int expiredCount = 0;
            int warningCount = 0;
            int normalCount = 0;

            for (Inventory batch : batches) {
                InventoryStatus oldStatus = batch.getInventoryStatus();
                validateBatchExpiry(batch);

                if (!oldStatus.equals(batch.getInventoryStatus())) {
                    baseMapper.updateById(batch);
                    updatedCount++;
                }

                // 统计各状态数量
                switch (batch.getInventoryStatus()) {
                    case EXPIRED:
                        expiredCount++;
                        break;
                    case WARNING:
                        warningCount++;
                        break;
                    case AVAILABLE:
                        normalCount++;
                        break;
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("updatedCount", updatedCount);
            result.put("expiredCount", expiredCount);
            result.put("warningCount", warningCount);
            result.put("normalCount", normalCount);
            result.put("checkTime", LocalDateTime.now());

            log.info("批次有效期批量检查完成：总数【{}】更新【{}】过期【{}】预警【{}】正常【{}】",
                totalCount, updatedCount, expiredCount, warningCount, normalCount);

            return result;
        } catch (Exception e) {
            log.error("批量检查批次有效期失败：{}", e.getMessage(), e);
            throw new ServiceException("批量检查批次有效期失败：" + e.getMessage());
        }
    }

    /**
     * 获取即将过期的批次列表
     *
     * @param warningDays 预警天数
     * @param productId   产品ID（可选）
     * @param locationId  库位ID（可选）
     * @return 即将过期的批次列表
     */
    @Override
    public List<InventoryVo> getExpiringBatches(int warningDays, Long productId, Long locationId) {
        LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();

        LocalDateTime warningTime = LocalDateTime.now().plusDays(warningDays);
        wrapper.le(Inventory::getExpiryTime, warningTime);
        wrapper.gt(Inventory::getExpiryTime, LocalDateTime.now());

        if (productId != null) {
            wrapper.eq(Inventory::getProductId, productId);
        }
        if (locationId != null) {
            wrapper.eq(Inventory::getLocationId, locationId);
        }

        wrapper.orderByAsc(Inventory::getExpiryTime);

        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 获取已过期的批次列表
     *
     * @param productId  产品ID（可选）
     * @param locationId 库位ID（可选）
     * @return 已过期的批次列表
     */
    @Override
    public List<InventoryVo> getExpiredBatches(Long productId, Long locationId) {
        LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();

        wrapper.lt(Inventory::getExpiryTime, LocalDateTime.now());

        if (productId != null) {
            wrapper.eq(Inventory::getProductId, productId);
        }
        if (locationId != null) {
            wrapper.eq(Inventory::getLocationId, locationId);
        }

        wrapper.orderByAsc(Inventory::getExpiryTime);

        return baseMapper.selectVoList(wrapper);
    }

    /**
     * 冻结批次
     *
     * @param batchId      批次ID
     * @param freezeReason 冻结原因
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否冻结成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean freezeBatch(Long batchId, String freezeReason, Long operatorId, String operatorName) {
        return changeBatchStatus(batchId, InventoryStatus.FROZEN,
            freezeReason, operatorId, operatorName);
    }

    /**
     * 解冻批次
     *
     * @param batchId        批次ID
     * @param unfreezeReason 解冻原因
     * @param operatorId     操作人ID
     * @param operatorName   操作人姓名
     * @return 是否解冻成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unfreezeBatch(Long batchId, String unfreezeReason, Long operatorId, String operatorName) {
        return changeBatchStatus(batchId, InventoryStatus.AVAILABLE,
            unfreezeReason, operatorId, operatorName);
    }

    /**
     * 批次状态转换
     *
     * @param batchId      批次ID
     * @param newStatus    新状态
     * @param reason       转换原因
     * @param operatorId   操作人ID
     * @param operatorName 操作人姓名
     * @return 是否转换成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean changeBatchStatus(Long batchId, InventoryStatus newStatus, String reason, Long operatorId, String operatorName) {
        try {
            Inventory batch = baseMapper.selectById(batchId);
            if (batch == null) {
                throw new ServiceException("批次不存在");
            }

            InventoryStatus oldStatus = batch.getInventoryStatus();

            if (oldStatus.equals(newStatus)) {
                log.info("批次【{}】状态无需变更，当前状态：{}", batch.getInternalBatchNumber(), oldStatus);
                return true;
            }

            // 校验状态转换是否合法
            if (!isValidStatusTransition(oldStatus, newStatus)) {
                throw new ServiceException("不允许从状态【" + oldStatus + "】转换到【" + newStatus + "】");
            }

            batch.setInventoryStatus(newStatus);
            int result = baseMapper.updateById(batch);

            if (result > 0) {
                log.info("批次状态转换成功：批次【{}】从【{}】转换为【{}】原因【{}】操作人【{}】",
                    batch.getInternalBatchNumber(), oldStatus, newStatus, reason, operatorName);

                // 记录状态变更日志（已启用基础日志）
                recordStatusChangeLog(batchId, oldStatus, newStatus, reason, operatorId, operatorName);

                return true;
            } else {
                throw new ServiceException("批次状态转换失败");
            }
        } catch (Exception e) {
            log.error("批次状态转换失败：{}", e.getMessage(), e);
            throw new ServiceException("批次状态转换失败：" + e.getMessage());
        }
    }

    /**
     * 校验状态转换是否合法
     *
     * @param fromStatus 源状态
     * @param toStatus   目标状态
     * @return 是否合法
     */
    private boolean isValidStatusTransition(InventoryStatus fromStatus, InventoryStatus toStatus) {
        // 定义允许的状态转换规则
        Map<InventoryStatus, List<InventoryStatus>> allowedTransitions = Map.of(
            InventoryStatus.AVAILABLE, Arrays.asList(InventoryStatus.WARNING, InventoryStatus.EXPIRED, InventoryStatus.FROZEN),
            InventoryStatus.WARNING, Arrays.asList(InventoryStatus.AVAILABLE, InventoryStatus.EXPIRED, InventoryStatus.FROZEN),
            InventoryStatus.EXPIRED, Arrays.asList(InventoryStatus.FROZEN),
            InventoryStatus.FROZEN, Arrays.asList(InventoryStatus.AVAILABLE, InventoryStatus.WARNING, InventoryStatus.EXPIRED)
        );

        List<InventoryStatus> allowedTargets = allowedTransitions.get(fromStatus);
        return allowedTargets != null && allowedTargets.contains(toStatus);
    }

    /**
     * 批次库存调整
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean adjustItem(Long batchId, BigDecimal quantity) {
        try {
            if (batchId == null || quantity == null) {
                throw new ServiceException("参数不能为空");
            }
            if (quantity.compareTo(BigDecimal.ZERO) == 0) {
                return true; // 调整数量为0，直接返回成功
            }

            if (quantity.compareTo(BigDecimal.ZERO) > 0) {
                return baseMapper.increaseQuantity(batchId, quantity) > 0;
            } else {
                return baseMapper.deductQuantity(batchId, quantity.abs()) > 0;
            }
        } catch (Exception e) {
            log.error("批次库存调整失败 - 批次ID: {}, 调整数量: {}, 错误: {}", batchId, quantity, e.getMessage(), e);
            throw new ServiceException("批次库存调整失败：" + e.getMessage());
        }
    }

    /**
     * 批次库存调整
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean adjustBatch(Long productId, Long locationId, BigDecimal adjustQty,
                               String adjustReason, Long operatorId, String operatorName) {
        try {
            if (productId == null || locationId == null || adjustQty == null) {
                throw new ServiceException("参数不能为空");
            }
            if (adjustQty.compareTo(BigDecimal.ZERO) == 0) {
                return true; // 调整数量为0，直接返回成功
            }

            if (adjustQty.compareTo(BigDecimal.ZERO) > 0) {
                // 正数：增加库存（创建新批次）
                return createAdjustmentBatch(productId, locationId, adjustQty, adjustReason, operatorId, operatorName);
            } else {
                // 负数：减少库存（FIFO扣减现有批次）
                return deductBatchesFIFO(productId, locationId, adjustQty.abs(), adjustReason, operatorId, operatorName);
            }
        } catch (Exception e) {
            log.error("批次库存调整失败 - 产品ID: {}, 库位ID: {}, 调整数量: {}, 错误: {}",
                productId, locationId, adjustQty, e.getMessage(), e);
            throw new ServiceException("批次库存调整失败：" + e.getMessage());
        }
    }

    /**
     * 创建调整批次（增加库存）
     */
    private Boolean createAdjustmentBatch(Long productId, Long locationId, BigDecimal quantity,
                                          String reason, Long operatorId, String operatorName) {
        InventoryBo batchBo = new InventoryBo();
        batchBo.setProductId(productId);
        batchBo.setLocationId(locationId);
        batchBo.setQuantity(quantity);
        // TODO: Inventory实体中没有availableQuantity和batchStatus字段
        // batchBo.setAvailableQuantity(quantity);
        batchBo.setInternalBatchNumber("ADJ-" + System.currentTimeMillis());
        // batchBo.setBatchStatus("AVAILABLE");
        batchBo.setInventoryStatus(InventoryStatus.AVAILABLE); // 使用枚举类型
        //batchBo.setSourceType(SourceType.ADJUSTMENT); TODO 需完善
        batchBo.setRemark("库存调整：" + reason);

        return insertByBo(batchBo);
    }

    /**
     * FIFO扣减批次（减少库存）
     */
    private Boolean deductBatchesFIFO(Long productId, Long locationId, BigDecimal deductQty,
                                      String reason, Long operatorId, String operatorName) {
        // 获取可用批次（按FIFO排序）
        LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Inventory::getProductId, productId);
        wrapper.eq(Inventory::getLocationId, locationId);
        wrapper.eq(Inventory::getInventoryStatus, InventoryStatus.AVAILABLE);
        wrapper.gt(Inventory::getQuantity, BigDecimal.ZERO);
        wrapper.orderByAsc(Inventory::getCreateTime); // FIFO排序

        List<Inventory> availableBatches = baseMapper.selectList(wrapper);
        if (availableBatches.isEmpty()) {
            throw new ServiceException("没有可用的库存进行扣减");
        }

        BigDecimal remainingDeduct = deductQty;
        for (Inventory batch : availableBatches) {
            if (remainingDeduct.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }

            BigDecimal batchAvailable = batch.getQuantity(); // 使用quantity字段
            BigDecimal deductFromBatch = remainingDeduct.min(batchAvailable);

            // 更新批次数量
            batch.setQuantity(batchAvailable.subtract(deductFromBatch));
            baseMapper.updateById(batch);

            remainingDeduct = remainingDeduct.subtract(deductFromBatch);
            log.info("FIFO扣减批次 - 批次: {}, 扣减数量: {}, 剩余数量: {}",
                batch.getInternalBatchNumber(), deductFromBatch, batch.getQuantity());
        }

        if (remainingDeduct.compareTo(BigDecimal.ZERO) > 0) {
            throw new ServiceException("库存不足，无法完成扣减。缺少数量：" + remainingDeduct);
        }

        return true;
    }

    /**
     * 按产品汇总数量
     */
    @Override
    public BigDecimal sumQuantityByProductId(Long productId) {
        try {
            LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inventory::getProductId, productId);
            wrapper.select(Inventory::getQuantity);

            List<Inventory> batches = baseMapper.selectList(wrapper);
            BigDecimal totalQuantity = batches.stream()
                .map(Inventory::getQuantity)
                .filter(qty -> qty != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.debug("产品【{}】批次总数量：{}", productId, totalQuantity);
            return totalQuantity;
        } catch (Exception e) {
            log.error("汇总产品批次数量失败 - 产品ID: {}, 错误: {}", productId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 按产品汇总可用数量
     */
    @Override
    public BigDecimal sumAvailableQuantityByProductId(Long productId) {
        try {
            LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inventory::getProductId, productId);
            wrapper.eq(Inventory::getInventoryStatus, InventoryStatus.AVAILABLE);
            wrapper.select(Inventory::getQuantity);

            List<Inventory> batches = baseMapper.selectList(wrapper);
            BigDecimal availableQuantity = batches.stream()
                .map(Inventory::getQuantity)
                .filter(qty -> qty != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.debug("产品【{}】可用数量：{}", productId, availableQuantity);
            return availableQuantity;
        } catch (Exception e) {
            log.error("汇总产品可用数量失败 - 产品ID: {}, 错误: {}", productId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 按产品和库位汇总可用数量
     */
    @Override
    public BigDecimal sumAvailableQuantityByProduct(Long productId, Long locationId) {
        try {
            LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inventory::getProductId, productId);
            if (locationId != null) {
                wrapper.eq(Inventory::getLocationId, locationId);
            }
            wrapper.eq(Inventory::getInventoryStatus, InventoryStatus.AVAILABLE);
            wrapper.select(Inventory::getQuantity);

            List<Inventory> batches = baseMapper.selectList(wrapper);
            BigDecimal availableQuantity = batches.stream().map(Inventory::getQuantity).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            log.debug("产品【{}】库位【{}】可用数量：{}", productId, locationId, availableQuantity);
            return availableQuantity;
        } catch (Exception e) {
            log.error("汇总产品库位可用数量失败 - 产品ID: {}, 库位ID: {}, 错误: {}", productId, locationId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 带并发控制的库存扣减（FIFO）
     * 使用SELECT FOR UPDATE防止并发超卖
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deductInventoryWithLock(Long productId, Long locationId, BigDecimal deductQty,
                                           String reason, Long operatorId, String operatorName) {
        try {
            log.info("开始并发安全库存扣减 - 产品ID: {}, 库位ID: {}, 扣减数量: {}, 原因: {}, 操作人: {}", productId, locationId, deductQty, reason, operatorName);

            // 参数校验
            if (productId == null || locationId == null || deductQty == null ||
                deductQty.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("扣减参数无效");
            }

            // 使用SELECT FOR UPDATE锁定可用批次，防止并发超卖
            List<Inventory> availableBatches = baseMapper.selectAvailableBatchesForUpdate(productId, locationId);
            if (availableBatches.isEmpty()) {
                throw new ServiceException("没有可用的库存进行扣减");
            }

            // 检查总可用数量是否足够
            BigDecimal totalAvailable = availableBatches.stream()
                .map(Inventory::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (totalAvailable.compareTo(deductQty) < 0) {
                throw new ServiceException("库存不足，可用数量：" + totalAvailable + "，需要数量：" + deductQty);
            }

            // FIFO扣减
            BigDecimal remainingDeduct = deductQty;
            for (Inventory batch : availableBatches) {
                if (remainingDeduct.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }

                BigDecimal batchAvailable = batch.getQuantity();
                BigDecimal deductFromBatch = remainingDeduct.min(batchAvailable);

                // 更新批次数量
                batch.setQuantity(batchAvailable.subtract(deductFromBatch));

                // 如果批次数量为0，设置状态为冻结（表示已用完）
                if (batch.getQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    batch.setInventoryStatus(InventoryStatus.FROZEN);
                }

                baseMapper.updateById(batch);

                remainingDeduct = remainingDeduct.subtract(deductFromBatch);
                log.info("并发安全FIFO扣减批次 - 批次: {}, 扣减数量: {}, 剩余数量: {}",
                    batch.getInternalBatchNumber(), deductFromBatch, batch.getQuantity());
            }

            log.info("并发安全库存扣减成功 - 产品ID: {}, 库位ID: {}, 扣减数量: {}",
                productId, locationId, deductQty);
            return true;

        } catch (Exception e) {
            log.error("并发安全库存扣减失败 - 产品ID: {}, 库位ID: {}, 扣减数量: {}, 错误: {}",
                productId, locationId, deductQty, e.getMessage(), e);
            throw new ServiceException("库存扣减失败：" + e.getMessage());
        }
    }

    /**
     * 冻结指定库位的批次
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean freezeBatchesByLocation(Long productId, Long locationId, BigDecimal freezeQty,
                                           String freezeReason, Long operatorId, String operatorName) {
        try {
            // 获取可用批次（按FIFO排序）
            LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inventory::getProductId, productId);
            wrapper.eq(Inventory::getLocationId, locationId);
            wrapper.eq(Inventory::getInventoryStatus, InventoryStatus.AVAILABLE);
            wrapper.gt(Inventory::getQuantity, BigDecimal.ZERO);
            wrapper.orderByAsc(Inventory::getCreateTime);

            List<Inventory> availableBatches = baseMapper.selectList(wrapper);
            if (availableBatches.isEmpty()) {
                throw new ServiceException("没有可用的库存进行冻结");
            }

            BigDecimal remainingFreeze = freezeQty;
            for (Inventory batch : availableBatches) {
                if (remainingFreeze.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }

                BigDecimal batchAvailable = batch.getQuantity();
                BigDecimal freezeFromBatch = remainingFreeze.min(batchAvailable);

                // 更新批次状态为冻结
                batch.setInventoryStatus(InventoryStatus.FROZEN);
                batch.setRemark("冻结原因：" + freezeReason);
                baseMapper.updateById(batch);

                remainingFreeze = remainingFreeze.subtract(freezeFromBatch);
                log.info("冻结批次 - 批次: {}, 冻结数量: {}", batch.getInternalBatchNumber(), freezeFromBatch);
            }

            if (remainingFreeze.compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException("可用库存不足，无法完成冻结。缺少数量：" + remainingFreeze);
            }

            return true;
        } catch (Exception e) {
            log.error("冻结批次失败 - 产品ID: {}, 库位ID: {}, 冻结数量: {}, 错误: {}",
                productId, locationId, freezeQty, e.getMessage(), e);
            throw new ServiceException("冻结批次失败：" + e.getMessage());
        }
    }

    /**
     * 解冻指定库位的批次
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean unfreezeBatchesByLocation(Long productId, Long locationId, BigDecimal unfreezeQty,
                                             String unfreezeReason, Long operatorId, String operatorName) {
        try {
            // 获取冻结批次
            LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inventory::getProductId, productId);
            wrapper.eq(Inventory::getLocationId, locationId);
            wrapper.eq(Inventory::getInventoryStatus, InventoryStatus.FROZEN);
            wrapper.gt(Inventory::getQuantity, BigDecimal.ZERO);
            wrapper.orderByAsc(Inventory::getCreateTime);

            List<Inventory> frozenBatches = baseMapper.selectList(wrapper);
            if (frozenBatches.isEmpty()) {
                throw new ServiceException("没有冻结的库存进行解冻");
            }

            BigDecimal remainingUnfreeze = unfreezeQty;
            for (Inventory batch : frozenBatches) {
                if (remainingUnfreeze.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }

                BigDecimal batchQuantity = batch.getQuantity();
                BigDecimal unfreezeFromBatch = remainingUnfreeze.min(batchQuantity);

                // 更新批次状态为可用
                batch.setInventoryStatus(InventoryStatus.AVAILABLE);
                // TODO: Inventory实体中没有availableQuantity字段
                // batch.setAvailableQuantity(unfreezeFromBatch);
                batch.setRemark("解冻原因：" + unfreezeReason);
                baseMapper.updateById(batch);

                remainingUnfreeze = remainingUnfreeze.subtract(unfreezeFromBatch);
                log.info("解冻批次 - 批次: {}, 解冻数量: {}", batch.getInternalBatchNumber(), unfreezeFromBatch);
            }

            if (remainingUnfreeze.compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException("冻结库存不足，无法完成解冻。缺少数量：" + remainingUnfreeze);
            }

            return true;
        } catch (Exception e) {
            log.error("解冻批次失败 - 产品ID: {}, 库位ID: {}, 解冻数量: {}, 错误: {}",
                productId, locationId, unfreezeQty, e.getMessage(), e);
            throw new ServiceException("解冻批次失败：" + e.getMessage());
        }
    }

    /**
     * 解析库存状态
     */
    private InventoryStatus parseInventoryStatus(InventoryStatus status) {
        for (InventoryStatus batchStatus : InventoryStatus.values()) {
            if (batchStatus.equals(status)) {
                return batchStatus;
            }
        }
        throw new ServiceException("无效的库存状态：" + status);
    }

    /**
     * 记录状态变更日志
     */
    private void recordStatusChangeLog(Long batchId, InventoryStatus oldStatus,
                                       InventoryStatus newStatus, String reason,
                                       Long operatorId, String operatorName) {
        try {
            // 记录状态变更日志（基础实现）
            log.info("批次状态变更日志 - 批次ID: {}, 旧状态: {}, 新状态: {}, 原因: {}, 操作人: {}({})",
                batchId, oldStatus, newStatus, reason, operatorName, operatorId);

            // TODO: 可扩展为持久化到数据库的状态变更日志表
            // 当需要详细的审计日志时，可以创建专门的日志表进行记录
        } catch (Exception e) {
            log.warn("记录状态变更日志失败: {}", e.getMessage());
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 检查库存可用性
     *
     * @param productId  产品ID
     * @param locationId 库位ID
     * @param requireQty 需求数量
     * @return 是否有足够可用库存
     */
    @Override
    public Boolean checkInventoryAvailability(Long productId, Long locationId, BigDecimal requireQty) {
        if (productId == null || requireQty == null || requireQty.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        BigDecimal availableQty = getAvailableQuantity(productId, locationId);
        return availableQty.compareTo(requireQty) >= 0;
    }

    /**
     * 获取产品可用库存数量
     *
     * @param productId  产品ID
     * @param locationId 库位ID（可选，为null时查询所有库位）
     * @return 可用库存数量
     */
    @Override
    public BigDecimal getAvailableQuantity(Long productId, Long locationId) {
        if (productId == null) {
            return BigDecimal.ZERO;
        }

        // 从Inventory汇总可用数量
        // 通过批次服务计算实际可用数量（排除冻结、预留等状态的库存）
        try {
            // 使用真实的汇总逻辑
            BigDecimal availableQuantity = sumAvailableQuantityByProduct(productId, locationId);
            log.debug("产品【{}】库位【{}】可用库存数量：{}", productId, locationId, availableQuantity);
            return availableQuantity;
        } catch (Exception e) {
            log.error("获取可用库存数量失败 - 产品ID: {}, 库位ID: {}, 错误: {}", productId, locationId, e.getMessage(), e);

            // 降级处理：返回总库存数量
            LambdaQueryWrapper<Inventory> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Inventory::getProductId, productId);
            wrapper.select(Inventory::getQuantity);

            List<Inventory> inventories = baseMapper.selectList(wrapper);
            BigDecimal totalQuantity = inventories.stream()
                .map(Inventory::getQuantity)
                .filter(qty -> qty != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.warn("使用总库存数量作为可用数量 - 产品【{}】总库存数量：{}", productId, totalQuantity);
            return totalQuantity;
        }
    }
}
