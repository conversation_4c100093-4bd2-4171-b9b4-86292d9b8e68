package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.wms.domain.OutboundItemBatch;
import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.bo.OutboundItemBatchBo;
import com.iotlaser.spms.wms.domain.vo.InventoryVo;
import com.iotlaser.spms.wms.domain.vo.OutboundItemBatchVo;
import com.iotlaser.spms.wms.domain.vo.OutboundVo;
import com.iotlaser.spms.wms.enums.OutboundStatus;
import com.iotlaser.spms.wms.mapper.OutboundItemBatchMapper;
import com.iotlaser.spms.wms.service.IInventoryService;
import com.iotlaser.spms.wms.service.IOutboundItemBatchService;
import com.iotlaser.spms.wms.service.IOutboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品出库批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OutboundItemBatchServiceImpl implements IOutboundItemBatchService {

    private final OutboundItemBatchMapper baseMapper;
    private final IInventoryService inventoryService;
    private final ILocationService locationService;
    @Lazy
    @Autowired
    private IOutboundService outboundService;

    /**
     * 查询产品出库批次明细
     *
     * @param batchId 主键
     * @return 产品出库批次明细
     */
    @Override
    public OutboundItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询产品出库批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品出库批次明细分页列表
     */
    @Override
    public TableDataInfo<OutboundItemBatchVo> queryPageList(OutboundItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OutboundItemBatch> lqw = buildQueryWrapper(bo);
        Page<OutboundItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品出库批次明细列表
     *
     * @param bo 查询条件
     * @return 产品出库批次明细列表
     */
    @Override
    public List<OutboundItemBatchVo> queryList(OutboundItemBatchBo bo) {
        LambdaQueryWrapper<OutboundItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OutboundItemBatch> buildQueryWrapper(OutboundItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OutboundItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(OutboundItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, OutboundItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getOutboundId() != null, OutboundItemBatch::getOutboundId, bo.getOutboundId());
        lqw.eq(bo.getInventoryId() != null, OutboundItemBatch::getInventoryId, bo.getInventoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), OutboundItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), OutboundItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), OutboundItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, OutboundItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), OutboundItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), OutboundItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, OutboundItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), OutboundItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), OutboundItemBatch::getUnitName, bo.getUnitName());

        // ✅ 优化：移除数量和价格的精确匹配查询，这些字段用等于查询没有实际业务意义
        // 原代码：lqw.eq(bo.getQuantity() != null, OutboundItemBatch::getQuantity, bo.getQuantity());
        // 原代码：lqw.eq(bo.getPrice() != null, OutboundItemBatch::getPrice, bo.getPrice());
        // TODO: 如需要可以后续添加数量和价格的范围查询支持

        lqw.eq(bo.getLocationId() != null, OutboundItemBatch::getLocationId, bo.getLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getLocationCode()), OutboundItemBatch::getLocationCode, bo.getLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getLocationName()), OutboundItemBatch::getLocationName, bo.getLocationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), OutboundItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品出库批次明细
     *
     * @param bo 产品出库批次明细
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OutboundItemBatchBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        OutboundItemBatch add = MapstructUtils.convert(bo, OutboundItemBatch.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBatchId(add.getBatchId());
        }
        return flag;
    }

    /**
     * 修改产品出库批次明细
     *
     * @param bo 产品出库批次明细
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OutboundItemBatchBo bo) {
        //填充冗余信息
        fillRedundantFields(bo);
        OutboundItemBatch update = MapstructUtils.convert(bo, OutboundItemBatch.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     * <p>
     * 注意：字段非空校验、数据类型校验、格式校验等基础校验已移至Bo类的注解实现
     * 当前方法只负责核心业务逻辑校验（当前无特殊业务逻辑校验需求）
     *
     * @param entity 出库批次明细实体
     */
    private void validEntityBeforeSave(OutboundItemBatch entity) {
        // 当前无特殊业务逻辑校验需求
        // 所有基础校验已移至Bo类注解实现
    }

    /**
     * 校验并批量删除产品出库批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<OutboundItemBatch> batches = baseMapper.selectByIds(ids);
            for (OutboundItemBatch batch : batches) {
                // 检查主表状态，只有草稿状态的出库批次才能删除
                OutboundVo outbound = outboundService.queryById(batch.getOutboundId());
                if (outbound == null) {
                    throw new ServiceException("出库批次关联的出库单不存在，批次号：" + batch.getInternalBatchNumber());
                }
                if (!OutboundStatus.PENDING_PICKING.getValue().equals(outbound.getOutboundStatus())) {
                    throw new ServiceException("出库批次所属出库单【" + outbound.getOutboundCode() +
                        "】状态为【" + outbound.getOutboundStatus() + "】，不允许删除批次");
                }

                // 检查是否已关联库存记录
                // 通过内部批次号查询是否已生成库存
                if (StringUtils.isNotBlank(batch.getInternalBatchNumber())) {
                    InventoryBo queryBo = new InventoryBo();
                    queryBo.setInternalBatchNumber(batch.getInternalBatchNumber());
                    List<InventoryVo> inventoryes = inventoryService.queryList(queryBo);
                    if (!inventoryes.isEmpty()) {
                        throw new ServiceException("出库批次【" + batch.getInternalBatchNumber() +
                            "】已关联库存记录，不允许删除");
                    }
                    log.debug("出库批次【{}】库存关联检查通过", batch.getInternalBatchNumber());
                }

                log.info("删除出库批次明细校验通过，批次号：{}", batch.getInternalBatchNumber());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除出库批次成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除出库批次失败：{}", e.getMessage(), e);
            throw new ServiceException("删除出库批次失败：" + e.getMessage());
        }
    }

    /**
     * 批量新增或更新产品出库批次明细
     * ✅ 统一使用insertOrUpdateBatch方法，避免重复的批量插入方法
     *
     * @param batches 批次明细BO列表
     * @return 是否操作成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertOrUpdateBatch(List<OutboundItemBatchBo> batches) {
        List<OutboundItemBatch> entities = batches.stream()
            .map(bo -> MapstructUtils.convert(bo, OutboundItemBatch.class))
            .collect(Collectors.toList());
        return baseMapper.insertBatch(entities);
    }

    /**
     * 根据明细ID查询
     *
     * @param itemId 明细ID
     * @return 库存记录
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<OutboundItemBatchVo> queryByItemId(Long itemId) {
        LambdaQueryWrapper<OutboundItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.eq(OutboundItemBatch::getItemId, itemId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(OutboundItemBatchBo bo) {
        // 填充位置信息
        if (bo.getLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getLocationId());
            if (vo != null) {
                bo.setLocationCode(vo.getLocationCode());
                bo.setLocationName(vo.getLocationName());
            }
        }
    }
}
