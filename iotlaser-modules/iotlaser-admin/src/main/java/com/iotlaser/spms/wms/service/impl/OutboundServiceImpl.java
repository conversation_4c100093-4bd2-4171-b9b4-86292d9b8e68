package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundItemVo;
import com.iotlaser.spms.erp.domain.vo.SaleOutboundVo;
import com.iotlaser.spms.pro.service.IProductService;
import com.iotlaser.spms.wms.domain.Outbound;
import com.iotlaser.spms.wms.domain.OutboundItem;
import com.iotlaser.spms.wms.domain.OutboundItemBatch;
import com.iotlaser.spms.wms.domain.bo.InventoryBo;
import com.iotlaser.spms.wms.domain.bo.OutboundBo;
import com.iotlaser.spms.wms.domain.bo.OutboundItemBo;
import com.iotlaser.spms.wms.domain.vo.*;
import com.iotlaser.spms.wms.enums.*;
import com.iotlaser.spms.wms.mapper.OutboundItemBatchMapper;
import com.iotlaser.spms.wms.mapper.OutboundItemMapper;
import com.iotlaser.spms.wms.mapper.OutboundMapper;
import com.iotlaser.spms.wms.service.IInventoryLogService;
import com.iotlaser.spms.wms.service.IInventoryService;
import com.iotlaser.spms.wms.service.IOutboundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static com.iotlaser.spms.base.enums.GenCodeType.WMS_OUTBOUND_CODE;
import static org.dromara.common.core.constant.SystemConstants.YES;
import static org.dromara.common.satoken.utils.LoginHelper.getLoginUser;

/**
 * 产品出库Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OutboundServiceImpl implements IOutboundService {

    private final OutboundMapper baseMapper;
    private final OutboundItemMapper itemMapper;
    private final OutboundItemBatchMapper batchMapper;
    private final Gen gen;
    private final IInventoryLogService inventoryLogService;
    private final IInventoryService inventoryService;
    private final IProductService productService;
    private final ILocationService locationService;

    /**
     * 查询产品出库
     *
     * @param outboundId 主键
     * @return 产品出库
     */
    @Override
    public OutboundVo queryById(Long outboundId) {
        return baseMapper.selectVoById(outboundId);
    }

    /**
     * 分页查询产品出库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品出库分页列表
     */
    @Override
    public TableDataInfo<OutboundVo> queryPageList(OutboundBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Outbound> lqw = buildQueryWrapper(bo);
        Page<OutboundVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品出库列表
     *
     * @param bo 查询条件
     * @return 产品出库列表
     */
    @Override
    public List<OutboundVo> queryList(OutboundBo bo) {
        LambdaQueryWrapper<Outbound> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * TODO 需完善 根据来源ID和来源类型查询仓库出库单列表
     *
     * @param sourceId   来源ID
     * @param sourceType 来源类型
     * @return 仓库出库单列表
     */
    @Override
    public List<OutboundVo> queryBySourceId(Long sourceId, SourceType sourceType) {
        if (sourceId == null || sourceType == null) {
            throw new ServiceException("来源ID和来源类型不能为空");
        }

        LambdaQueryWrapper<Outbound> wrapper = Wrappers.lambdaQuery();
        //wrapper.eq(Outbound::getSourceId, sourceId);
        //wrapper.eq(Outbound::getSourceType, sourceType);
        wrapper.eq(Outbound::getStatus, "1"); // 只查询有效记录
        wrapper.orderByDesc(Outbound::getCreateTime);

        try {
            List<OutboundVo> result = baseMapper.selectVoList(wrapper);
            log.info("根据来源查询仓库出库单完成 - 来源ID: {}, 来源类型: {}, 结果数量: {}",
                sourceId, sourceType, result.size());
            return result;
        } catch (Exception e) {
            log.error("根据来源查询仓库出库单失败 - 来源ID: {}, 来源类型: {}, 错误: {}",
                sourceId, sourceType, e.getMessage(), e);
            throw new ServiceException("查询仓库出库单失败：" + e.getMessage());
        }
    }

    private LambdaQueryWrapper<Outbound> buildQueryWrapper(OutboundBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Outbound> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Outbound::getOutboundId);
        lqw.eq(StringUtils.isNotBlank(bo.getOutboundCode()), Outbound::getOutboundCode, bo.getOutboundCode());
        if (bo.getOutboundStatus() != null) {
            lqw.eq(Outbound::getOutboundStatus, bo.getOutboundStatus());
        }
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Outbound::getStatus, bo.getStatus());
        lqw.between(params.get("beginOutboundTime") != null && params.get("endOutboundTime") != null,
            Outbound::getOutboundTime, params.get("beginOutboundTime"), params.get("endOutboundTime"));
        return lqw;
    }

    /**
     * 新增产品出库
     *
     * @param bo 产品出库
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutboundVo insertByBo(OutboundBo bo) {
        try {
            // 生成出库单号
            if (StringUtils.isEmpty(bo.getOutboundCode())) {
                bo.setOutboundCode(gen.code(WMS_OUTBOUND_CODE));
            }
            // 设置初始状态
            if (bo.getOutboundStatus() == null) {
                bo.setOutboundStatus(OutboundStatus.PENDING_PICKING);
            }
            if (bo.getOutboundTime() == null) {
                bo.setOutboundTime(LocalDateTime.now());
            }
            // 转换为实体并校验
            Outbound add = MapstructUtils.convert(bo, Outbound.class);
            validEntityBeforeSave(add);
            // 插入主表
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增产品出库失败");
            } else {
                if(add.getSourceType() == null) {
                    add.setSourceType(SourceType.OUTBOUND);
                    add.setSourceId(add.getOutboundId());
                    add.setSourceCode(add.getOutboundCode());
                }
                if(add.getDirectSourceType() == null) {
                    add.setDirectSourceId(add.getOutboundId());
                    add.setDirectSourceCode(add.getOutboundCode());
                    add.setDirectSourceType(DirectSourceType.INVENTORY_ADJUSTMENT);
                }
                baseMapper.updateById(add);
            }
            bo.setOutboundId(add.getOutboundId());
            log.info("新增产品出库成功：{}", add.getOutboundCode());
            return MapstructUtils.convert(add, OutboundVo.class);
        } catch (Exception e) {
            log.error("新增产品出库失败：{}", e.getMessage(), e);
            throw new ServiceException("新增产品出库失败：" + e.getMessage());
        }
    }

    /**
     * 修改产品出库
     *
     * @param bo 产品出库
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OutboundVo updateByBo(OutboundBo bo) {
        try {
            // 转换为实体并校验
            Outbound update = MapstructUtils.convert(bo, Outbound.class);
            validEntityBeforeSave(update);
            // 更新主表
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改产品出库失败：出库单不存在或数据未变更");
            }
            log.info("修改产品出库成功：{}", update.getOutboundCode());
            return MapstructUtils.convert(update, OutboundVo.class);
        } catch (Exception e) {
            log.error("修改产品出库失败：{}", e.getMessage(), e);
            throw new ServiceException("修改产品出库失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Outbound entity) {
        // 校验出库单编码唯一性
        if (StringUtils.isNotBlank(entity.getOutboundCode())) {
            LambdaQueryWrapper<Outbound> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Outbound::getOutboundCode, entity.getOutboundCode());
            if (entity.getOutboundId() != null) {
                wrapper.ne(Outbound::getOutboundId, entity.getOutboundId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("出库单编码已存在：" + entity.getOutboundCode());
            }
        }
    }

    /**
     * 校验并批量删除产品出库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验出库单是否可以删除
            List<Outbound> outbounds = baseMapper.selectByIds(ids);
            for (Outbound outbound : outbounds) {
                // 检查出库单状态，只有待拣货和拣货中状态的出库单才能删除
                if (OutboundStatus.PENDING_PICKING != outbound.getOutboundStatus() && OutboundStatus.PICKING_IN_PROGRESS != outbound.getOutboundStatus()) {
                    throw new ServiceException("出库单【" + outbound.getOutboundCode() + "】状态为【" + outbound.getOutboundStatus() + "】，不允许删除");
                }
            }
        }

        try {
            int batchResult = batchMapper.deleteByOutboundIds(ids);
            if (batchResult > 0) {
                log.info("批量删除出库单成功，删除批次数量：{}", batchResult);
            }
            int itemResult = itemMapper.deleteByOutboundIds(ids);
            if (itemResult > 0) {
                log.info("批量删除出库单成功，删除明细数量：{}", itemResult);
            }
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除出库单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除出库单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除出库单失败：" + e.getMessage());
        }
    }


    /**
     * 查询产品出库明细表及其关联信息
     *
     * @param itemId 主键
     * @return 产品出库明细表
     */
    @Override
    public OutboundItemVo queryItemById(Long itemId) {
        return MapstructUtils.convert(itemMapper.queryById(itemId), OutboundItemVo.class);
    }

    /**
     * 查询产品出库明细表列表及其关联信息
     *
     * @param directSourceId 上游ID
     * @return 产品出库明细表列表
     */
    @Override
    public List<OutboundItemVo> queryItemByDirectSourceId(Long directSourceId, DirectSourceType directSourceType) {
        OutboundItemBo bo = new OutboundItemBo();
        bo.setDirectSourceId(directSourceId);
        bo.setDirectSourceType(directSourceType);
        QueryWrapper<OutboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return MapstructUtils.convert(itemMapper.queryPageList(null, queryWrapper), OutboundItemVo.class);
    }

    /**
     * 查询产品出库明细表列表及其关联信息
     *
     * @param bo 查询条件
     * @return 产品出库明细表列表
     */
    @Override
    public List<OutboundItemVo> queryItemList(OutboundItemBo bo) {
        QueryWrapper<OutboundItem> queryWrapper = buildQueryWrapperWith(bo);
        return MapstructUtils.convert(itemMapper.queryPageList(null, queryWrapper), OutboundItemVo.class);
    }

    /**
     * 分页查询产品出库明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品出库明细表分页列表
     */
    @Override
    public TableDataInfo<OutboundItemVo> queryItemPageList(OutboundItemBo bo, PageQuery pageQuery) {
        QueryWrapper<OutboundItem> queryWrapper = buildQueryWrapperWith(bo);
        List<OutboundItemVo> result = MapstructUtils.convert(itemMapper.queryPageList(pageQuery.build(), queryWrapper), OutboundItemVo.class);
        return TableDataInfo.build(result);
    }

    private QueryWrapper<OutboundItem> buildQueryWrapperWith(OutboundItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<OutboundItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getOutboundId() != null, "item.outbound_id", bo.getOutboundId());
        wrapper.eq(bo.getInventoryId() != null, "item.inventory_id", bo.getInventoryId());
        wrapper.eq(bo.getSourceId() != null, "item.source_id", bo.getSourceId());
        if (bo.getSourceType() != null) {
            wrapper.eq("item.source_type", bo.getSourceType());
        }
        wrapper.eq(bo.getDirectSourceId() != null, "item.direct_source_id", bo.getDirectSourceId());
        if (bo.getDirectSourceType() != null) {
            wrapper.eq("item.direct_source_type", bo.getDirectSourceType());
        }
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getLocationId() != null, "item.location_id", bo.getLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getLocationCode()), "item.location_code", bo.getLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getLocationName()), "item.location_name", bo.getLocationName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

    /**
     * 根据销售出库单创建仓库出库单
     *
     * @param outboundVo 销售出库单
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromSaleOutbound(SaleOutboundVo outboundVo) {
        try {
            Outbound add = new Outbound();
            add.setOutboundCode(gen.code(WMS_OUTBOUND_CODE));

            add.setSourceId(outboundVo.getSourceId());
            add.setSourceCode(outboundVo.getSourceCode());
            add.setSourceType(outboundVo.getSourceType());
            add.setDirectSourceId(outboundVo.getOutboundId());
            add.setDirectSourceCode(outboundVo.getOutboundCode());
            add.setDirectSourceType(DirectSourceType.SALE_OUTBOUND);

            add.setOutboundStatus(OutboundStatus.PENDING_PICKING);
            add.setOutboundTime(LocalDateTime.now());

            LoginUser loginUser = getLoginUser();
            if (loginUser != null) {
                add.setPackerId(loginUser.getUserId());
                add.setPackerName(loginUser.getNickname());
            }
            add.setSummary("基于销售出库单【" + outboundVo.getOutboundCode() + "】创建");

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("创建仓库出库单失败");
            }

            List<OutboundItem> inboundItems = new ArrayList<>();
            for (SaleOutboundItemVo itemVo : outboundVo.getItems()) {
                OutboundItem item = new OutboundItem();
                item.setOutboundId(add.getOutboundId());

                item.setSourceId(outboundVo.getSourceId());
                item.setSourceCode(outboundVo.getSourceCode());
                item.setSourceType(outboundVo.getSourceType());

                item.setDirectSourceId(outboundVo.getOutboundId());
                item.setDirectSourceCode(outboundVo.getOutboundCode());
                item.setDirectSourceType(DirectSourceType.SALE_OUTBOUND);
                item.setDirectSourceItemId(itemVo.getItemId());

                item.setProductId(itemVo.getProductId());
                item.setProductCode(itemVo.getProductCode());
                item.setProductName(itemVo.getProductName());
                item.setUnitId(itemVo.getUnitId());
                item.setUnitCode(itemVo.getUnitCode());
                item.setUnitName(itemVo.getUnitName());

                item.setLocationId(itemVo.getLocationId());
                item.setLocationCode(itemVo.getLocationCode());
                item.setLocationName(itemVo.getLocationName());

                item.setQuantity(itemVo.getQuantity());
                item.setFinishQuantity(itemVo.getFinishQuantity());
                item.setCostPrice(itemVo.getPriceExclusiveTax());
                inboundItems.add(item);
            }
            boolean insertItem = itemMapper.insertBatch(inboundItems);
            if (!insertItem) {
                throw new ServiceException("基于销售出库单创建仓库出库单失败");
            }
            log.info("基于销售出库单【{}】创建仓库出库单【{}】成功", outboundVo.getOutboundCode(), add.getOutboundCode());
        } catch (Exception e) {
            log.error("基于销售出库单创建仓库出库单失败：{}", e.getMessage(), e);
            throw new ServiceException("基于销售出库单创建仓库出库单失败：" + e.getMessage());
        }
        return true;
    }


    /**
     * 基于仓库移库单创建仓库出库单
     *
     * @param transferVo 仓库移库单
     * @return 是否创建成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createFromTransfer(TransferVo transferVo) {
        try {
            Outbound add = new Outbound();
            add.setOutboundCode(gen.code(WMS_OUTBOUND_CODE));

            add.setSourceId(transferVo.getTransferId());
            add.setSourceCode(transferVo.getTransferCode());
            add.setSourceType(SourceType.TRANSFER);

            add.setDirectSourceId(transferVo.getTransferId());
            add.setDirectSourceCode(transferVo.getTransferCode());
            add.setDirectSourceType(DirectSourceType.TRANSFER);

            add.setOutboundStatus(OutboundStatus.PENDING_PICKING);
            add.setOutboundTime(transferVo.getTransferTime());

            LoginUser loginUser = getLoginUser();
            if (loginUser != null) {
                add.setPackerId(loginUser.getUserId());
                add.setPackerName(loginUser.getNickname());
            }
            add.setSummary("基于仓库移库单【" + transferVo.getTransferCode() + "】创建");

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("创建仓库出库单失败");
            }
            if (baseMapper.insert(add) > 0) {
                List<OutboundItem> items = new ArrayList<>();
                List<TransferItemVo> transferItems = transferVo.getItems();
                for (TransferItemVo transferItem : transferItems) {
                    OutboundItem item = new OutboundItem();
                    item.setOutboundId(add.getOutboundId());
                    item.setSourceId(transferVo.getTransferId());
                    item.setSourceCode(transferVo.getTransferCode());
                    item.setSourceType(SourceType.TRANSFER);

                    item.setDirectSourceId(transferVo.getTransferId());
                    item.setDirectSourceCode(transferVo.getTransferCode());
                    item.setDirectSourceType(DirectSourceType.TRANSFER);
                    item.setDirectSourceItemId(transferItem.getItemId());

                    item.setProductId(transferItem.getProductId());
                    item.setProductCode(transferItem.getProductCode());
                    item.setProductName(transferItem.getProductName());
                    item.setLocationId(transferItem.getFromLocationId());
                    item.setLocationCode(transferItem.getFromLocationCode());
                    item.setLocationName(transferItem.getFromLocationName());
                    item.setUnitId(transferItem.getUnitId());
                    item.setUnitCode(transferItem.getUnitCode());
                    item.setUnitName(transferItem.getUnitName());
                    item.setQuantity(transferItem.getQuantity());
                    item.setCostPrice(transferItem.getCostPrice());
                    item.setRemark("基于仓库移库单【" + transferVo.getTransferCode() + "】创建");

                    if (transferItem.getBatches() != null && !transferItem.getBatches().isEmpty()) {
                        List<OutboundItemBatch> itemBatches = new ArrayList<>();
                        for (TransferItemBatchVo transferBatch : transferItem.getBatches()) {
                            OutboundItemBatch batch = new OutboundItemBatch();
                            batch.setOutboundId(add.getOutboundId());
                            batch.setInternalBatchNumber(transferBatch.getInternalBatchNumber());
                            batch.setSupplierBatchNumber(transferBatch.getSupplierBatchNumber());
                            batch.setSerialNumber(transferBatch.getSerialNumber());
                            batch.setProductId(transferBatch.getProductId());
                            batch.setProductCode(transferBatch.getProductCode());
                            batch.setProductName(transferBatch.getProductName());
                            batch.setUnitId(transferBatch.getUnitId());
                            batch.setUnitCode(transferBatch.getUnitCode());
                            batch.setUnitName(transferBatch.getUnitName());
                            batch.setLocationId(transferBatch.getFromLocationId());
                            batch.setLocationCode(transferBatch.getFromLocationCode());
                            batch.setLocationName(transferBatch.getFromLocationName());
                            batch.setQuantity(transferBatch.getQuantity());
                            batch.setCostPrice(transferBatch.getCostPrice());
                            batch.setRemark("基于仓库移库单【" + transferVo.getTransferCode() + "】创建");
                            itemBatches.add(batch);
                        }
                        item.setBatches(itemBatches);
                    }
                    items.add(item);
                }
                boolean itemResult = itemMapper.insertBatch(items);
                if (!itemResult) {
                    throw new ServiceException("基于仓库移库单创建仓库出库单失败");
                }
                List<OutboundItemBatch> itemBatches = new ArrayList<>();
                for (OutboundItem item : items) {
                    if (item.getBatches() != null && !item.getBatches().isEmpty()) {
                        item.getBatches().forEach(batch -> {
                            batch.setItemId(item.getItemId());
                            itemBatches.add(batch);
                        });
                    }
                }
                boolean batchResult = batchMapper.insertBatch(itemBatches);
                if (!batchResult) {
                    throw new ServiceException("基于仓库移库单创建仓库出库单失败");
                }
                log.info("基于仓库移库单【{}】创建仓库出库单【{}】成功", transferVo.getTransferCode(), add.getOutboundCode());
            }
        } catch (Exception e) {
            log.error("基于仓库移库单创建仓库出库单失败 {}", e.getMessage(), e);
            throw new ServiceException("基于仓库移库单创建仓库出库单失败：" + e.getMessage());
        }
        return true;

    }

    /**
     * 校验库存充足性
     */
    private void validateInventoryAvailability(List<OutboundItemVo> items) {
        for (OutboundItemVo item : items) {
            // 校验库存数量是否充足
            BigDecimal availableQuantity = inventoryService.getAvailableQuantity(item.getProductId(), item.getLocationId());
            if (availableQuantity == null || availableQuantity.compareTo(item.getQuantity()) < 0) {
                throw new ServiceException(String.format("产品【%s】在库位【%s】的可用库存不足，需要：%s，可用：%s",
                    item.getProductName(), item.getLocationName(),
                    item.getQuantity(), availableQuantity != null ? availableQuantity : BigDecimal.ZERO));
            }
        }
    }

    /**
     * 确认仓库出库单
     *
     * @param outboundId 仓库出库单ID
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean confirmOutbound(Long outboundId) {
        try {
            Outbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("仓库出库单不存在");
            }
            // 校验仓库出库单状态
            if (OutboundStatus.PENDING_PICKING != outbound.getOutboundStatus()) {
                throw new ServiceException("只有待拣货状态的仓库出库单才能确认");
            }
            // 校验仓库出库单
            outboundValid(outbound, false);
            // 更新仓库出库单状态
            outbound.setOutboundStatus(OutboundStatus.PICKING_IN_PROGRESS);
            String currentRemark = StringUtils.isNotBlank(outbound.getRemark()) ? outbound.getRemark() : "";
            outbound.setRemark(currentRemark + " [确认时间：" + DateUtils.getTime() + "]");

            int result = baseMapper.updateById(outbound);
            if (result <= 0) {
                throw new ServiceException("确认仓库出库单失败");
            }

            log.info("确认仓库出库单成功：仓库出库单【{}】", outbound.getOutboundCode());
            return true;
        } catch (Exception e) {
            log.error("确认仓库出库单失败：{}", e.getMessage(), e);
            throw new ServiceException("确认仓库出库单失败：" + e.getMessage());
        }
    }

    /**
     * 批量确认仓库出库单
     *
     * @param outboundIds 仓库出库单ID集合
     * @return 是否确认成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean batchConfirmOutbounds(Collection<Long> outboundIds) {
        try {
            if (outboundIds == null || outboundIds.isEmpty()) {
                throw new ServiceException("仓库出库单ID集合不能为空");
            }

            int successCount = 0;
            for (Long outboundId : outboundIds) {
                try {
                    confirmOutbound(outboundId);
                    successCount++;
                } catch (Exception e) {
                    log.warn("批量确认仓库出库单失败，仓库出库单ID：{}，错误：{}", outboundId, e.getMessage());
                }
            }

            if (successCount == 0) {
                throw new ServiceException("批量确认失败，没有仓库出库单被成功确认");
            }

            log.info("批量确认仓库出库单完成：成功【{}】个，总计【{}】个", successCount, outboundIds.size());
            return true;
        } catch (Exception e) {
            log.error("批量确认仓库出库单失败：{}", e.getMessage(), e);
            throw new ServiceException("批量确认仓库出库单失败：" + e.getMessage());
        }
    }

    /**
     * 完成仓库出库
     *
     * @param outboundId 仓库出库单ID
     * @return 是否完成成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean completeOutbound(Long outboundId) {
        try {
            Outbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("仓库出库单不存在");
            }
            // 校验是否可以完成
            outboundValid(outbound, true);
            // 更新库存记录
            updateInventoryRecords(outbound);
            // 更新仓库出库单状态
            outbound.setOutboundStatus(OutboundStatus.COMPLETED);
            outbound.setOutboundTime(LocalDateTime.now());
            String currentRemark = StringUtils.isNotBlank(outbound.getRemark()) ? outbound.getRemark() : "";
            outbound.setRemark(currentRemark + " [完成仓库出库时间：" + DateUtils.getTime() + "]");

            int result = baseMapper.updateById(outbound);
            if (result <= 0) {
                throw new ServiceException("完成仓库出库失败");
            }

            log.info("完成仓库出库成功：仓库出库单【{}】", outbound.getOutboundCode());

            // TODO: [WMS出库完成后，回调ERP] - 参考文档 docs/design/README_FLOW.md
            // WMS出库单完成后，需要调用ERP模块的ISaleOutboundService，通知其更新状态为“已完成”。
            // if (result) {
            //     // 假设 saleOutboundService 是注入的ERP模块服务
            //     // saleOutboundService.completeFromWms(outbound.getDirectSourceId());
            // }

            return true;
        } catch (Exception e) {
            log.error("完成仓库出库失败：{}", e.getMessage(), e);
            throw new ServiceException("完成仓库出库失败：" + e.getMessage());
        }
    }

    /**
     * 取消仓库出库单
     *
     * @param outboundId   仓库出库单ID
     * @param cancelReason 取消原因
     * @return 是否取消成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelOutbound(Long outboundId, String cancelReason) {
        try {
            Outbound outbound = baseMapper.selectById(outboundId);
            if (outbound == null) {
                throw new ServiceException("仓库出库单不存在");
            }

            // 校验仓库出库单状态
            if (OutboundStatus.CANCELLED == outbound.getOutboundStatus()) {
                throw new ServiceException("仓库出库单已取消，不能重复操作");
            }

            if (OutboundStatus.COMPLETED == outbound.getOutboundStatus()) {
                throw new ServiceException("仓库出库单已完成，不能取消");
            }

            // 校验是否可以取消
            validateOutboundForCancel(outbound);

            // 更新仓库出库单状态和取消原因
            outbound.setOutboundStatus(OutboundStatus.CANCELLED);
            if (StringUtils.isNotBlank(cancelReason)) {
                String currentRemark = StringUtils.isNotBlank(outbound.getRemark()) ? outbound.getRemark() : "";
                outbound.setRemark(currentRemark + " [取消原因：" + cancelReason + "，取消时间：" + DateUtils.getTime() + "]");
            }

            int result = baseMapper.updateById(outbound);
            if (result <= 0) {
                throw new ServiceException("取消仓库出库单失败");
            }

            log.info("取消仓库出库单成功：仓库出库单【{}】，原因【{}】", outbound.getOutboundCode(), cancelReason);
            return true;
        } catch (Exception e) {
            log.error("取消仓库出库单失败：{}", e.getMessage(), e);
            throw new ServiceException("取消仓库出库单失败：" + e.getMessage());
        }
    }


    /**
     * 校验仓库出库单是否可以确认
     *
     * @param outbound 仓库出库单
     */
    private void outboundValid(Outbound outbound, boolean isComplete) {
        OutboundItemBo query = new OutboundItemBo();
        query.setOutboundId(outbound.getOutboundId());
        List<OutboundItemVo> itemVos = queryItemList(query);
        if (itemVos == null || itemVos.isEmpty()) {
            throw new ServiceException("仓库出库单【" + outbound.getOutboundCode() + "】缺少仓库出库明细");
        }

        //校验库存充足性
        validateInventoryAvailability(itemVos);

        for (OutboundItemVo itemVo : itemVos) {
            if (itemVo.getQuantity() == null || itemVo.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细通知通知出库数量不能小于等于0");
            }
            if (isComplete) {
                if (itemVo.getFinishQuantity() == null || itemVo.getFinishQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new ServiceException("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细已拣货数量不能小于等于0");
                }
                if (itemVo.getQuantity().compareTo(itemVo.getFinishQuantity()) != 0) {
                    throw new ServiceException("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细已拣货数量不能大于或小于应通知出库数量");
                }
            }
            if (isComplete) {
                if (itemVo.getProduct().getBatchFlag().equals(YES)) {
                    if (itemVo.getBatches().isEmpty()) {
                        throw new ServiceException("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细缺少批次信息");
                    }
                    BigDecimal totalQuantity = BigDecimal.ZERO;
                    for (OutboundItemBatchVo batchVo : itemVo.getBatches()) {
                        if (batchVo.getQuantity() == null || batchVo.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                            throw new ServiceException("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细【" + itemVo.getProductName() + "】批次【" + batchVo.getInternalBatchNumber() + "】数量不能小于等于0");
                        }
                        if (batchVo.getLocationId() == null) {
                            throw new ServiceException("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细【" + itemVo.getProductName() + "】批次【" + batchVo.getInternalBatchNumber() + "】未选择仓库出库位置");
                        }
                        totalQuantity = totalQuantity.add(batchVo.getQuantity());
                    }
                    if (!totalQuantity.equals(itemVo.getFinishQuantity())) {
                        throw new ServiceException("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细【" + itemVo.getProductName() + "】批次数量总和与已拣货数量不一致");
                    }
                } else {
                    if (itemVo.getLocationId() == null) {
                        throw new ServiceException("仓库出库单【" + outbound.getOutboundCode() + "】的仓库出库明细【" + itemVo.getProductName() + "】未选择仓库出库位置");
                    }
                }
            }
        }
        log.debug("仓库出库单【{}】确认校验通过", outbound.getOutboundCode());
    }

    /**
     * 校验仓库出库单是否可以取消
     *
     * @param outbound 仓库出库单
     */
    private void validateOutboundForCancel(Outbound outbound) {
        // 检查是否已有库存变动
        // TODO: 检查是否有关联的库存日志
        // 如果已有库存变动，需要特殊处理

        log.debug("仓库出库单【{}】取消校验通过", outbound.getOutboundCode());
    }

    /**
     * 更新库存记录
     *
     * @param outbound 仓库出库单
     */
    private void updateInventoryRecords(Outbound outbound) {
        try {
            // 基于仓库出库明细和批次进行库存操作（遵循仓库出库单→明细→批次的标准结构）
            // 实现完整的仓库出库库存处理流程
            // 获取仓库出库单明细
            OutboundItemBo query = new OutboundItemBo();
            query.setOutboundId(outbound.getOutboundId());
            List<OutboundItemVo> itemVos = queryItemList(query);
            for (OutboundItemVo itemVo : itemVos) {
                // 是否批次管理
                if (itemVo.getProduct().getBatchFlag().equals(YES)) {
                    // 基于仓库出库批次创建库存记录（批次）
                    processOutboundItemBatches(outbound, itemVo);
                } else {
                    // 基于仓库出库明细创建库存记录（明细）
                    createInventoryFromItem(outbound, itemVo);
                }
            }
            log.info("仓库出库单【{}】库存记录更新完成，处理明细数量：{}", outbound.getOutboundCode(), itemVos.size());
        } catch (Exception e) {
            log.error("仓库出库单【{}】库存记录更新失败：{}", outbound.getOutboundCode(), e.getMessage(), e);
            throw new ServiceException("库存记录更新失败：" + e.getMessage());
        }
    }

    /**
     * 基于仓库出库明细创建库存记录（遵循明细→批次的标准结构）
     */
    private void createInventoryFromItem(Outbound outbound, OutboundItemVo itemVo) {
        try {
            InventoryBo inventoryBo = new InventoryBo();
            inventoryBo.setManagementType(InventoryManagementType.NONE);

            inventoryBo.setSourceId(itemVo.getSourceId());
            inventoryBo.setSourceCode(itemVo.getSourceCode());
            inventoryBo.setSourceType(itemVo.getSourceType());

            inventoryBo.setDirectSourceId(itemVo.getDirectSourceId());
            inventoryBo.setDirectSourceCode(itemVo.getDirectSourceCode());
            inventoryBo.setDirectSourceType(itemVo.getDirectSourceType());
            inventoryBo.setDirectSourceItemId(itemVo.getItemId());
            inventoryBo.setDirectSourceBatchId(0L);

            inventoryBo.setProductId(itemVo.getProductId());
            inventoryBo.setProductCode(itemVo.getProductCode());
            inventoryBo.setProductName(itemVo.getProductName());
            inventoryBo.setUnitId(itemVo.getUnitId());
            inventoryBo.setUnitCode(itemVo.getUnitCode());
            inventoryBo.setUnitName(itemVo.getUnitName());
            inventoryBo.setLocationId(itemVo.getLocationId());
            inventoryBo.setLocationCode(itemVo.getLocationCode());
            inventoryBo.setLocationName(itemVo.getLocationName());

            // 数量取负数
            inventoryBo.setQuantity(itemVo.getQuantity().negate());
            inventoryBo.setCostPrice(itemVo.getCostPrice());

            inventoryBo.setInventoryTime(outbound.getOutboundTime());

            inventoryBo.setInventoryStatus(InventoryStatus.AVAILABLE); // 使用枚举类型
            inventoryBo.setRemark("仓库出库单：" + outbound.getOutboundCode());

            Boolean result = inventoryService.updateByBo(inventoryBo);
            if (!result) {
                throw new ServiceException("更新库存失败");
            }
            log.info("更新库存成功 - 产品编码: {}, 产品名称: {}, 数量: {}", itemVo.getProductCode(), itemVo.getProductName(), inventoryBo.getQuantity());
        } catch (Exception e) {
            log.error("基于明细 - 库存失败 - 产品编码: {}, 产品名称: {}, 错误: {}", itemVo.getProductCode(), itemVo.getProductName(), e.getMessage(), e);
            throw new ServiceException("基于明细 - 库存失败：" + e.getMessage());
        }
    }

    /**
     * TODO需完善 处理仓库出库明细的批次分配（遵循明细→批次的标准结构）
     */
    private void processOutboundItemBatches(Outbound outbound, OutboundItemVo item) {
        try {
            // 获取该明细的批次信息
            if (item.getBatches().isEmpty()) {
                log.debug("仓库出库明细【{}】没有预定义批次，使用默认批次处理", item.getItemId());
                return;
            }
            // 处理每个批次的库存操作
            for (OutboundItemBatchVo batch : item.getBatches()) {
                // 基于批次信息创建库存
                createInventoryFromItemBatch(outbound, item, batch);
                log.info("处理仓库出库明细批次 - 明细ID: {}, 批次: {}, 数量: {}", item.getItemId(), batch.getInternalBatchNumber(), batch.getQuantity());
            }
        } catch (Exception e) {
            log.error("处理仓库出库明细批次失败 - 明细ID: {}, 错误: {}", item.getItemId(), e.getMessage(), e);
            throw new ServiceException("处理仓库出库明细批次失败：" + e.getMessage());
        }
    }

    /**
     * 基于仓库出库明细批次创建库存
     */
    private void createInventoryFromItemBatch(Outbound outbound, OutboundItemVo itemVo, OutboundItemBatchVo batchVo) {
        try {
            InventoryBo inventoryBo = new InventoryBo();
            inventoryBo.setManagementType(InventoryManagementType.BATCH);
            inventoryBo.setInternalBatchNumber(batchVo.getInternalBatchNumber());
            inventoryBo.setSupplierBatchNumber(batchVo.getSupplierBatchNumber());
            inventoryBo.setSerialNumber(batchVo.getSerialNumber());

            inventoryBo.setSourceId(itemVo.getSourceId());
            inventoryBo.setSourceCode(itemVo.getSourceCode());
            inventoryBo.setSourceType(itemVo.getSourceType());

            inventoryBo.setDirectSourceId(itemVo.getDirectSourceId());
            inventoryBo.setDirectSourceCode(itemVo.getDirectSourceCode());
            inventoryBo.setDirectSourceType(itemVo.getDirectSourceType());
            inventoryBo.setDirectSourceItemId(itemVo.getItemId());
            inventoryBo.setDirectSourceBatchId(batchVo.getBatchId());

            inventoryBo.setProductId(itemVo.getProductId());
            inventoryBo.setProductCode(itemVo.getProductCode());
            inventoryBo.setProductName(itemVo.getProductName());
            inventoryBo.setUnitId(itemVo.getUnitId());
            inventoryBo.setUnitCode(itemVo.getUnitCode());
            inventoryBo.setUnitName(itemVo.getUnitName());
            inventoryBo.setLocationId(batchVo.getLocationId());
            inventoryBo.setLocationCode(batchVo.getLocationCode());
            inventoryBo.setLocationName(batchVo.getLocationName());
            //数量取负数
            inventoryBo.setQuantity(batchVo.getQuantity().negate());
            inventoryBo.setCostPrice(batchVo.getCostPrice());

            inventoryBo.setExpiryTime(batchVo.getExpiryTime());
            inventoryBo.setInventoryTime(outbound.getOutboundTime());

            inventoryBo.setInventoryStatus(InventoryStatus.AVAILABLE);

            inventoryBo.setRemark("仓库出库单明细批次：" + outbound.getOutboundCode() + "-" + batchVo.getInternalBatchNumber());

            Boolean result = inventoryService.insertByBo(inventoryBo);
            if (!result) {
                throw new ServiceException("创建库存失败");
            }
            log.info("创建库存成功 - 产品编码: {}, 产品名称: {}, 数量: {}", itemVo.getProductCode(), itemVo.getProductName(), inventoryBo.getQuantity());
        } catch (Exception e) {
            log.error("基于批次 - 库存失败 - 明细ID: {}, 批次: {}, 错误: {}", itemVo.getItemId(), batchVo.getInternalBatchNumber(), e.getMessage(), e);
            throw new ServiceException("基于批次 - 库存失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(OutboundBo bo) {
        // 填充来源信息（如果有来源ID但缺少来源信息）
        //if (bo.getSourceId() != null && StringUtils.isEmpty(bo.getSourceCode())) {
        // TODO: 根据来源类型填充来源信息
        // 这里需要根据具体的业务逻辑来实现
        //}

        //填充责任人信息
        //LoginUser loginUser = LoginHelper.getLoginUser();
    }
}
