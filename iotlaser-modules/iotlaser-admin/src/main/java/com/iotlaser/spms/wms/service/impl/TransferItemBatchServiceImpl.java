package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.wms.domain.TransferItemBatch;
import com.iotlaser.spms.wms.domain.bo.TransferItemBatchBo;
import com.iotlaser.spms.wms.domain.vo.TransferItemBatchVo;
import com.iotlaser.spms.wms.mapper.TransferItemBatchMapper;
import com.iotlaser.spms.wms.service.ITransferItemBatchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品移库批次明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/10
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TransferItemBatchServiceImpl implements ITransferItemBatchService {

    private final TransferItemBatchMapper baseMapper;
    private final ILocationService locationService;

    /**
     * 查询产品移库批次明细
     *
     * @param batchId 主键
     * @return 产品移库批次明细
     */
    @Override
    public TransferItemBatchVo queryById(Long batchId) {
        return baseMapper.selectVoById(batchId);
    }

    /**
     * 分页查询产品移库批次明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品移库批次明细分页列表
     */
    @Override
    public TableDataInfo<TransferItemBatchVo> queryPageList(TransferItemBatchBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TransferItemBatch> lqw = buildQueryWrapper(bo);
        Page<TransferItemBatchVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品移库批次明细列表
     *
     * @param bo 查询条件
     * @return 产品移库批次明细列表
     */
    @Override
    public List<TransferItemBatchVo> queryList(TransferItemBatchBo bo) {
        LambdaQueryWrapper<TransferItemBatch> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TransferItemBatch> buildQueryWrapper(TransferItemBatchBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TransferItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(TransferItemBatch::getBatchId);
        lqw.eq(bo.getItemId() != null, TransferItemBatch::getItemId, bo.getItemId());
        lqw.eq(bo.getTransferId() != null, TransferItemBatch::getTransferId, bo.getTransferId());
        lqw.eq(bo.getInventoryId() != null, TransferItemBatch::getInventoryId, bo.getInventoryId());
        lqw.eq(StringUtils.isNotBlank(bo.getInternalBatchNumber()), TransferItemBatch::getInternalBatchNumber, bo.getInternalBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierBatchNumber()), TransferItemBatch::getSupplierBatchNumber, bo.getSupplierBatchNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getSerialNumber()), TransferItemBatch::getSerialNumber, bo.getSerialNumber());
        lqw.eq(bo.getProductId() != null, TransferItemBatch::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), TransferItemBatch::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), TransferItemBatch::getProductName, bo.getProductName());
        lqw.eq(bo.getUnitId() != null, TransferItemBatch::getUnitId, bo.getUnitId());
        lqw.eq(StringUtils.isNotBlank(bo.getUnitCode()), TransferItemBatch::getUnitCode, bo.getUnitCode());
        lqw.like(StringUtils.isNotBlank(bo.getUnitName()), TransferItemBatch::getUnitName, bo.getUnitName());
        lqw.eq(bo.getQuantity() != null, TransferItemBatch::getQuantity, bo.getQuantity());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TransferItemBatch::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品移库批次明细
     *
     * @param bo 产品移库批次明细
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TransferItemBatchBo bo) {
        try {
            //填充冗余信息
            fillRedundantFields(bo);
            TransferItemBatch add = MapstructUtils.convert(bo, TransferItemBatch.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增产品移库批次明细失败");
            }

            bo.setBatchId(add.getBatchId());
            log.info("新增产品移库批次明细成功：批次号【{}】", add.getInternalBatchNumber());
            return true;
        } catch (Exception e) {
            log.error("新增产品移库批次明细失败：{}", e.getMessage(), e);
            throw new ServiceException("新增产品移库批次明细失败：" + e.getMessage());
        }
    }

    /**
     * 修改产品移库批次明细
     *
     * @param bo 产品移库批次明细
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(TransferItemBatchBo bo) {
        try {
            //填充冗余信息
            fillRedundantFields(bo);
            TransferItemBatch update = MapstructUtils.convert(bo, TransferItemBatch.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改产品移库批次明细失败：明细不存在或数据未变更");
            }

            log.info("修改产品移库批次明细成功：批次号【{}】", update.getInternalBatchNumber());
            return true;
        } catch (Exception e) {
            log.error("修改产品移库批次明细失败：{}", e.getMessage(), e);
            throw new ServiceException("修改产品移库批次明细失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TransferItemBatch entity) {
        // 校验必填字段
        if (entity.getItemId() == null) {
            throw new ServiceException("移库明细不能为空");
        }
        if (entity.getInventoryId() == null) {
            throw new ServiceException("库存不能为空");
        }
        if (entity.getQuantity() == null || entity.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException("移库数量必须大于0");
        }
    }

    /**
     * 校验并批量删除产品移库批次明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验批次明细是否可以删除
            List<TransferItemBatch> batches = baseMapper.selectByIds(ids);
            for (TransferItemBatch batch : batches) {
                log.info("删除移库批次明细，批次号：{}", batch.getInternalBatchNumber());
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 批量插入产品移库批次明细
     *
     * @param batches 批次
     * @return 是否插入成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertBatch(Collection<TransferItemBatchBo> batches) {
        List<TransferItemBatch> entities = batches.stream()
            .map(bo -> MapstructUtils.convert(bo, TransferItemBatch.class))
            .collect(Collectors.toList());
        return baseMapper.insertBatch(entities);
    }

    /**
     * 根据明细ID查询
     *
     * @param itemId 明细ID
     * @return 库存记录
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TransferItemBatchVo> queryByItemId(Long itemId) {
        LambdaQueryWrapper<TransferItemBatch> lqw = Wrappers.lambdaQuery();
        lqw.eq(TransferItemBatch::getItemId, itemId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(TransferItemBatchBo bo) {
        // 填充位置信息
        // 填充位置信息
        if (bo.getFromLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getFromLocationId());
            if (vo != null) {
                bo.setFromLocationCode(vo.getLocationCode());
                bo.setFromLocationName(vo.getLocationName());
            }
        }
        if (bo.getToLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getToLocationId());
            if (vo != null) {
                bo.setToLocationCode(vo.getLocationCode());
                bo.setToLocationName(vo.getLocationName());
            }
        }
    }
}
