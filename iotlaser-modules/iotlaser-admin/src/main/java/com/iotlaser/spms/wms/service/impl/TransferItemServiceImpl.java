package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.domain.vo.LocationVo;
import com.iotlaser.spms.base.service.ILocationService;
import com.iotlaser.spms.wms.domain.TransferItem;
import com.iotlaser.spms.wms.domain.bo.TransferItemBo;
import com.iotlaser.spms.wms.domain.vo.TransferItemVo;
import com.iotlaser.spms.wms.domain.vo.TransferVo;
import com.iotlaser.spms.wms.enums.TransferStatus;
import com.iotlaser.spms.wms.mapper.TransferItemMapper;
import com.iotlaser.spms.wms.service.ITransferItemService;
import com.iotlaser.spms.wms.service.ITransferService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 产品移库明细Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TransferItemServiceImpl implements ITransferItemService {

    private final TransferItemMapper baseMapper;
    private final ILocationService locationService;
    @Lazy
    @Autowired
    private ITransferService transferService;

    /**
     * 查询产品移库明细
     *
     * @param itemId 主键
     * @return 产品移库明细
     */
    @Override
    public TransferItemVo queryById(Long itemId) {
        return baseMapper.selectVoById(itemId);
    }

    /**
     * 分页查询产品移库明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品移库明细分页列表
     */
    @Override
    public TableDataInfo<TransferItemVo> queryPageList(TransferItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TransferItem> lqw = buildQueryWrapper(bo);
        Page<TransferItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品移库明细列表
     *
     * @param bo 查询条件
     * @return 产品移库明细列表
     */
    @Override
    public List<TransferItemVo> queryList(TransferItemBo bo) {
        LambdaQueryWrapper<TransferItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TransferItem> buildQueryWrapper(TransferItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TransferItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(TransferItem::getItemId);
        lqw.eq(bo.getTransferId() != null, TransferItem::getTransferId, bo.getTransferId());
        lqw.eq(bo.getProductId() != null, TransferItem::getProductId, bo.getProductId());
        lqw.eq(StringUtils.isNotBlank(bo.getProductCode()), TransferItem::getProductCode, bo.getProductCode());
        lqw.like(StringUtils.isNotBlank(bo.getProductName()), TransferItem::getProductName, bo.getProductName());
        lqw.eq(bo.getFromLocationId() != null, TransferItem::getFromLocationId, bo.getFromLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getFromLocationCode()), TransferItem::getFromLocationCode, bo.getFromLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getFromLocationName()), TransferItem::getFromLocationName, bo.getFromLocationName());
        lqw.eq(bo.getToLocationId() != null, TransferItem::getToLocationId, bo.getToLocationId());
        lqw.eq(StringUtils.isNotBlank(bo.getToLocationCode()), TransferItem::getToLocationCode, bo.getToLocationCode());
        lqw.like(StringUtils.isNotBlank(bo.getToLocationName()), TransferItem::getToLocationName, bo.getToLocationName());

        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TransferItem::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增产品移库明细
     *
     * @param bo 产品移库明细
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TransferItemBo bo) {
        try {
            //填充冗余信息
            fillRedundantFields(bo);
            TransferItem add = MapstructUtils.convert(bo, TransferItem.class);
            validEntityBeforeSave(add);

            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增产品移库明细失败");
            }

            bo.setItemId(add.getItemId());
            log.info("新增产品移库明细成功：产品【{}】", add.getProductName());
            return true;
        } catch (Exception e) {
            log.error("新增产品移库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("新增产品移库明细失败：" + e.getMessage());
        }
    }

    /**
     * 修改产品移库明细
     *
     * @param bo 产品移库明细
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(TransferItemBo bo) {
        try {
            //填充冗余信息
            fillRedundantFields(bo);
            TransferItem update = MapstructUtils.convert(bo, TransferItem.class);
            validEntityBeforeSave(update);

            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改产品移库明细失败：明细不存在或数据未变更");
            }

            log.info("修改产品移库明细成功：产品【{}】", update.getProductName());
            return true;
        } catch (Exception e) {
            log.error("修改产品移库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("修改产品移库明细失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     * <p>
     * 注意：字段非空校验、数据类型校验、格式校验等基础校验已移至Bo类的注解实现
     * 当前方法只负责核心业务逻辑校验：
     * 源库位和目标库位不能相同
     * 同一移库单中产品不能重复
     *
     * @param entity 移库明细实体
     */
    private void validEntityBeforeSave(TransferItem entity) {
        // 校验源库位和目标库位不能相同
        if (entity.getFromLocationId() != null && entity.getToLocationId() != null
            && entity.getFromLocationId().equals(entity.getToLocationId())) {
            throw new ServiceException("源库位和目标库位不能相同");
        }

        // 校验同一移库单中产品不能重复
        if (entity.getTransferId() != null && entity.getProductId() != null) {
            LambdaQueryWrapper<TransferItem> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(TransferItem::getTransferId, entity.getTransferId());
            wrapper.eq(TransferItem::getProductId, entity.getProductId());
            if (entity.getItemId() != null) {
                wrapper.ne(TransferItem::getItemId, entity.getItemId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("同一移库单中不能重复添加相同产品");
            }
        }
    }

    /**
     * 校验并批量删除产品移库明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验明细是否可以删除
            List<TransferItem> items = baseMapper.selectByIds(ids);
            for (TransferItem item : items) {
                // 检查主表状态，只有草稿状态的移库明细才能删除
                TransferVo transfer = transferService.queryById(item.getTransferId());
                if (transfer != null && transfer.getTransferStatus() != TransferStatus.DRAFT) {
                    throw new ServiceException("移库明细所属移库单【" + transfer.getTransferName() +
                        "】状态为【" + transfer.getTransferStatus() + "】，不允许删除明细");
                }

                // 级联删除移库明细批次
                // TODO: 添加existsByItemId和getBatchIdsByItemId方法
                // if (batchService.existsByItemId(item.getItemId())) {
                //     List<Long> batchIds = batchService.getBatchIdsByItemId(item.getItemId());
                //     if (!batchIds.isEmpty()) {
                //         batchService.deleteWithValidByIds(batchIds, false);
                //         log.info("级联删除移库明细批次，明细：{}，批次数量：{}", item.getProductName(), batchIds.size());
                //     }
                // }

                log.info("删除移库明细校验通过，产品：{}", item.getProductName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除移库明细成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除移库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("删除移库明细失败：" + e.getMessage());
        }
    }

    /**
     * 批量插入或更新移库明细
     *
     * @param items 明细BO集合
     * @return 是否操作成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertOrUpdateBatch(List<TransferItemBo> items) {
        if (items == null || items.isEmpty()) {
            return true;
        }

        try {
            List<TransferItem> entities = items.stream()
                .map(bo -> MapstructUtils.convert(bo, TransferItem.class))
                .collect(Collectors.toList());

            // 验证每个实体
            entities.forEach(this::validEntityBeforeSave);

            // 批量插入或更新
            boolean result = baseMapper.insertOrUpdateBatch(entities);
            if (result) {
                log.info("批量插入或更新销售出库明细成功，数量：{}", entities.size());
            }
            return result;
        } catch (Exception e) {
            log.error("批量插入或更新销售出库明细失败：{}", e.getMessage(), e);
            throw new ServiceException("批量操作失败：" + e.getMessage());
        }
    }

    /**
     * 填充冗余字段
     */
    private void fillRedundantFields(TransferItemBo bo) {
        // 填充位置信息
        if (bo.getFromLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getFromLocationId());
            if (vo != null) {
                bo.setFromLocationCode(vo.getLocationCode());
                bo.setFromLocationName(vo.getLocationName());
            }
        }
        if (bo.getToLocationId() != null) {
            LocationVo vo = locationService.queryById(bo.getToLocationId());
            if (vo != null) {
                bo.setToLocationCode(vo.getLocationCode());
                bo.setToLocationName(vo.getLocationName());
            }
        }
    }


    /**
     * 查询产品移库明细表及其关联信息
     *
     * @param itemId 主键
     * @return 产品移库明细表
     */
    @Override
    public TransferItemVo queryByIdWith(Long itemId) {
        return MapstructUtils.convert(baseMapper.queryByIdWith(itemId), TransferItemVo.class);
    }

    /**
     * 分页查询产品移库明细表列表及其关联信息
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品移库明细表分页列表
     */
    @Override
    public TableDataInfo<TransferItemVo> queryPageListWith(TransferItemBo bo, PageQuery pageQuery) {
        QueryWrapper<TransferItem> queryWrapper = buildQueryWrapperWith(bo);
        List<TransferItemVo> result = MapstructUtils.convert(baseMapper.queryPageListWith(pageQuery.build(), queryWrapper), TransferItemVo.class);
        return TableDataInfo.build(result);
    }

    /**
     * 查询产品移库明细表列表及其关联信息
     *
     * @param bo 查询条件
     * @return 产品移库明细
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<TransferItemVo> queryListWith(TransferItemBo bo) {
        QueryWrapper<TransferItem> queryWrapper = buildQueryWrapperWith(bo);
        return MapstructUtils.convert(baseMapper.queryPageListWith(null, queryWrapper), TransferItemVo.class);
    }

    private QueryWrapper<TransferItem> buildQueryWrapperWith(TransferItemBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<TransferItem> wrapper = Wrappers.query();
        wrapper.eq("item.del_flag", SystemConstants.NORMAL);
        wrapper.orderByAsc("item.item_id");
        wrapper.eq(bo.getTransferId() != null, "item.transfer_id", bo.getTransferId());
        wrapper.eq(bo.getInventoryId() != null, "item.inventory_id", bo.getInventoryId());
        wrapper.eq(bo.getProductId() != null, "item.product_id", bo.getProductId());
        wrapper.eq(StringUtils.isNotBlank(bo.getProductCode()), "item.product_code", bo.getProductCode());
        wrapper.like(StringUtils.isNotBlank(bo.getProductName()), "item.product_name", bo.getProductName());
        wrapper.eq(bo.getUnitId() != null, "item.unit_id", bo.getUnitId());
        wrapper.eq(StringUtils.isNotBlank(bo.getUnitCode()), "item.unit_code", bo.getUnitCode());
        wrapper.like(StringUtils.isNotBlank(bo.getUnitName()), "item.unit_name", bo.getUnitName());
        wrapper.eq(bo.getFromLocationId() != null, "item.from_location_id", bo.getFromLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getFromLocationCode()), "item.from_location_code", bo.getFromLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getFromLocationName()), "item.from_location_name", bo.getFromLocationName());
        wrapper.eq(bo.getToLocationId() != null, "item.to_location_id", bo.getToLocationId());
        wrapper.eq(StringUtils.isNotBlank(bo.getToLocationCode()), "item.to_location_code", bo.getToLocationCode());
        wrapper.like(StringUtils.isNotBlank(bo.getToLocationName()), "item.to_location_name", bo.getToLocationName());
        wrapper.eq(bo.getQuantity() != null, "item.quantity", bo.getQuantity());
        wrapper.eq(bo.getFinishQuantity() != null, "item.finish_quantity", bo.getFinishQuantity());
        wrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "item.status", bo.getStatus());
        return wrapper;
    }

}
