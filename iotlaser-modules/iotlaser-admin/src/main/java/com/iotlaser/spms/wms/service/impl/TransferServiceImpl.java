package com.iotlaser.spms.wms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.base.strategy.Gen;
import com.iotlaser.spms.wms.domain.Transfer;
import com.iotlaser.spms.wms.domain.bo.TransferBo;
import com.iotlaser.spms.wms.domain.bo.TransferItemBo;
import com.iotlaser.spms.wms.domain.vo.TransferItemVo;
import com.iotlaser.spms.wms.domain.vo.TransferVo;
import com.iotlaser.spms.wms.enums.TransferStatus;
import com.iotlaser.spms.wms.mapper.TransferMapper;
import com.iotlaser.spms.wms.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.UserService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.iotlaser.spms.base.enums.GenCodeType.WMS_TRANSFER_CODE;

/**
 * 产品移库Service业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/04/23
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TransferServiceImpl implements ITransferService {

    private final TransferMapper baseMapper;
    private final ITransferItemService itemService;
    private final IInboundService inboundService;
    private final IOutboundService outboundService;
    private final Gen gen;

    // 添加缺失的依赖注入
    @Autowired
    private IInventoryService inventoryService;
    @Autowired
    private UserService userService;

    /**
     * 查询产品移库
     *
     * @param transferId 主键
     * @return 产品移库
     */
    @Override
    public TransferVo queryById(Long transferId) {
        return baseMapper.selectVoById(transferId);
    }

    /**
     * 分页查询产品移库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 产品移库分页列表
     */
    @Override
    public TableDataInfo<TransferVo> queryPageList(TransferBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Transfer> lqw = buildQueryWrapper(bo);
        Page<TransferVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的产品移库列表
     *
     * @param bo 查询条件
     * @return 产品移库列表
     */
    @Override
    public List<TransferVo> queryList(TransferBo bo) {
        LambdaQueryWrapper<Transfer> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Transfer> buildQueryWrapper(TransferBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Transfer> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(Transfer::getTransferId);
        lqw.eq(StringUtils.isNotBlank(bo.getTransferCode()), Transfer::getTransferCode, bo.getTransferCode());
        lqw.like(StringUtils.isNotBlank(bo.getTransferName()), Transfer::getTransferName, bo.getTransferName());
        lqw.eq(StringUtils.isNotBlank(bo.getTransferType()), Transfer::getTransferType, bo.getTransferType());
        lqw.eq(bo.getTransferStatus() != null, Transfer::getTransferStatus, bo.getTransferStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Transfer::getStatus, bo.getStatus());
        lqw.between(params.get("beginTransferTime") != null && params.get("endTransferTime") != null,
            Transfer::getTransferTime, params.get("beginTransferTime"), params.get("endTransferTime"));
        return lqw;
    }

    /**
     * 新增产品移库
     *
     * @param bo 产品移库
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferVo insertByBo(TransferBo bo) {
        try {
            // 如果移库单号为空，则生成新的移库单号
            if (StringUtils.isEmpty(bo.getTransferCode())) {
                bo.setTransferCode(gen.code(WMS_TRANSFER_CODE));
            }
            // 设置移库状态为草稿
            if (bo.getTransferStatus() == null) {
                bo.setTransferStatus(TransferStatus.DRAFT);
            }
            if (bo.getTransferTime() == null) {
                bo.setTransferTime(LocalDateTime.now());
            }
            // 将BO对象转换为Transfer实体对象
            Transfer add = MapstructUtils.convert(bo, Transfer.class);
            // 保存前验证实体
            validEntityBeforeSave(add);

            // 插入Transfer实体，判断插入是否成功
            int result = baseMapper.insert(add);
            if (result <= 0) {
                throw new ServiceException("新增产品移库失败");
            }

            bo.setTransferId(add.getTransferId());
            log.info("新增产品移库成功：{}", add.getTransferCode());
            return MapstructUtils.convert(add, TransferVo.class);
        } catch (Exception e) {
            log.error("新增产品移库失败：{}", e.getMessage(), e);
            throw new ServiceException("新增产品移库失败：" + e.getMessage());
        }
    }

    /**
     * 修改产品移库
     *
     * @param bo 产品移库
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferVo updateByBo(TransferBo bo) {
        try {
            // 将传入的TransferBo转换为Transfer对象
            Transfer update = MapstructUtils.convert(bo, Transfer.class);
            // 在保存前验证实体的合法性
            validEntityBeforeSave(update);

            // 更新实体，判断更新是否成功
            int result = baseMapper.updateById(update);
            if (result <= 0) {
                throw new ServiceException("修改产品移库失败：移库单不存在或数据未变更");
            }

            // 根据移库状态处理出入库逻辑
            processTransferStatusChange(update);
            log.info("修改产品移库成功：{}", update.getTransferCode());
            return MapstructUtils.convert(update, TransferVo.class);
        } catch (Exception e) {
            log.error("修改产品移库失败：{}", e.getMessage(), e);
            throw new ServiceException("修改产品移库失败：" + e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     * <p>
     * 注意：字段非空校验、数据类型校验、格式校验等基础校验已移至Bo类的注解实现
     * 当前方法只负责核心业务逻辑校验：
     * 移库单编码唯一性检查
     * 批次产品的批次明细完整性校验
     *
     * @param entity 移库单实体
     */
    private void validEntityBeforeSave(Transfer entity) {
        // 校验移库单编码唯一性
        if (StringUtils.isNotBlank(entity.getTransferCode())) {
            LambdaQueryWrapper<Transfer> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(Transfer::getTransferCode, entity.getTransferCode());
            if (entity.getTransferId() != null) {
                wrapper.ne(Transfer::getTransferId, entity.getTransferId());
            }
            if (baseMapper.exists(wrapper)) {
                throw new ServiceException("移库单编码已存在：" + entity.getTransferCode());
            }
        }
    }

    /**
     * 校验并批量删除产品移库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 校验移库单是否可以删除
            List<Transfer> transfers = baseMapper.selectByIds(ids);
            for (Transfer transfer : transfers) {
                // 检查移库单状态，只有草稿状态的移库单才能删除
                if (transfer.getTransferStatus() != TransferStatus.DRAFT) {
                    throw new ServiceException("移库单【" + transfer.getTransferName() + "】状态为【" +
                        transfer.getTransferStatus() + "】，不允许删除");
                }

                // 检查是否有关联的出库单和入库单
                checkTransferRelatedDocuments(transfer.getTransferId());

                // 级联删除移库明细
                TransferItemBo queryBo = new TransferItemBo();
                queryBo.setTransferId(transfer.getTransferId());
                List<TransferItemVo> items = itemService.queryList(queryBo);
                if (!items.isEmpty()) {
                    List<Long> itemIds = items.stream()
                        .map(TransferItemVo::getItemId)
                        .collect(Collectors.toList());
                    itemService.deleteWithValidByIds(itemIds, false);
                    log.info("级联删除移库明细，移库单：{}，明细数量：{}", transfer.getTransferName(), itemIds.size());
                }

                log.info("删除移库单校验通过：{}", transfer.getTransferName());
            }
        }

        try {
            int result = baseMapper.deleteByIds(ids);
            if (result > 0) {
                log.info("批量删除移库单成功，删除数量：{}", result);
            }
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除移库单失败：{}", e.getMessage(), e);
            throw new ServiceException("删除移库单失败：" + e.getMessage());
        }
    }

    /**
     * TODO 需完善 更新产品移库为已完成
     *
     * @param transferId 移库ID
     */
    @Override
    public void finish(Long transferId) {
        /*Transfer update = baseMapper.selectById(transferId);
        OutboundBo outboundBo = new OutboundBo();
        outboundBo.setSourceId(transferId);
        Optional<OutboundVo> outboundVo = outboundService.queryList(outboundBo).stream().findFirst();
        if (outboundVo.isEmpty() || !outboundVo.get().getOutboundStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("请先完成出库单");
        }

        InboundBo bo = new InboundBo();
        bo.setDirectSourceId(transferId);
        Optional<InboundVo> inboundVo = inboundService.queryList(bo).stream().findFirst();
        if (inboundVo.isEmpty() || !inboundVo.get().getInboundStatus().equals(BusinessStatusEnum.FINISH.getStatus())) {
            throw new ServiceException("请先完成入库单");
        }

        update.setTransferStatus(TransferStatus.COMPLETED);
        baseMapper.updateById(update);*/
    }

    /**
     * 处理移库状态变更逻辑
     *
     * @param transfer 移库单
     */
    private void processTransferStatusChange(Transfer transfer) {
        try {
            // 集成库存管理模块，实现移库状态变更的自动化处理
            TransferStatus transferStatus = transfer.getTransferStatus();

            // 状态过渡处理：DRAFT -> SUBMITTED -> APPROVED -> CONFIRMED -> COMPLETED
            switch (transferStatus) {
                case CONFIRMED:
                    // 确认状态：从源库位扣减库存
                    processTransferOut(transfer);
                    break;
                case COMPLETED:
                    // 完成状态：向目标库位增加库存，记录日志
                    processTransferIn(transfer);
                    recordTransferLog(transfer);
                    break;
                default:
                    log.debug("移库单状态【{}】无需特殊处理", transferStatus);
                    break;
            }

            log.info("处理移库单【{}】状态变更：{}", transfer.getTransferCode(), transfer.getTransferStatus());
        } catch (Exception e) {
            log.error("处理移库状态变更失败：{}", e.getMessage(), e);
            throw new ServiceException("处理移库状态变更失败：" + e.getMessage());
        }
    }

    /**
     * 处理移库出库（基于移库明细从源库位扣减）
     */
    private void processTransferOut(Transfer transfer) {
        try {
            // 基于移库明细进行库存扣减（遵循移库单→明细→批次的标准结构）
            TransferItemBo queryBo = new TransferItemBo();
            queryBo.setTransferId(transfer.getTransferId());
            List<TransferItemVo> items = itemService.queryList(queryBo);

            for (TransferItemVo item : items) {
                // 从源库位扣减库存（基于明细）
                Boolean result = inventoryService.adjustBatch(
                    item.getProductId(),
                    item.getFromLocationId(),
                    item.getQuantity().negate(), // 负数表示扣减
                    "移库出库：" + transfer.getTransferCode() + " 明细：" + item.getItemId(),
                    transfer.getCreateBy(),
                    transfer.getCreateBy() != null ? userService.selectNicknameById(transfer.getCreateBy()) : null
                );

                if (!result) {
                    throw new ServiceException("移库出库失败：明细【" + item.getItemId() + "】库存扣减失败");
                }

                log.info("移库出库成功 - 移库单: {}, 明细: {}, 产品: {}, 源库位: {}, 数量: {}",
                    transfer.getTransferCode(), item.getItemId(), item.getProductId(),
                    item.getFromLocationId(), item.getQuantity());
            }
        } catch (Exception e) {
            log.error("移库出库失败 - 移库单: {}, 错误: {}", transfer.getTransferCode(), e.getMessage(), e);
            throw new ServiceException("移库出库失败：" + e.getMessage());
        }
    }

    /**
     * 处理移库入库（基于移库明细向目标库位增加）
     */
    private void processTransferIn(Transfer transfer) {
        try {
            // 基于移库明细进行库存增加（遵循移库单→明细→批次的标准结构）
            TransferItemBo queryBo = new TransferItemBo();
            queryBo.setTransferId(transfer.getTransferId());
            List<TransferItemVo> items = itemService.queryList(queryBo);

            for (TransferItemVo item : items) {
                // 向目标库位增加库存（基于明细）
                Boolean result = inventoryService.adjustBatch(
                    item.getProductId(),
                    item.getToLocationId(),
                    item.getQuantity(), // 正数表示增加
                    "移库入库：" + transfer.getTransferCode() + " 明细：" + item.getItemId(),
                    transfer.getUpdateBy(),
                    transfer.getUpdateBy() != null ? userService.selectNicknameById(transfer.getUpdateBy()) : null
                );

                if (!result) {
                    throw new ServiceException("移库入库失败：明细【" + item.getItemId() + "】库存增加失败");
                }

                log.info("移库入库成功 - 移库单: {}, 明细: {}, 产品: {}, 目标库位: {}, 数量: {}",
                    transfer.getTransferCode(), item.getItemId(), item.getProductId(),
                    item.getToLocationId(), item.getQuantity());
            }
        } catch (Exception e) {
            log.error("移库入库失败 - 移库单: {}, 错误: {}", transfer.getTransferCode(), e.getMessage(), e);
            throw new ServiceException("移库入库失败：" + e.getMessage());
        }
    }

    /**
     * 记录移库日志
     */
    private void recordTransferLog(Transfer transfer) {
        try {
            // TODO: 记录出库日志（Transfer实体中没有productId和fromLocationId字段）
            // InventoryLogBo outLog = new InventoryLogBo();
            // outLog.setProductId(transfer.getProductId());
            // outLog.setLocationId(transfer.getFromLocationId());
            // outLog.setOperationType("TRANSFER_OUT");
            // outLog.setQuantity(transfer.getQuantity().negate());
            // outLog.setSourceType("TRANSFER");
            // outLog.setSourceId(transfer.getTransferId());
            // outLog.setSourceCode(transfer.getTransferCode());
            // outLog.setRemark("移库出库：" + transfer.getTransferCode());
            // TODO: 记录库存日志
            // inventoryLogService.insertByBo(outLog);

            // 记录入库日志
            // InventoryLogBo inLog = new InventoryLogBo();
            // inLog.setProductId(transfer.getProductId());
            // inLog.setLocationId(transfer.getToLocationId());
            // inLog.setOperationType("TRANSFER_IN");
            // inLog.setQuantity(transfer.getQuantity());
            // inLog.setSourceType("TRANSFER");
            // inLog.setSourceId(transfer.getTransferId());
            // inLog.setSourceCode(transfer.getTransferCode());
            // inLog.setRemark("移库入库：" + transfer.getTransferCode());
            // inventoryLogService.insertByBo(inLog);

            log.info("移库日志记录成功 - 移库单: {}", transfer.getTransferCode());
        } catch (Exception e) {
            log.error("记录移库日志失败 - 移库单: {}, 错误: {}", transfer.getTransferCode(), e.getMessage(), e);
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 需完善 检查移库单关联的出库单和入库单
     *
     * @param transferId 移库单ID
     */
    private void checkTransferRelatedDocuments(Long transferId) {
        /*try {
            // 检查是否有关联的出库单
            OutboundBo outboundQuery = new OutboundBo();
            outboundQuery.setSourceId(transferId);
            outboundQuery.setSourceType(DirectSourceType.TRANSFER_OUTBOUND);
            List<OutboundVo> outbounds = outboundService.queryList(outboundQuery);
            if (!outbounds.isEmpty()) {
                throw new ServiceException("该移库单已生成出库单，不能删除");
            }

            // 检查是否有关联的入库单
            InboundBo inboundQuery = new InboundBo();
            inboundQuery.setDirectSourceId(transferId);
            inboundQuery.setSourceType(DirectSourceType.TRANSFER_INBOUND);
            List<InboundVo> inbounds = inboundService.queryList(inboundQuery);
            if (!inbounds.isEmpty()) {
                throw new ServiceException("该移库单已生成入库单，不能删除");
            }

            log.debug("移库单【{}】关联文档检查通过", transferId);
        } catch (Exception e) {
            if (e instanceof ServiceException) {
                throw e;
            }
            // 如果出入库服务不可用，记录警告但不阻止删除
            log.warn("检查移库单【{}】关联文档时出现异常：{}", transferId, e.getMessage());
        }*/
    }
}
