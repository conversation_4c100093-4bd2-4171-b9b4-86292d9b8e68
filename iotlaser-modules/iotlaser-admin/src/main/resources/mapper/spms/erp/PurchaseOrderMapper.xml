<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iotlaser.spms.erp.mapper.PurchaseOrderMapper">

    <!-- 查询采购订单及其明细信息 -->
    <select id="selectOrderWithItems" resultType="com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo">
        SELECT
        po.order_id,
        po.order_code,
        po.order_name,
        po.source_document_id,
        po.source_document_code,
        po.source_document_name,
        po.source_document_type,
        po.supplier_id,
        po.supplier_code,
        po.supplier_name,
        po.order_time,
        po.order_status,
        po.remark,
        po.status,
        po.create_time,
        po.update_time,
        COUNT(poi.item_id) as item_count,
        SUM(poi.quantity * poi.price) as total_amount,
        SUM(COALESCE(poi.finish_quantity, 0) * poi.price) as received_amount
        FROM erp_purchase_order po
        LEFT JOIN erp_purchase_order_item poi ON po.order_id = poi.order_id AND poi.del_flag = '0'
        WHERE po.del_flag = '0'
        <if test="orderId != null">
            AND po.order_id = #{orderId}
        </if>
        GROUP BY po.order_id
        ${ew.getCustomSqlSegment}
    </select>

    <!-- 查询待收货的采购订单 -->
    <select id="selectPendingReceiveOrders" resultType="com.iotlaser.spms.erp.domain.vo.PurchaseOrderVo">
        SELECT
        po.order_id,
        po.order_code,
        po.order_name,
        po.supplier_code,
        po.supplier_name,
        po.order_time,
        po.order_status,
        COUNT(poi.item_id) as item_count,
        SUM(poi.quantity - COALESCE(poi.finish_quantity, 0)) as pending_quantity
        FROM erp_purchase_order po
        INNER JOIN erp_purchase_order_item poi ON po.order_id = poi.order_id AND poi.del_flag = '0'
        WHERE po.del_flag = '0'
        AND po.order_status IN ('confirmed', 'partially_received')
        AND poi.quantity > COALESCE(poi.finish_quantity, 0)
        GROUP BY po.order_id
        HAVING pending_quantity > 0
        ORDER BY po.order_time ASC
        ${ew.getCustomSqlSegment}
    </select>

    <!-- 查询供应商的采购统计 -->
    <select id="selectSupplierPurchaseStats" resultType="java.util.Map">
        SELECT
        po.supplier_id,
        po.supplier_code,
        po.supplier_name,
        COUNT(po.order_id) as order_count,
        SUM(poi.quantity * poi.price) as total_amount,
        SUM(CASE WHEN po.order_status = 'closed' THEN poi.quantity * poi.price ELSE 0 END) as completed_amount
        FROM erp_purchase_order po
        INNER JOIN erp_purchase_order_item poi ON po.order_id = poi.order_id AND poi.del_flag = '0'
        WHERE po.del_flag = '0'
        <if test="startTime != null">
            AND po.order_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND po.order_time &lt;= #{endTime}
        </if>
        <if test="supplierId != null">
            AND po.supplier_id = #{supplierId}
        </if>
        GROUP BY po.supplier_id, po.supplier_code, po.supplier_name
        ORDER BY total_amount DESC
        ${ew.getCustomSqlSegment}
    </select>

</mapper>
