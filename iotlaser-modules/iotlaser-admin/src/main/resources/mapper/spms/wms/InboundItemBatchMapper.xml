<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iotlaser.spms.wms.mapper.InboundItemBatchMapper">
    <select id="selectQuantitySumByItemId" resultType="java.math.BigDecimal">
        select COALESCE(sum(quantity), 0) from erp_purchase_inbound_item_batch where item_id = #{itemId} and del_flag = '0'
        <if test="batchId != null">
            and batch_id != #{batchId}
        </if>
    </select>
</mapper>
