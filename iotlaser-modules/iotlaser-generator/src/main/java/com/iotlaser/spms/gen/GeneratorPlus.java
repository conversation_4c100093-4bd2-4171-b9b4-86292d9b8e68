package com.iotlaser.spms.gen;

import com.iotlaser.spms.gen.domain.GenTablePlusExt;
import com.iotlaser.spms.gen.domain.GenTablePlusOptions;
import com.iotlaser.spms.gen.enums.RelationshipType;
import com.iotlaser.spms.gen.utils.RemoveUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.dromara.common.core.utils.file.FileUtils;
import org.dromara.generator.domain.GenTableColumn;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 代码生成器
 *
 * <AUTHOR>
 * @date 2025/5/16
 */
@Slf4j
public class GeneratorPlus {
    private VelocityEngine velocityEngine;
    private GenTablePlusOptions config;

    public GeneratorPlus(GenTablePlusOptions config) {
        this.config = config;
        // 初始化Velocity引擎
        velocityEngine = new VelocityEngine();
        velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
        velocityEngine.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
        velocityEngine.init();
    }

    public static void main(String[] args) {
        GenTablePlusOptions config = new GenTablePlusOptions();

        GenTablePlusExt main = new GenTablePlusExt();
        main.setPackageName("com.iotlaser.spms.erp");
        main.setGenPath("E:\\src");
        main.setModuleName("spms/erp");
        main.setFunctionAuthor("Li Kai");
        main.setOverwrite(true);
        main.setTableName("erp_purchase_order_item");
        main.setClassName("PurchaseOrderItem");
        main.setAttrName("item");
        main.setTableComment("采购订单明细表");
        main.setModuleName("spms/erp");
        main.setBusinessName("purchaseOrderItem");
        main.setPkColumn(newCol("item_id", "Long", "itemId"));

        List<GenTableColumn> columns = new ArrayList<>();
        columns.add(newCol("item_id", "Long", "itemId"));
        columns.add(newCol("product_id", "Long", "productId"));
        columns.add(newCol("product_code", "String", "productCode"));
        columns.add(newCol("product_name", "String", "productName"));
        main.setColumns(columns);

        config.setMain(main);

        List<GenTablePlusExt> subs = new ArrayList<>();

        GenTablePlusExt sub1 = new GenTablePlusExt();
        sub1.setType(RelationshipType.ONE_TO_ONE);
        sub1.setPackageName("com.iotlaser.spms.pro");
        main.setModuleName("spms/erp");
        sub1.setFunctionAuthor("Li Kai");
        sub1.setOverwrite(true);
        sub1.setTableName("pro_product");
        sub1.setClassName("Product");
        sub1.setAttrName("product");
        sub1.setTableComment("产品信息表");
        sub1.setMainJoin("product_id");
        sub1.setSubJoin("product_id");
        sub1.setPkColumn(newCol("product_id", "Long", "productId"));

        List<GenTableColumn> col1 = new ArrayList<>();
        col1.add(newCol("product_id", "Long", "productId"));
        col1.add(newCol("product_code", "String", "productCode"));
        col1.add(newCol("product_name", "String", "productName"));
        sub1.setColumns(col1);

        subs.add(sub1);

        GenTablePlusExt sub2 = new GenTablePlusExt();
        sub2.setType(RelationshipType.ONE_TO_MANY);
        sub2.setPackageName("com.iotlaser.spms.erp");
        main.setModuleName("spms/erp");
        sub2.setFunctionAuthor("Li Kai");
        sub2.setOverwrite(true);
        sub2.setTableName("erp_purchase_order_item_batch");
        sub2.setClassName("PurchaseOrderItemBatch");
        sub2.setAttrName("batches");
        sub2.setTableComment("采购订单批次表");
        sub2.setMainJoin("item_id");
        sub2.setSubJoin("item_id");
        sub2.setPkColumn(newCol("batch_id", "Long", "batchId"));

        List<GenTableColumn> col2 = new ArrayList<>();
        col2.add(newCol("batch_id", "Long", "batchId"));
        col2.add(newCol("batch_code", "String", "batchCode"));
        col2.add(newCol("item_id", "Long", "itemId"));
        col2.add(newCol("product_id", "Long", "productId"));
        col2.add(newCol("product_code", "String", "productCode"));
        col2.add(newCol("product_name", "String", "productName"));
        col2.add(newCol("quantity", "Integer", "quantity"));
        sub2.setColumns(col2);

        subs.add(sub2);

        config.setSubs(subs);

        // --- 2. 执行生成 ---
        GeneratorPlus generator = new GeneratorPlus(config);
        generator.generate();
        System.out.println("Code generation finished successfully!");
    }

    public void generate() {
        VelocityContext context = new VelocityContext();
        context.put("config", config); // 全局配置，包含主表和关系
        context.put("main", config.getMain());      // 当前正在处理的表 (可能是主表，也可能是子表)
        context.put("subs", config.getSubs());      // 当前正在处理的表 (可能是主表，也可能是子表)
        context.put("currentDate", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        context.put("dollar", "$"); // For escaping $ in templates if needed
        context.put("sharp", "#");  // For escaping # in templates if needed

        try {
            // Domain
            generateDomain(context, "templates/domain.vm", getOutputFilePath("domain", config.getMain().getClassName() + ".java"));
            // VO
            generateVo(context, "templates/vo.vm", getOutputFilePath("domain/vo", config.getMain().getClassName() + "Vo.java"));
            // Controller
            generateController(context, "templates/controller.vm", getOutputFilePath("controller", config.getMain().getClassName() + "Controller.java"));
            // Mapper
            generateMapper(context, "templates/mapper.vm", getOutputFilePath("mapper", config.getMain().getClassName() + "Mapper.java"));
            // Mapper XML
            generateMapperXml(context, "templates/mapper_xml.vm", getOutputFilePath("mapper/" + config.getMain().getModuleName(), config.getMain().getClassName() + "Mapper.xml"));
            // Service Interface
            generateService(context, "templates/service.vm", getOutputFilePath("service", "I" + config.getMain().getClassName() + "Service.java"));
            // Service Impl
            generateServiceImpl(context, "templates/serviceImpl.vm", getOutputFilePath("service/impl", config.getMain().getClassName() + "ServiceImpl.java"));
        } catch (IOException e) {
            System.err.println("Error during code generation: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private String getOutputFilePath(String layer, String fileName) {
        try {
            Path projectRoot = Paths.get(System.getProperty("user.dir")).toRealPath();
            Path targetDirectory = projectRoot.resolve(Paths.get("iotlaser-modules", "iotlaser-admin", "src", "main"));
            config.getMain().setGenPath(targetDirectory.toString());
            String packagePath = config.getMain().getPackageName().replace(".", "/");
            if (layer.startsWith("mapper/")) {
                return Paths.get(config.getMain().getGenPath(), "resources", "mapper", config.getMain().getModuleName(), fileName).toString();
            }
            return Paths.get(config.getMain().getGenPath(), "java", packagePath, layer, fileName).toString();
        } catch (Exception e) {
            log.error("获取配置文件失败，", e);
            throw new RuntimeException("获取配置文件失败，请检查配置文件");
        }
    }

    private void generateDomain(VelocityContext context, String templateName, String outputFilePathStr) throws IOException {
        Path outputFilePath = Paths.get(outputFilePathStr);
        File outputFile = outputFilePath.toFile();
        String codes = FileUtils.readString(outputFilePathStr, StandardCharsets.UTF_8);
        if (config.getMain().isOverwrite()) {
            for (GenTablePlusExt sub : config.getSubs()) {
                codes = RemoveUtils.deleteField(codes, sub.getAttrName());
            }
        }
        Template template = velocityEngine.getTemplate(templateName, String.valueOf(StandardCharsets.UTF_8));
        StringWriter writer = new StringWriter();
        template.merge(context, writer);
        int lastBraceIndex = codes.lastIndexOf("}");
        if (lastBraceIndex > 0) {
            String beforeBrace = codes.substring(0, lastBraceIndex);
            String afterBrace = codes.substring(lastBraceIndex);
            codes = beforeBrace + writer + "\n" + afterBrace;
        } else {
            codes = codes + "\n" + writer;
        }
        try (FileWriter fileWriter = new FileWriter(outputFile)) {
            fileWriter.write(codes);
            System.out.println("Generated: " + outputFile.getAbsolutePath());
        }
    }

    private void generateVo(VelocityContext context, String templateName, String outputFilePathStr) throws IOException {
        Path outputFilePath = Paths.get(outputFilePathStr);
        File outputFile = outputFilePath.toFile();
        String codes = FileUtils.readString(outputFilePathStr, StandardCharsets.UTF_8);
        if (config.getMain().isOverwrite()) {
            for (GenTablePlusExt sub : config.getSubs()) {
                codes = RemoveUtils.deleteField(codes, sub.getAttrName());
            }
        }
        Template template = velocityEngine.getTemplate(templateName, String.valueOf(StandardCharsets.UTF_8));
        StringWriter writer = new StringWriter();
        template.merge(context, writer);
        int lastBraceIndex = codes.lastIndexOf("}");
        if (lastBraceIndex > 0) {
            String beforeBrace = codes.substring(0, lastBraceIndex);
            String afterBrace = codes.substring(lastBraceIndex);
            codes = beforeBrace + writer + "\n" + afterBrace;
        } else {
            codes = codes + "\n" + writer;
        }
        try (FileWriter fileWriter = new FileWriter(outputFile)) {
            fileWriter.write(codes);
            System.out.println("Generated: " + outputFile.getAbsolutePath());
        }
    }

    private void generateController(VelocityContext context, String templateName, String outputFilePathStr) throws IOException {
        Path outputFilePath = Paths.get(outputFilePathStr);
        File outputFile = outputFilePath.toFile();
        String codes = FileUtils.readString(outputFilePathStr, StandardCharsets.UTF_8);
        if (config.getMain().isOverwrite()) {
            codes = RemoveUtils.deleteFunction(codes, "queryByIdWith");
            codes = RemoveUtils.deleteFunction(codes, "queryPageListWith");
        }
        Template template = velocityEngine.getTemplate(templateName, String.valueOf(StandardCharsets.UTF_8));
        StringWriter writer = new StringWriter();
        template.merge(context, writer);
        int lastBraceIndex = codes.lastIndexOf("}");
        if (lastBraceIndex > 0) {
            String beforeBrace = codes.substring(0, lastBraceIndex);
            String afterBrace = codes.substring(lastBraceIndex);
            codes = beforeBrace + writer + "\n" + afterBrace;
        } else {
            codes = codes + "\n" + writer;
        }
        try (FileWriter fileWriter = new FileWriter(outputFile)) {
            fileWriter.write(codes);
            System.out.println("Generated: " + outputFile.getAbsolutePath());
        }
    }

    private void generateMapper(VelocityContext context, String templateName, String outputFilePathStr) throws IOException {
        Path outputFilePath = Paths.get(outputFilePathStr);
        File outputFile = outputFilePath.toFile();
        String codes = FileUtils.readString(outputFilePathStr, StandardCharsets.UTF_8);
        if (config.getMain().isOverwrite()) {
            codes = RemoveUtils.deleteFunction(codes, "queryByIdWith");
            codes = RemoveUtils.deleteFunction(codes, "queryPageListWith");
        }
        Template template = velocityEngine.getTemplate(templateName, String.valueOf(StandardCharsets.UTF_8));
        StringWriter writer = new StringWriter();
        template.merge(context, writer);
        int lastBraceIndex = codes.lastIndexOf("}");
        if (lastBraceIndex > 0) {
            String beforeBrace = codes.substring(0, lastBraceIndex);
            String afterBrace = codes.substring(lastBraceIndex);
            codes = beforeBrace + writer + "\n" + afterBrace;
        } else {
            codes = codes + "\n" + writer;
        }
        try (FileWriter fileWriter = new FileWriter(outputFile)) {
            fileWriter.write(codes);
            System.out.println("Generated: " + outputFile.getAbsolutePath());
        }
    }

    private void generateMapperXml(VelocityContext context, String templateName, String outputFilePathStr) throws IOException {
        Path outputFilePath = Paths.get(outputFilePathStr);
        File outputFile = outputFilePath.toFile();
        String codes = FileUtils.readString(outputFilePathStr, StandardCharsets.UTF_8);
        if (config.getMain().isOverwrite()) {
            codes = RemoveUtils.deleteNodeById(codes, "BaseResultMap");
            codes = RemoveUtils.deleteNodeById(codes, "Base_Column_List");
            codes = RemoveUtils.deleteNodeById(codes, config.getMain().getClassName() + "ResultMap");
            codes = RemoveUtils.deleteNodeById(codes, "queryByIdWith");
            codes = RemoveUtils.deleteNodeById(codes, "queryPageListWith");
        }
        Template template = velocityEngine.getTemplate(templateName, String.valueOf(StandardCharsets.UTF_8));
        StringWriter writer = new StringWriter();
        template.merge(context, writer);
        int lastBraceIndex = codes.lastIndexOf("</mapper>");
        if (lastBraceIndex > 0) {
            String beforeBrace = codes.substring(0, lastBraceIndex);
            String afterBrace = codes.substring(lastBraceIndex);
            codes = beforeBrace + writer + "\n" + afterBrace;
        } else {
            codes = codes + "\n" + writer;
        }
        try (FileWriter fileWriter = new FileWriter(outputFile)) {
            fileWriter.write(codes);
            System.out.println("Generated: " + outputFile.getAbsolutePath());
        }
    }

    private void generateService(VelocityContext context, String templateName, String outputFilePathStr) throws IOException {
        Path outputFilePath = Paths.get(outputFilePathStr);
        File outputFile = outputFilePath.toFile();
        String codes = FileUtils.readString(outputFilePathStr, StandardCharsets.UTF_8);
        if (config.getMain().isOverwrite()) {
            codes = RemoveUtils.deleteFunction(codes, "queryByIdWith");
            codes = RemoveUtils.deleteFunction(codes, "queryPageListWith");
        }
        Template template = velocityEngine.getTemplate(templateName, String.valueOf(StandardCharsets.UTF_8));
        StringWriter writer = new StringWriter();
        template.merge(context, writer);
        int lastBraceIndex = codes.lastIndexOf("}");
        if (lastBraceIndex > 0) {
            String beforeBrace = codes.substring(0, lastBraceIndex);
            String afterBrace = codes.substring(lastBraceIndex);
            codes = beforeBrace + writer + "\n" + afterBrace;
        } else {
            codes = codes + "\n" + writer;
        }
        try (FileWriter fileWriter = new FileWriter(outputFile)) {
            fileWriter.write(codes);
            System.out.println("Generated: " + outputFile.getAbsolutePath());
        }
    }

    private void generateServiceImpl(VelocityContext context, String templateName, String outputFilePathStr) throws IOException {
        Path outputFilePath = Paths.get(outputFilePathStr);
        File outputFile = outputFilePath.toFile();
        String codes = FileUtils.readString(outputFilePathStr, StandardCharsets.UTF_8);
        if (config.getMain().isOverwrite()) {
            codes = RemoveUtils.deleteFunction(codes, "queryByIdWith");
            codes = RemoveUtils.deleteFunction(codes, "queryPageListWith");
            codes = RemoveUtils.deleteFunction(codes, "buildQueryWrapperWith");
        }
        Template template = velocityEngine.getTemplate(templateName, String.valueOf(StandardCharsets.UTF_8));
        StringWriter writer = new StringWriter();
        template.merge(context, writer);
        int lastBraceIndex = codes.lastIndexOf("}");
        if (lastBraceIndex > 0) {
            String beforeBrace = codes.substring(0, lastBraceIndex);
            String afterBrace = codes.substring(lastBraceIndex);
            codes = beforeBrace + writer + "\n" + afterBrace;
        } else {
            codes = codes + "\n" + writer;
        }
        try (FileWriter fileWriter = new FileWriter(outputFile)) {
            fileWriter.write(codes);
            System.out.println("Generated: " + outputFile.getAbsolutePath());
        }
    }

    public static GenTableColumn newCol(String columnName, String javaType, String javaField) {
        GenTableColumn column = new GenTableColumn();
        column.setColumnName(columnName);
        column.setJavaType(javaType);
        column.setJavaField(javaField);
        column.setIsQuery("1");
        column.setQueryType("eq");
        return column;
    }
}
