package com.iotlaser.spms.gen.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iotlaser.spms.gen.GeneratorPlus;
import com.iotlaser.spms.gen.domain.GenTablePlus;
import com.iotlaser.spms.gen.domain.GenTablePlusExt;
import com.iotlaser.spms.gen.domain.GenTablePlusOptions;
import com.iotlaser.spms.gen.domain.bo.GenTablePlusBo;
import com.iotlaser.spms.gen.domain.vo.GenTablePlusVo;
import com.iotlaser.spms.gen.mapper.GenTablePlusMapper;
import com.iotlaser.spms.gen.service.IGenTablePlusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.generator.domain.GenTable;
import org.dromara.generator.domain.GenTableColumn;
import org.dromara.generator.mapper.GenTableMapper;
import org.dromara.generator.service.IGenTableService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 代码生成PlusService业务层处理
 *
 * <AUTHOR> Kai
 * @date 2025/05/15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class GenTablePlusServiceImpl implements IGenTablePlusService {

    private final GenTablePlusMapper baseMapper;
    private final GenTableMapper genTableMapper;
    private final IGenTableService genTableService;

    /**
     * 查询代码生成Plus
     *
     * @param id 主键
     * @return 代码生成Plus
     */
    @Override
    public GenTablePlusVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询代码生成Plus列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 代码生成Plus分页列表
     */
    @Override
    public TableDataInfo<GenTablePlusVo> queryPageList(GenTablePlusBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GenTablePlus> lqw = buildQueryWrapper(bo);
        Page<GenTablePlusVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的代码生成Plus列表
     *
     * @param bo 查询条件
     * @return 代码生成Plus列表
     */
    @Override
    public List<GenTablePlusVo> queryList(GenTablePlusBo bo) {
        LambdaQueryWrapper<GenTablePlus> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GenTablePlus> buildQueryWrapper(GenTablePlusBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<GenTablePlus> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(GenTablePlus::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), GenTablePlus::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), GenTablePlus::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getOptions()), GenTablePlus::getOptions, bo.getOptions());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), GenTablePlus::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增代码生成Plus
     *
     * @param bo 代码生成Plus
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(GenTablePlusBo bo) {
        GenTablePlus add = MapstructUtils.convert(bo, GenTablePlus.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改代码生成Plus
     *
     * @param bo 代码生成Plus
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(GenTablePlusBo bo) {
        GenTablePlus update = MapstructUtils.convert(bo, GenTablePlus.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GenTablePlus entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除代码生成Plus信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 生成代码文件
     * 根据数据库表结构和配置信息，生成对应的Java代码文件
     * 包括主表和子表的代码生成配置
     *
     * @param id 数据库表的ID，用于获取表结构信息
     */
    @Override
    public void generatorCode(Long id) {
        try {
            GenTablePlusVo plusVo = baseMapper.selectVoById(id);
            GenTablePlusOptions config = JsonUtils.parseObject(plusVo.getOptions(), GenTablePlusOptions.class);
            if(config == null) {
                throw new RuntimeException("配置为空");
            }
            GenTablePlusExt main = config.getMain();
            if(main == null) {
                throw new RuntimeException("主表配置为空");
            }
            if(config.getSubs() == null || config.getSubs().isEmpty()) {
                throw new RuntimeException("子表配置为空");
            }
            getGenTable(main);

            List<GenTableColumn> mainColumns = genTableService.selectGenTableColumnListByTableId(main.getTableId());
            if (!main.getJoinColumns().isEmpty() && !main.getJoinColumns().contains("*")) {
                List<GenTableColumn> addColumns = new ArrayList<>();
                for (String column : main.getJoinColumns()) {
                    for (GenTableColumn genTableColumn : mainColumns) {
                        if (genTableColumn.getColumnName().equals(column)) {
                            addColumns.add(genTableColumn);
                        }
                    }
                }
                main.setColumns(addColumns);
            } else {
                main.setColumns(mainColumns);
            }
            setPkColumn(main);
            //main.setGenPath("E:\\src"); //测试使用
            config.setMain(main);

            List<GenTablePlusExt> subs = new ArrayList<>();
            for (GenTablePlusExt sub : config.getSubs()) {
                getGenTable(sub);
                List<GenTableColumn> subColumns = genTableService.selectGenTableColumnListByTableId(sub.getTableId());
                if (!sub.getJoinColumns().isEmpty() && !sub.getJoinColumns().contains("*")) {
                    List<GenTableColumn> addColumns = new ArrayList<>();
                    for (String column : sub.getJoinColumns()) {
                        for (GenTableColumn genTableColumn : subColumns) {
                            if (genTableColumn.getColumnName().equals(column)) {
                                addColumns.add(genTableColumn);
                            }
                        }
                    }
                    sub.setColumns(addColumns);
                } else {
                    sub.setColumns(subColumns);
                }
                setPkColumn(sub);
                subs.add(sub);
            }
            config.setSubs(subs);

            GeneratorPlus generatorPlus = new GeneratorPlus(config);
            generatorPlus.generate();
        } catch (Exception e) {
            log.error("生成代码失败，配置错误", e);
            throw new RuntimeException("生成代码失败，配置错误");
        }
    }

    private void getGenTable(GenTablePlusExt ext) {
        if(ext.getTableId() == null && StringUtils.isEmpty(ext.getTableName())) {
            throw new RuntimeException("表ID和表名不能同时为空");
        }
        if (ext.getTableId() != null) {
            GenTable genTable = genTableService.selectGenTableById(ext.getTableId());
            BeanUtil.copyProperties(genTable, ext);
        }
        if (StringUtils.isNotEmpty(ext. getTableName())) {
            GenTable genTable = genTableMapper.selectGenTableByName(ext.getTableName());
            if (genTable == null) {
                throw new RuntimeException("表名不存在");
            }
            BeanUtil.copyProperties(genTable, ext);
        }
    }

    /**
     * 设置主键列信息
     *
     * @param table 业务表信息
     */
    public void setPkColumn(GenTable table) {
        if(table.getColumns().isEmpty()) {
            throw new RuntimeException(table.getTableName() + "表字段获取失败");
        }

        for (GenTableColumn column : table.getColumns()) {
            if (column.isPk()) {
                table.setPkColumn(column);
                break;
            }
        }

        if (ObjectUtil.isNull(table.getPkColumn())) {
            table.setPkColumn(table.getColumns().getFirst());
        }
    }
}
