    <resultMap id="BaseResultMap" type="${config.main.packageName}.domain.${config.main.className}">
#foreach($column in $config.main.columns)
#if($column.isPrimaryKey)
        <id column="${column.columnName}" property="${column.javaField}"/>
#else
        <result column="${column.columnName}" property="${column.javaField}"/>
#end
#end
    </resultMap>

    <sql id="Base_Column_List">
    #foreach($column in $config.main.columns)
        #if($foreach.index > 0),#end${column.columnName}
    #end
    </sql>

#if(!$config.subs.isEmpty())
    <resultMap id="${config.main.className}ResultMap" type="${config.main.packageName}.domain.${config.main.className}" extends="BaseResultMap">
#foreach($sub in $config.subs)
#if($sub.type == "ONE_TO_ONE")
        <association property="${sub.attrName}" javaType="${sub.packageName}.domain.${sub.className}">
#foreach($subColumn in $sub.columns)
#if($subColumn.isPk == 1)
            <id column="sub_${sub.attrName}_${subColumn.columnName}" property="${subColumn.javaField}"/>
#else
            <result column="sub_${sub.attrName}_${subColumn.columnName}" property="${subColumn.javaField}"/>
#end
#end
        </association>
#elseif($sub.type == "ONE_TO_MANY")
        <collection property="${sub.attrName}" ofType="${sub.packageName}.domain.${sub.className}">
#foreach($subColumn in $sub.columns)
#if($subColumn.isPk == 1)
            <id column="sub_${sub.attrName}_${subColumn.columnName}" property="${subColumn.javaField}"/>
#else
            <result column="sub_${sub.attrName}_${subColumn.columnName}" property="${subColumn.javaField}"/>
#end
#end
        </collection>
#end
#end
    </resultMap>


    <select id="queryByIdWith" resultMap="${config.main.className}ResultMap">
        SELECT
        ${config.main.attrName}.*,
        #foreach($sub in $config.subs)
            #set($subLast = $foreach.last)
            #foreach($subColumn in $sub.columns)
                ${sub.attrName}.${subColumn.columnName} AS sub_${sub.attrName}_${subColumn.columnName}#if($foreach.hasNext || !$subLast),#end
            #end
        #end
        FROM
        ${config.main.tableName} ${config.main.attrName}
        #foreach($sub in $config.subs)
        LEFT JOIN ${sub.tableName} ${sub.attrName} ON ${config.main.attrName}.${sub.mainJoin} = ${sub.attrName}.${sub.subJoin}
        AND ${sub.attrName}.del_flag = '0'
        #end
        WHERE ${config.main.attrName}.${config.main.pkColumn.columnName} = #{${config.main.pkColumn.javaField}}
    </select>

    <select id="queryPageListWith" resultMap="${config.main.className}ResultMap">
        SELECT
        ${config.main.attrName}.*,
        #foreach($sub in $config.subs)
            #set($subLast = $foreach.last)
            #foreach($subColumn in $sub.columns)
                ${sub.attrName}.${subColumn.columnName} AS sub_${sub.attrName}_${subColumn.columnName}#if($foreach.hasNext || !$subLast),#end
            #end
        #end
        FROM
        ${config.main.tableName} ${config.main.attrName}
        #foreach($sub in $config.subs)
        LEFT JOIN ${sub.tableName} ${sub.attrName} ON ${config.main.attrName}.${sub.mainJoin} = ${sub.attrName}.${sub.subJoin}
        AND ${sub.attrName}.del_flag = '0'
        #end
        <if test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
            ${ew.customSqlSegment}
        </if>
    </select>
#end
